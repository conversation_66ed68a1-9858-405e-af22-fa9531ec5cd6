from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from ems.models import Role, UserProfile, Employee, Department
from django.db import transaction
from datetime import date


class Command(BaseCommand):
    help = 'Create superadmin role and user'

    def add_arguments(self, parser):
        parser.add_argument('--username', type=str, help='Username for superadmin', default='superadmin')
        parser.add_argument('--email', type=str, help='Email for superadmin', default='<EMAIL>')
        parser.add_argument('--password', type=str, help='Password for superadmin', default='superadmin123')
        parser.add_argument('--first-name', type=str, help='First name', default='Super')
        parser.add_argument('--last-name', type=str, help='Last name', default='Administrator')

    def handle(self, *args, **options):
        username = options['username']
        email = options['email']
        password = options['password']
        first_name = options['first_name']
        last_name = options['last_name']

        try:
            with transaction.atomic():
                # Create or get the SUPERADMIN role
                superadmin_role, created = Role.objects.get_or_create(
                    name='SUPERADMIN',
                    defaults={
                        'name_ar': 'مدير النظام الأعلى',
                        'description': 'Super Administrator with full system access',
                        'description_ar': 'مدير النظام الأعلى مع صلاحيات كاملة للنظام',
                        'permissions': {
                            'all_access': True,
                            'system_admin': True,
                            'user_management': True,
                            'role_management': True,
                            'security_management': True,
                            'audit_access': True,
                            'compliance_management': True,
                            'ai_management': True,
                            'advanced_analytics': True,
                            'enterprise_features': True,
                            'backup_management': True,
                            'system_configuration': True
                        }
                    }
                )

                if created:
                    self.stdout.write(
                        self.style.SUCCESS(f'Successfully created SUPERADMIN role')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'SUPERADMIN role already exists')
                    )

                # Check if user already exists
                if User.objects.filter(username=username).exists():
                    self.stdout.write(
                        self.style.WARNING(f'User {username} already exists')
                    )
                    return

                # Create the superadmin user
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    password=password,
                    first_name=first_name,
                    last_name=last_name,
                    is_staff=True,
                    is_superuser=True
                )

                # Create user profile
                user_profile = UserProfile.objects.create(
                    user=user,
                    role=superadmin_role,
                    preferred_language='ar',
                    timezone='Asia/Riyadh'
                )

                # Get or create IT department for superadmin
                it_department, dept_created = Department.objects.get_or_create(
                    name='IT Department',
                    defaults={
                        'name_ar': 'قسم تقنية المعلومات',
                        'description': 'Information Technology Department',
                        'description_ar': 'قسم تقنية المعلومات',
                        'is_active': True
                    }
                )

                # Create employee record
                employee = Employee.objects.create(
                    user=user,
                    employee_id='SA001',
                    department=it_department,
                    position='Super Administrator',
                    position_ar='مدير النظام الأعلى',
                    phone='+966500000000',
                    gender='M',
                    hire_date=date.today(),
                    salary=50000.00,
                    employment_status='FULL_TIME',
                    work_location='Head Office',
                    is_active=True
                )

                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully created superadmin user: {username}\n'
                        f'Email: {email}\n'
                        f'Password: {password}\n'
                        f'Employee ID: SA001\n'
                        f'Role: SUPERADMIN\n'
                        f'Department: {it_department.name}'
                    )
                )

                # Update other admin roles to have less permissions
                admin_role = Role.objects.filter(name='ADMIN').first()
                if admin_role:
                    admin_role.permissions = {
                        'user_management': True,
                        'department_management': True,
                        'employee_management': True,
                        'project_management': True,
                        'financial_management': True,
                        'inventory_management': True,
                        'reports_access': True,
                        'settings_access': True
                    }
                    admin_role.save()
                    self.stdout.write(
                        self.style.SUCCESS('Updated ADMIN role permissions')
                    )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating superadmin: {str(e)}')
            )
