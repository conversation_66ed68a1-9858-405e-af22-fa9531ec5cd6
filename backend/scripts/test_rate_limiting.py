#!/usr/bin/env python3
"""
CRITICAL FIX: Rate Limiting Test Script
Validates rate limiting functionality
"""

import requests
import time
import json
from concurrent.futures import ThreadPoolExecutor, as_completed

class RateLimitTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_login_rate_limit(self):
        """Test login endpoint rate limiting"""
        print("🔐 Testing login rate limiting...")
        
        login_url = f"{self.base_url}/api/auth/login/"
        
        # Attempt multiple failed logins
        results = []
        for i in range(10):
            response = self.session.post(login_url, json={
                "username": "nonexistent",
                "password": "wrongpassword"
            })
            
            results.append({
                "attempt": i + 1,
                "status": response.status_code,
                "response_time": response.elapsed.total_seconds()
            })
            
            print(f"  Attempt {i+1}: Status {response.status_code}")
            
            # Check if rate limited
            if response.status_code == 429:
                print(f"  ✅ Rate limiting triggered at attempt {i+1}")
                break
            
            time.sleep(0.1)  # Small delay between requests
        
        return results
    
    def test_api_rate_limit(self):
        """Test general API rate limiting"""
        print("🌐 Testing API rate limiting...")
        
        api_url = f"{self.base_url}/api/employees/"
        
        # Make rapid API requests
        results = []
        for i in range(50):
            response = self.session.get(api_url)
            
            results.append({
                "attempt": i + 1,
                "status": response.status_code,
                "response_time": response.elapsed.total_seconds()
            })
            
            if i % 10 == 0:
                print(f"  Request {i+1}: Status {response.status_code}")
            
            # Check if rate limited
            if response.status_code == 429:
                print(f"  ✅ API rate limiting triggered at request {i+1}")
                break
        
        return results
    
    def test_concurrent_requests(self):
        """Test rate limiting under concurrent load"""
        print("⚡ Testing concurrent request rate limiting...")
        
        api_url = f"{self.base_url}/api/dashboard-stats/"
        
        def make_request(request_id):
            try:
                response = requests.get(api_url, timeout=10)
                return {
                    "request_id": request_id,
                    "status": response.status_code,
                    "response_time": response.elapsed.total_seconds()
                }
            except Exception as e:
                return {
                    "request_id": request_id,
                    "status": "error",
                    "error": str(e)
                }
        
        # Launch 20 concurrent requests
        results = []
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(make_request, i) for i in range(20)]
            
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
                
                if result["status"] == 429:
                    print(f"  ✅ Concurrent rate limiting triggered")
        
        # Analyze results
        status_counts = {}
        for result in results:
            status = result["status"]
            status_counts[status] = status_counts.get(status, 0) + 1
        
        print(f"  📊 Results: {status_counts}")
        return results
    
    def test_rate_limit_recovery(self):
        """Test rate limit recovery after cooldown"""
        print("🔄 Testing rate limit recovery...")
        
        login_url = f"{self.base_url}/api/auth/login/"
        
        # Trigger rate limit
        for i in range(6):
            response = self.session.post(login_url, json={
                "username": "test",
                "password": "wrong"
            })
            if response.status_code == 429:
                print(f"  ✅ Rate limit triggered")
                break
        
        # Wait for cooldown
        print(f"  ⏳ Waiting 65 seconds for rate limit cooldown...")
        time.sleep(65)
        
        # Test recovery
        response = self.session.post(login_url, json={
            "username": "test",
            "password": "wrong"
        })
        
        if response.status_code != 429:
            print(f"  ✅ Rate limit recovered (Status: {response.status_code})")
            return True
        else:
            print(f"  ❌ Rate limit not recovered")
            return False
    
    def run_all_tests(self):
        """Run comprehensive rate limiting tests"""
        print("🚀 Starting Rate Limiting Tests...")
        print("=" * 50)
        
        try:
            # Test 1: Login rate limiting
            login_results = self.test_login_rate_limit()
            
            # Test 2: API rate limiting
            api_results = self.test_api_rate_limit()
            
            # Test 3: Concurrent requests
            concurrent_results = self.test_concurrent_requests()
            
            # Test 4: Rate limit recovery (optional - takes time)
            # recovery_success = self.test_rate_limit_recovery()
            
            print("\n" + "=" * 50)
            print("📋 RATE LIMITING TEST SUMMARY")
            print("=" * 50)
            
            # Analyze login results
            login_rate_limited = any(r["status"] == 429 for r in login_results)
            print(f"🔐 Login rate limiting: {'✅ WORKING' if login_rate_limited else '❌ NOT WORKING'}")
            
            # Analyze API results
            api_rate_limited = any(r["status"] == 429 for r in api_results)
            print(f"🌐 API rate limiting: {'✅ WORKING' if api_rate_limited else '❌ NOT WORKING'}")
            
            # Analyze concurrent results
            concurrent_rate_limited = any(r["status"] == 429 for r in concurrent_results)
            print(f"⚡ Concurrent rate limiting: {'✅ WORKING' if concurrent_rate_limited else '❌ NOT WORKING'}")
            
            # Overall assessment
            all_working = login_rate_limited and api_rate_limited
            print(f"\n🎯 Overall rate limiting: {'✅ FUNCTIONAL' if all_working else '❌ NEEDS ATTENTION'}")
            
            return all_working
            
        except Exception as e:
            print(f"💥 Rate limiting test failed: {e}")
            return False

if __name__ == '__main__':
    import sys
    
    # Allow custom base URL
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8000"
    
    tester = RateLimitTester(base_url)
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)
