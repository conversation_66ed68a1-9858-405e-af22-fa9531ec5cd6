from django.shortcuts import render
from django.db.models import Count, Avg, Q
from django.utils import timezone
from datetime import timedelta
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .models import (
    SupportTicketCategory, SupportAgent, ServiceLevelAgreement,
    SupportTicket, TicketComment, KnowledgeBaseCategory,
    KnowledgeBaseArticle, CustomerFeedback, LiveChatSession, ChatMessage
)
from .serializers import (
    SupportTicketCategorySerializer, SupportAgentSerializer, ServiceLevelAgreementSerializer,
    SupportTicketSerializer, SupportTicketCreateSerializer, TicketCommentSerializer,
    KnowledgeBaseCategorySerializer, KnowledgeBaseArticleSerializer,
    CustomerFeedbackSerializer, LiveChatSessionSerializer, ChatMessageSerializer,
    SupportDashboardSerializer, TicketTrendSerializer, AgentPerformanceSerializer
)

class SupportTicketCategoryViewSet(viewsets.ModelViewSet):
    queryset = SupportTicketCategory.objects.all()
    serializer_class = SupportTicketCategorySerializer
    permission_classes = [IsAuthenticated]

class SupportAgentViewSet(viewsets.ModelViewSet):
    queryset = SupportAgent.objects.all()
    serializer_class = SupportAgentSerializer
    permission_classes = [IsAuthenticated]

class ServiceLevelAgreementViewSet(viewsets.ModelViewSet):
    queryset = ServiceLevelAgreement.objects.all()
    serializer_class = ServiceLevelAgreementSerializer
    permission_classes = [IsAuthenticated]

class SupportTicketViewSet(viewsets.ModelViewSet):
    queryset = SupportTicket.objects.all()
    serializer_class = SupportTicketSerializer
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'create':
            return SupportTicketCreateSerializer
        return SupportTicketSerializer

    def perform_create(self, serializer):
        serializer.save(customer=self.request.user)

    @action(detail=True, methods=['post'])
    def add_comment(self, request, pk=None):
        ticket = self.get_object()
        comment_data = {
            'ticket': ticket.id,
            'author': request.user.id,
            'comment_type': request.data.get('comment_type', 'customer'),
            'content': request.data.get('content'),
            'is_public': request.data.get('is_public', True)
        }
        serializer = TicketCommentSerializer(data=comment_data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def assign_agent(self, request, pk=None):
        ticket = self.get_object()
        agent_id = request.data.get('agent_id')
        try:
            agent = SupportAgent.objects.get(id=agent_id)
            ticket.assigned_agent = agent
            ticket.save()
            return Response({'message': 'Agent assigned successfully'})
        except SupportAgent.DoesNotExist:
            return Response({'error': 'Agent not found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        ticket = self.get_object()
        new_status = request.data.get('status')
        if new_status in dict(SupportTicket.STATUS_CHOICES):
            ticket.status = new_status
            if new_status == 'resolved':
                ticket.resolved_at = timezone.now()
            elif new_status == 'closed':
                ticket.closed_at = timezone.now()
            ticket.save()
            return Response({'message': 'Status updated successfully'})
        return Response({'error': 'Invalid status'}, status=status.HTTP_400_BAD_REQUEST)

class TicketCommentViewSet(viewsets.ModelViewSet):
    queryset = TicketComment.objects.all()
    serializer_class = TicketCommentSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(author=self.request.user)

class KnowledgeBaseCategoryViewSet(viewsets.ModelViewSet):
    queryset = KnowledgeBaseCategory.objects.all()
    serializer_class = KnowledgeBaseCategorySerializer
    permission_classes = [IsAuthenticated]

class KnowledgeBaseArticleViewSet(viewsets.ModelViewSet):
    queryset = KnowledgeBaseArticle.objects.filter(is_published=True)
    serializer_class = KnowledgeBaseArticleSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(author=self.request.user)

    @action(detail=True, methods=['post'])
    def vote_helpful(self, request, pk=None):
        article = self.get_object()
        vote_type = request.data.get('vote_type')  # 'helpful' or 'not_helpful'

        if vote_type == 'helpful':
            article.helpful_votes += 1
        elif vote_type == 'not_helpful':
            article.not_helpful_votes += 1
        else:
            return Response({'error': 'Invalid vote type'}, status=status.HTTP_400_BAD_REQUEST)

        article.save()
        return Response({'message': 'Vote recorded successfully'})

    @action(detail=True, methods=['post'])
    def increment_view(self, request, pk=None):
        article = self.get_object()
        article.view_count += 1
        article.save()
        return Response({'message': 'View count updated'})

class CustomerFeedbackViewSet(viewsets.ModelViewSet):
    queryset = CustomerFeedback.objects.all()
    serializer_class = CustomerFeedbackSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(customer=self.request.user)

class LiveChatSessionViewSet(viewsets.ModelViewSet):
    queryset = LiveChatSession.objects.all()
    serializer_class = LiveChatSessionSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(customer=self.request.user)

    @action(detail=True, methods=['post'])
    def send_message(self, request, pk=None):
        session = self.get_object()
        message_data = {
            'session': session.id,
            'sender': request.user.id,
            'message_type': request.data.get('message_type', 'customer'),
            'content': request.data.get('content')
        }
        serializer = ChatMessageSerializer(data=message_data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ChatMessageViewSet(viewsets.ModelViewSet):
    queryset = ChatMessage.objects.all()
    serializer_class = ChatMessageSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(sender=self.request.user)

# Dashboard Analytics Views
class SupportDashboardViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """Get support dashboard statistics"""
        # Calculate date ranges
        today = timezone.now().date()
        last_30_days = today - timedelta(days=30)

        # Basic ticket statistics
        total_tickets = SupportTicket.objects.count()
        open_tickets = SupportTicket.objects.filter(status='open').count()
        in_progress_tickets = SupportTicket.objects.filter(status='in_progress').count()
        resolved_tickets = SupportTicket.objects.filter(status='resolved').count()

        # Calculate average resolution time
        resolved_tickets_with_time = SupportTicket.objects.filter(
            status='resolved',
            resolved_at__isnull=False,
            created_at__isnull=False
        )

        avg_resolution_time = "N/A"
        if resolved_tickets_with_time.exists():
            total_time = sum([
                (ticket.resolved_at - ticket.created_at).total_seconds()
                for ticket in resolved_tickets_with_time
            ])
            avg_seconds = total_time / resolved_tickets_with_time.count()
            avg_hours = avg_seconds / 3600
            avg_resolution_time = f"{avg_hours:.1f} hours"

        # Customer satisfaction
        feedback_ratings = CustomerFeedback.objects.filter(
            created_at__gte=last_30_days
        ).aggregate(avg_rating=Avg('overall_rating'))
        customer_satisfaction = feedback_ratings['avg_rating'] or 0.0

        # SLA compliance (simplified calculation)
        sla_compliant_tickets = SupportTicket.objects.filter(
            is_sla_breached=False,
            created_at__gte=last_30_days
        ).count()
        total_recent_tickets = SupportTicket.objects.filter(
            created_at__gte=last_30_days
        ).count()
        sla_compliance = (sla_compliant_tickets / total_recent_tickets * 100) if total_recent_tickets > 0 else 100.0

        # Active chat sessions
        active_chat_sessions = LiveChatSession.objects.filter(status='active').count()

        data = {
            'total_tickets': total_tickets,
            'open_tickets': open_tickets,
            'in_progress_tickets': in_progress_tickets,
            'resolved_tickets': resolved_tickets,
            'avg_resolution_time': avg_resolution_time,
            'customer_satisfaction': round(customer_satisfaction, 1),
            'sla_compliance': round(sla_compliance, 1),
            'active_chat_sessions': active_chat_sessions,
        }

        serializer = SupportDashboardSerializer(data)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def ticket_trends(self, request):
        """Get ticket creation and resolution trends"""
        days = int(request.query_params.get('days', 30))
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)

        trends = []
        current_date = start_date

        while current_date <= end_date:
            created_count = SupportTicket.objects.filter(
                created_at__date=current_date
            ).count()

            resolved_count = SupportTicket.objects.filter(
                resolved_at__date=current_date
            ).count()

            trends.append({
                'date': current_date,
                'created': created_count,
                'resolved': resolved_count
            })

            current_date += timedelta(days=1)

        serializer = TicketTrendSerializer(trends, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def agent_performance(self, request):
        """Get agent performance metrics"""
        agents = SupportAgent.objects.filter(is_available=True)
        performance_data = []

        for agent in agents:
            tickets_resolved = SupportTicket.objects.filter(
                assigned_agent=agent,
                status='resolved'
            ).count()

            # Calculate average resolution time for this agent
            agent_resolved_tickets = SupportTicket.objects.filter(
                assigned_agent=agent,
                status='resolved',
                resolved_at__isnull=False,
                created_at__isnull=False
            )

            avg_resolution_time = "N/A"
            if agent_resolved_tickets.exists():
                total_time = sum([
                    (ticket.resolved_at - ticket.created_at).total_seconds()
                    for ticket in agent_resolved_tickets
                ])
                avg_seconds = total_time / agent_resolved_tickets.count()
                avg_hours = avg_seconds / 3600
                avg_resolution_time = f"{avg_hours:.1f} hours"

            # Get customer ratings for this agent's tickets
            agent_feedback = CustomerFeedback.objects.filter(
                related_ticket__assigned_agent=agent
            ).aggregate(avg_rating=Avg('overall_rating'))
            customer_rating = agent_feedback['avg_rating'] or 0.0

            performance_data.append({
                'agent_name': agent.user.get_full_name() or agent.user.username,
                'tickets_resolved': tickets_resolved,
                'avg_resolution_time': avg_resolution_time,
                'customer_rating': round(customer_rating, 1)
            })

        serializer = AgentPerformanceSerializer(performance_data, many=True)
        return Response(serializer.data)
