#!/usr/bin/env python3
"""
CRITICAL FIX: Advanced Database Migration Analysis
Comprehensive analysis for zero-downtime PostgreSQL migration
"""

import os
import sys
import sqlite3
import json
import time
from pathlib import Path
from datetime import datetime, timedelta

# Add Django to path
sys.path.append(str(Path(__file__).parent.parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

import django
django.setup()

from django.db import connection
from django.apps import apps
from django.core.management import call_command

class AdvancedMigrationAnalyzer:
    def __init__(self):
        self.analysis_results = {
            "database_size": {},
            "table_analysis": {},
            "foreign_key_analysis": {},
            "index_analysis": {},
            "data_quality": {},
            "migration_plan": {},
            "risk_assessment": {}
        }
        
    def run_comprehensive_analysis(self):
        """Run comprehensive pre-migration analysis"""
        print("🔍 Advanced Database Migration Analysis")
        print("=" * 60)
        
        self.analyze_database_size()
        self.analyze_table_structures()
        self.analyze_foreign_keys()
        self.analyze_existing_indexes()
        self.analyze_data_quality()
        self.create_migration_plan()
        self.assess_migration_risks()
        
        self.save_analysis_report()
        self.print_executive_summary()
        
        return self.analysis_results
    
    def analyze_database_size(self):
        """Analyze database size and estimate migration time"""
        print("📊 Analyzing database size...")
        
        try:
            db_path = Path('db.sqlite3')
            if not db_path.exists():
                raise FileNotFoundError("SQLite database not found")
            
            # Get file size
            file_size = db_path.stat().st_size
            file_size_mb = file_size / (1024 * 1024)
            
            # Analyze table sizes
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # Get table sizes
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """)
            
            tables = cursor.fetchall()
            table_sizes = {}
            
            for (table_name,) in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                row_count = cursor.fetchone()[0]
                
                # Estimate size (rough calculation)
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                estimated_row_size = len(columns) * 50  # Rough estimate
                estimated_size_mb = (row_count * estimated_row_size) / (1024 * 1024)
                
                table_sizes[table_name] = {
                    "row_count": row_count,
                    "estimated_size_mb": estimated_size_mb
                }
            
            conn.close()
            
            # Calculate migration time estimate
            # Rough estimate: 1000 rows per second
            total_rows = sum(table["row_count"] for table in table_sizes.values())
            estimated_migration_seconds = total_rows / 1000
            
            self.analysis_results["database_size"] = {
                "file_size_mb": file_size_mb,
                "total_rows": total_rows,
                "table_count": len(tables),
                "table_sizes": table_sizes,
                "estimated_migration_time_seconds": estimated_migration_seconds,
                "estimated_migration_time_minutes": estimated_migration_seconds / 60
            }
            
            print(f"  📈 Database size: {file_size_mb:.2f} MB")
            print(f"  📊 Total rows: {total_rows:,}")
            print(f"  ⏱️ Estimated migration time: {estimated_migration_seconds/60:.1f} minutes")
            
        except Exception as e:
            print(f"  ❌ Database size analysis failed: {e}")
            self.analysis_results["database_size"]["error"] = str(e)
    
    def analyze_table_structures(self):
        """Analyze table structures for compatibility"""
        print("🏗️ Analyzing table structures...")
        
        try:
            models = apps.get_models()
            table_analysis = {}
            
            for model in models:
                table_name = model._meta.db_table
                fields_info = {}
                
                for field in model._meta.fields:
                    field_info = {
                        "type": field.__class__.__name__,
                        "null": field.null,
                        "blank": field.blank,
                        "unique": field.unique,
                        "db_index": field.db_index
                    }
                    
                    # Check for potential issues
                    issues = []
                    if hasattr(field, 'max_length') and field.max_length:
                        field_info["max_length"] = field.max_length
                        if field.max_length > 255:
                            issues.append("Long text field - may need TEXT type in PostgreSQL")
                    
                    if field.__class__.__name__ == 'JSONField':
                        issues.append("JSONField - ensure PostgreSQL compatibility")
                    
                    field_info["potential_issues"] = issues
                    fields_info[field.name] = field_info
                
                table_analysis[table_name] = {
                    "model": model.__name__,
                    "fields": fields_info,
                    "field_count": len(fields_info)
                }
            
            self.analysis_results["table_analysis"] = table_analysis
            print(f"  📋 Analyzed {len(table_analysis)} tables")
            
            # Check for potential issues
            issues_found = 0
            for table, info in table_analysis.items():
                for field, field_info in info["fields"].items():
                    issues_found += len(field_info["potential_issues"])
            
            if issues_found > 0:
                print(f"  ⚠️ Found {issues_found} potential compatibility issues")
            else:
                print(f"  ✅ No compatibility issues found")
                
        except Exception as e:
            print(f"  ❌ Table structure analysis failed: {e}")
            self.analysis_results["table_analysis"]["error"] = str(e)
    
    def analyze_foreign_keys(self):
        """Analyze foreign key relationships"""
        print("🔗 Analyzing foreign key relationships...")
        
        try:
            models = apps.get_models()
            fk_analysis = {
                "relationships": {},
                "circular_dependencies": [],
                "orphaned_records": {}
            }
            
            for model in models:
                model_fks = []
                
                for field in model._meta.fields:
                    if hasattr(field, 'related_model') and field.related_model:
                        fk_info = {
                            "field_name": field.name,
                            "related_model": field.related_model.__name__,
                            "on_delete": getattr(field, 'on_delete', None).__name__ if hasattr(field, 'on_delete') else 'CASCADE'
                        }
                        model_fks.append(fk_info)
                
                if model_fks:
                    fk_analysis["relationships"][model.__name__] = model_fks
            
            # Check for potential orphaned records
            for model in models:
                try:
                    for field in model._meta.fields:
                        if hasattr(field, 'related_model') and field.related_model:
                            # Check for orphaned records (basic check)
                            orphaned_count = model.objects.filter(**{f"{field.name}__isnull": True}).count()
                            if orphaned_count > 0:
                                fk_analysis["orphaned_records"][f"{model.__name__}.{field.name}"] = orphaned_count
                except Exception:
                    # Skip if there are issues with the query
                    pass
            
            self.analysis_results["foreign_key_analysis"] = fk_analysis
            
            total_relationships = sum(len(fks) for fks in fk_analysis["relationships"].values())
            orphaned_count = len(fk_analysis["orphaned_records"])
            
            print(f"  🔗 Found {total_relationships} foreign key relationships")
            if orphaned_count > 0:
                print(f"  ⚠️ Found {orphaned_count} potential orphaned record issues")
            else:
                print(f"  ✅ No orphaned records detected")
                
        except Exception as e:
            print(f"  ❌ Foreign key analysis failed: {e}")
            self.analysis_results["foreign_key_analysis"]["error"] = str(e)
    
    def analyze_existing_indexes(self):
        """Analyze existing indexes"""
        print("📇 Analyzing existing indexes...")
        
        try:
            conn = sqlite3.connect('db.sqlite3')
            cursor = conn.cursor()
            
            # Get all indexes
            cursor.execute("""
                SELECT name, tbl_name, sql 
                FROM sqlite_master 
                WHERE type='index' AND name NOT LIKE 'sqlite_%'
            """)
            
            indexes = cursor.fetchall()
            index_analysis = {
                "existing_indexes": [],
                "missing_indexes": [],
                "recommendations": []
            }
            
            for name, table, sql in indexes:
                index_info = {
                    "name": name,
                    "table": table,
                    "sql": sql
                }
                index_analysis["existing_indexes"].append(index_info)
            
            conn.close()
            
            # Analyze Django models for missing indexes
            models = apps.get_models()
            for model in models:
                for field in model._meta.fields:
                    if hasattr(field, 'related_model') and field.related_model:
                        # Foreign keys should have indexes
                        expected_index = f"{model._meta.db_table}_{field.name}_id"
                        if not any(idx["name"] == expected_index for idx in index_analysis["existing_indexes"]):
                            index_analysis["missing_indexes"].append({
                                "table": model._meta.db_table,
                                "field": field.name,
                                "type": "foreign_key"
                            })
            
            self.analysis_results["index_analysis"] = index_analysis
            
            existing_count = len(index_analysis["existing_indexes"])
            missing_count = len(index_analysis["missing_indexes"])
            
            print(f"  📇 Found {existing_count} existing indexes")
            if missing_count > 0:
                print(f"  ⚠️ Recommended {missing_count} additional indexes")
            else:
                print(f"  ✅ Index coverage looks good")
                
        except Exception as e:
            print(f"  ❌ Index analysis failed: {e}")
            self.analysis_results["index_analysis"]["error"] = str(e)
    
    def analyze_data_quality(self):
        """Analyze data quality issues"""
        print("🔍 Analyzing data quality...")
        
        try:
            models = apps.get_models()
            data_quality = {
                "null_checks": {},
                "duplicate_checks": {},
                "constraint_violations": {}
            }
            
            for model in models:
                try:
                    model_name = model.__name__
                    
                    # Check for null values in non-nullable fields
                    null_issues = {}
                    for field in model._meta.fields:
                        if not field.null and field.name != 'id':
                            try:
                                null_count = model.objects.filter(**{f"{field.name}__isnull": True}).count()
                                if null_count > 0:
                                    null_issues[field.name] = null_count
                            except Exception:
                                pass
                    
                    if null_issues:
                        data_quality["null_checks"][model_name] = null_issues
                    
                    # Check for duplicates in unique fields
                    duplicate_issues = {}
                    for field in model._meta.fields:
                        if field.unique and field.name != 'id':
                            try:
                                # This is a simplified check
                                total_count = model.objects.count()
                                distinct_count = model.objects.values(field.name).distinct().count()
                                if total_count != distinct_count:
                                    duplicate_issues[field.name] = total_count - distinct_count
                            except Exception:
                                pass
                    
                    if duplicate_issues:
                        data_quality["duplicate_checks"][model_name] = duplicate_issues
                        
                except Exception:
                    # Skip models that can't be analyzed
                    pass
            
            self.analysis_results["data_quality"] = data_quality
            
            null_issues = sum(len(issues) for issues in data_quality["null_checks"].values())
            duplicate_issues = sum(len(issues) for issues in data_quality["duplicate_checks"].values())
            
            print(f"  🔍 Data quality analysis complete")
            if null_issues > 0:
                print(f"  ⚠️ Found {null_issues} null value issues")
            if duplicate_issues > 0:
                print(f"  ⚠️ Found {duplicate_issues} duplicate value issues")
            if null_issues == 0 and duplicate_issues == 0:
                print(f"  ✅ No data quality issues found")
                
        except Exception as e:
            print(f"  ❌ Data quality analysis failed: {e}")
            self.analysis_results["data_quality"]["error"] = str(e)
    
    def create_migration_plan(self):
        """Create detailed migration plan"""
        print("📋 Creating migration plan...")
        
        try:
            db_size = self.analysis_results.get("database_size", {})
            total_rows = db_size.get("total_rows", 0)
            
            # Determine migration strategy based on size
            if total_rows < 10000:
                strategy = "simple_migration"
                downtime_estimate = "< 5 minutes"
            elif total_rows < 100000:
                strategy = "chunked_migration"
                downtime_estimate = "5-15 minutes"
            else:
                strategy = "zero_downtime_migration"
                downtime_estimate = "< 2 minutes (with proper planning)"
            
            migration_plan = {
                "strategy": strategy,
                "estimated_downtime": downtime_estimate,
                "steps": self.get_migration_steps(strategy),
                "rollback_plan": self.get_rollback_plan(),
                "validation_steps": self.get_validation_steps()
            }
            
            self.analysis_results["migration_plan"] = migration_plan
            print(f"  📋 Migration strategy: {strategy}")
            print(f"  ⏱️ Estimated downtime: {downtime_estimate}")
            
        except Exception as e:
            print(f"  ❌ Migration planning failed: {e}")
            self.analysis_results["migration_plan"]["error"] = str(e)
    
    def get_migration_steps(self, strategy):
        """Get migration steps based on strategy"""
        
        if strategy == "simple_migration":
            return [
                "1. Create PostgreSQL database",
                "2. Run Django migrations",
                "3. Export SQLite data",
                "4. Import data to PostgreSQL",
                "5. Validate data integrity",
                "6. Switch application to PostgreSQL"
            ]
        elif strategy == "chunked_migration":
            return [
                "1. Create PostgreSQL database",
                "2. Run Django migrations",
                "3. Export SQLite data in chunks",
                "4. Import chunks to PostgreSQL with progress tracking",
                "5. Validate each chunk",
                "6. Final validation and switch"
            ]
        else:  # zero_downtime_migration
            return [
                "1. Setup PostgreSQL database",
                "2. Run migrations on PostgreSQL",
                "3. Setup dual-write mode (SQLite + PostgreSQL)",
                "4. Migrate historical data in background",
                "5. Validate data consistency",
                "6. Switch reads to PostgreSQL",
                "7. Stop dual-write mode"
            ]
    
    def get_rollback_plan(self):
        """Get rollback plan"""
        return [
            "1. Stop application",
            "2. Restore SQLite database from backup",
            "3. Revert Django settings to SQLite",
            "4. Restart application",
            "5. Validate application functionality"
        ]
    
    def get_validation_steps(self):
        """Get validation steps"""
        return [
            "1. Compare record counts between databases",
            "2. Validate foreign key relationships",
            "3. Test application functionality",
            "4. Run performance tests",
            "5. Validate data integrity"
        ]
    
    def assess_migration_risks(self):
        """Assess migration risks"""
        print("⚠️ Assessing migration risks...")
        
        risks = []
        risk_level = "LOW"
        
        # Check database size risk
        db_size = self.analysis_results.get("database_size", {})
        total_rows = db_size.get("total_rows", 0)
        
        if total_rows > 1000000:
            risks.append("Large dataset - extended migration time")
            risk_level = "HIGH"
        elif total_rows > 100000:
            risks.append("Medium dataset - moderate migration time")
            risk_level = "MEDIUM"
        
        # Check data quality risks
        data_quality = self.analysis_results.get("data_quality", {})
        if data_quality.get("null_checks") or data_quality.get("duplicate_checks"):
            risks.append("Data quality issues detected")
            risk_level = "MEDIUM" if risk_level == "LOW" else "HIGH"
        
        # Check foreign key risks
        fk_analysis = self.analysis_results.get("foreign_key_analysis", {})
        if fk_analysis.get("orphaned_records"):
            risks.append("Orphaned records detected")
            risk_level = "MEDIUM" if risk_level == "LOW" else "HIGH"
        
        self.analysis_results["risk_assessment"] = {
            "overall_risk": risk_level,
            "identified_risks": risks,
            "mitigation_strategies": self.get_mitigation_strategies(risks)
        }
        
        print(f"  ⚠️ Overall risk level: {risk_level}")
        print(f"  📋 Identified {len(risks)} risk factors")
    
    def get_mitigation_strategies(self, risks):
        """Get mitigation strategies for identified risks"""
        strategies = []
        
        for risk in risks:
            if "Large dataset" in risk:
                strategies.append("Use chunked migration with progress tracking")
            elif "Data quality" in risk:
                strategies.append("Clean data before migration")
            elif "Orphaned records" in risk:
                strategies.append("Fix foreign key relationships before migration")
        
        return strategies
    
    def save_analysis_report(self):
        """Save analysis report to file"""
        report_file = Path('migration_analysis_report.json')
        
        with open(report_file, 'w') as f:
            json.dump(self.analysis_results, f, indent=2, default=str)
        
        print(f"\n📄 Analysis report saved to: {report_file}")
    
    def print_executive_summary(self):
        """Print executive summary"""
        print("\n" + "=" * 60)
        print("📋 MIGRATION ANALYSIS EXECUTIVE SUMMARY")
        print("=" * 60)
        
        db_size = self.analysis_results.get("database_size", {})
        migration_plan = self.analysis_results.get("migration_plan", {})
        risk_assessment = self.analysis_results.get("risk_assessment", {})
        
        print(f"\n📊 Database Overview:")
        print(f"  • Size: {db_size.get('file_size_mb', 0):.2f} MB")
        print(f"  • Total records: {db_size.get('total_rows', 0):,}")
        print(f"  • Tables: {db_size.get('table_count', 0)}")
        
        print(f"\n📋 Migration Plan:")
        print(f"  • Strategy: {migration_plan.get('strategy', 'Unknown')}")
        print(f"  • Estimated downtime: {migration_plan.get('estimated_downtime', 'Unknown')}")
        
        print(f"\n⚠️ Risk Assessment:")
        print(f"  • Risk level: {risk_assessment.get('overall_risk', 'Unknown')}")
        print(f"  • Risk factors: {len(risk_assessment.get('identified_risks', []))}")
        
        print(f"\n✅ Recommendations:")
        if risk_assessment.get('overall_risk') == 'LOW':
            print(f"  • Proceed with migration as planned")
        elif risk_assessment.get('overall_risk') == 'MEDIUM':
            print(f"  • Address data quality issues before migration")
            print(f"  • Plan for extended maintenance window")
        else:
            print(f"  • Resolve all identified risks before migration")
            print(f"  • Consider professional database migration service")

if __name__ == '__main__':
    analyzer = AdvancedMigrationAnalyzer()
    analyzer.run_comprehensive_analysis()
