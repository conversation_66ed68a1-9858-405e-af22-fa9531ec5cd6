"""
Live Chat API Views
Provides CRUD operations for live chat functionality
"""

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.contrib.auth.models import User
from django.utils import timezone
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.db import models
import json

from .models import LiveChatSession, ChatMessage, SupportAgent
from .services_live_chat import LiveChatService
from .serializers import LiveChatSessionSerializer, ChatMessageSerializer

class LiveChatSessionViewSet(viewsets.ModelViewSet):
    """ViewSet for managing live chat sessions"""

    queryset = LiveChatSession.objects.all()
    serializer_class = LiveChatSessionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter sessions based on user role"""
        user = self.request.user

        # Check if user is admin/staff first
        if user.is_staff or user.is_superuser:
            # Admins see all sessions
            return LiveChatSession.objects.all().order_by('-started_at')

        # Check if user is an agent
        try:
            agent = SupportAgent.objects.get(user=user)
            # Agents see their own sessions and waiting sessions
            return LiveChatSession.objects.filter(
                models.Q(agent=user) | models.Q(status='waiting')
            ).order_by('-started_at')
        except SupportAgent.DoesNotExist:
            pass

        # Regular users see only their own sessions
        return LiveChatSession.objects.filter(customer=user).order_by('-started_at')

    @action(detail=False, methods=['post'], permission_classes=[AllowAny])
    def start_session(self, request):
        """Start a new chat session"""
        try:
            data = request.data

            # Create session
            session, customer_created = LiveChatService.create_chat_session(
                customer_data={
                    'customer_name': data.get('customer_name', 'Anonymous'),
                    'customer_email': data.get('customer_email'),
                    'customer_phone': data.get('customer_phone'),
                    'language': data.get('language', 'en'),
                    'session_id': data.get('session_id')
                },
                subject=data.get('subject', 'Live Chat Support')
            )

            # Try to assign an agent immediately
            assigned_session, message = LiveChatService.assign_agent_to_session(session.session_id)

            return Response({
                'session_id': session.session_id,
                'status': session.status,
                'customer_created': customer_created,
                'agent_assigned': assigned_session is not None,
                'message': message,
                'started_at': session.started_at.isoformat()
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                'error': f'Failed to start chat session: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'])
    def send_message(self, request, pk=None):
        """Send a message in a chat session"""
        try:
            session = self.get_object()
            content = request.data.get('content', '').strip()
            message_type = request.data.get('message_type', 'text')

            if not content:
                return Response({
                    'error': 'Message content is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Send message
            message = LiveChatService.send_message(
                session.session_id,
                request.user,
                content,
                message_type
            )

            if message:
                return Response({
                    'message_id': message.id,
                    'content': message.content,
                    'sender': message.sender.username if message.sender else 'System',
                    'message_type': message.message_type,
                    'created_at': message.created_at.isoformat()
                })
            else:
                return Response({
                    'error': 'Failed to send message'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'])
    def messages(self, request, pk=None):
        """Get messages for a chat session"""
        try:
            session = self.get_object()
            limit = int(request.query_params.get('limit', 50))

            messages = LiveChatService.get_session_messages(session.session_id, limit)

            # Mark messages as read for the requesting user
            LiveChatService.mark_messages_as_read(session.session_id, request.user)

            message_data = []
            for message in messages:
                message_data.append({
                    'id': message.id,
                    'content': message.content,
                    'sender': message.sender.username if message.sender else 'System',
                    'sender_name': message.sender.get_full_name() if message.sender else 'System',
                    'message_type': message.message_type,
                    'is_read': message.is_read,
                    'created_at': message.created_at.isoformat()
                })

            return Response({
                'session_id': session.session_id,
                'messages': message_data,
                'total_messages': len(message_data)
            })

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'])
    def end_session(self, request, pk=None):
        """End a chat session"""
        try:
            session = self.get_object()
            reason = request.data.get('reason', 'Session ended')

            ended_session = LiveChatService.end_chat_session(
                session.session_id,
                request.user,
                reason
            )

            if ended_session:
                return Response({
                    'session_id': session.session_id,
                    'status': 'ended',
                    'ended_at': ended_session.ended_at.isoformat(),
                    'message': 'Chat session ended successfully'
                })
            else:
                return Response({
                    'error': 'Failed to end chat session'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'])
    def assign_agent(self, request, pk=None):
        """Assign an agent to a chat session"""
        try:
            session = self.get_object()
            agent_id = request.data.get('agent_id')

            if agent_id:
                try:
                    agent_user = User.objects.get(id=agent_id)
                except User.DoesNotExist:
                    return Response({
                        'error': 'Agent not found'
                    }, status=status.HTTP_404_NOT_FOUND)
            else:
                agent_user = None

            assigned_session, message = LiveChatService.assign_agent_to_session(
                session.session_id,
                agent_user
            )

            if assigned_session:
                return Response({
                    'session_id': session.session_id,
                    'agent': assigned_session.agent.username if assigned_session.agent else None,
                    'agent_name': assigned_session.agent.get_full_name() if assigned_session.agent else None,
                    'status': assigned_session.status,
                    'message': message
                })
            else:
                return Response({
                    'error': message
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'])
    def transfer_session(self, request, pk=None):
        """Transfer a chat session to another agent"""
        try:
            session = self.get_object()
            new_agent_id = request.data.get('new_agent_id')
            reason = request.data.get('reason', 'Session transferred')

            if not new_agent_id:
                return Response({
                    'error': 'New agent ID is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            try:
                new_agent_user = User.objects.get(id=new_agent_id)
            except User.DoesNotExist:
                return Response({
                    'error': 'New agent not found'
                }, status=status.HTTP_404_NOT_FOUND)

            transferred_session = LiveChatService.transfer_session_to_agent(
                session.session_id,
                new_agent_user,
                reason
            )

            if transferred_session:
                return Response({
                    'session_id': session.session_id,
                    'new_agent': new_agent_user.username,
                    'new_agent_name': new_agent_user.get_full_name(),
                    'message': 'Session transferred successfully'
                })
            else:
                return Response({
                    'error': 'Failed to transfer session'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'])
    def stats(self, request, pk=None):
        """Get statistics for a chat session"""
        try:
            session = self.get_object()
            stats = LiveChatService.get_session_stats(session.session_id)
            return Response(stats)
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def waiting_sessions(self, request):
        """Get sessions waiting for agent assignment"""
        try:
            sessions = LiveChatService.get_waiting_sessions()
            session_data = []

            for session in sessions:
                session_data.append({
                    'session_id': session.session_id,
                    'customer': session.customer.get_full_name() if session.customer else 'Unknown',
                    'customer_email': session.customer.email if session.customer else '',
                    'subject': session.subject,
                    'started_at': session.started_at.isoformat(),
                    'waiting_time_minutes': (timezone.now() - session.started_at).total_seconds() / 60
                })

            return Response({
                'waiting_sessions': session_data,
                'total_waiting': len(session_data)
            })

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def my_active_sessions(self, request):
        """Get active sessions for the current agent"""
        try:
            sessions = LiveChatService.get_active_sessions_for_agent(request.user)
            session_data = []

            for session in sessions:
                session_data.append({
                    'session_id': session.session_id,
                    'customer': session.customer.get_full_name() if session.customer else 'Unknown',
                    'customer_email': session.customer.email if session.customer else '',
                    'subject': session.subject,
                    'started_at': session.started_at.isoformat(),
                    'last_activity': session.last_activity_at.isoformat() if session.last_activity_at else None,
                    'unread_messages': ChatMessage.objects.filter(
                        session=session,
                        is_read=False
                    ).exclude(sender=request.user).count()
                })

            return Response({
                'active_sessions': session_data,
                'total_active': len(session_data)
            })

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Simple API endpoints for public access
@csrf_exempt
@require_http_methods(["POST"])
def start_public_chat(request):
    """Start a chat session for public users"""
    try:
        data = json.loads(request.body)

        session, customer_created = LiveChatService.create_chat_session(
            customer_data=data,
            subject=data.get('subject', 'Live Chat Support')
        )

        # Try to assign agent
        assigned_session, message = LiveChatService.assign_agent_to_session(session.session_id)

        return JsonResponse({
            'session_id': session.session_id,
            'status': session.status,
            'agent_assigned': assigned_session is not None,
            'message': message,
            'customer_created': customer_created
        })

    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def send_public_message(request):
    """Send a message in a public chat session"""
    try:
        data = json.loads(request.body)
        session_id = data.get('session_id')
        content = data.get('content', '').strip()

        if not session_id or not content:
            return JsonResponse({
                'error': 'Session ID and content are required'
            }, status=400)

        # For public messages, we'll use the customer as sender
        try:
            session = LiveChatSession.objects.get(session_id=session_id)
            message = LiveChatService.send_message(session_id, session.customer, content)

            if message:
                return JsonResponse({
                    'message_id': message.id,
                    'content': message.content,
                    'created_at': message.created_at.isoformat(),
                    'success': True
                })
            else:
                return JsonResponse({
                    'error': 'Failed to send message'
                }, status=500)

        except LiveChatSession.DoesNotExist:
            return JsonResponse({
                'error': 'Chat session not found'
            }, status=404)

    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=500)

@csrf_exempt
@require_http_methods(["GET"])
def get_public_messages(request):
    """Get messages for a public chat session"""
    try:
        session_id = request.GET.get('session_id')

        if not session_id:
            return JsonResponse({
                'error': 'Session ID is required'
            }, status=400)

        messages = LiveChatService.get_session_messages(session_id)

        message_data = []
        for message in messages:
            message_data.append({
                'id': message.id,
                'content': message.content,
                'sender': message.sender.username if message.sender else 'System',
                'sender_name': message.sender.get_full_name() if message.sender else 'System',
                'message_type': message.message_type,
                'created_at': message.created_at.isoformat()
            })

        return JsonResponse({
            'messages': message_data,
            'total': len(message_data)
        })

    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=500)
