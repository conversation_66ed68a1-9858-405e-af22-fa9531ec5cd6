"""
Customer Management Models
Handles customer creation, tracking, and management
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import uuid

class Customer(models.Model):
    """Customer model for tracking all customers"""

    CUSTOMER_TYPES = [
        ('individual', 'Individual'),
        ('business', 'Business'),
        ('enterprise', 'Enterprise'),
    ]

    CUSTOMER_STATUS = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('suspended', 'Suspended'),
        ('prospect', 'Prospect'),
    ]

    # Basic Information
    customer_id = models.CharField(max_length=20, unique=True, editable=False)
    user = models.OneToOneField(User, on_delete=models.CASCADE, null=True, blank=True, related_name='customer_record')

    # Contact Information
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100, blank=True)
    email = models.EmailField(unique=True)
    phone = models.Char<PERSON>ield(max_length=20, blank=True)

    # Customer Details
    customer_type = models.CharField(max_length=20, choices=CUSTOMER_TYPES, default='individual')
    status = models.CharField(max_length=20, choices=CUSTOMER_STATUS, default='prospect')

    # Company Information (for business customers)
    company_name = models.CharField(max_length=200, blank=True)
    company_size = models.CharField(max_length=50, blank=True)
    industry = models.CharField(max_length=100, blank=True)

    # Address Information
    address_line1 = models.CharField(max_length=255, blank=True)
    address_line2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    country = models.CharField(max_length=100, blank=True)

    # Preferences
    preferred_language = models.CharField(max_length=10, choices=[('en', 'English'), ('ar', 'Arabic')], default='en')
    preferred_contact_method = models.CharField(
        max_length=20,
        choices=[
            ('email', 'Email'),
            ('phone', 'Phone'),
            ('sms', 'SMS'),
            ('chat', 'Chat'),
        ],
        default='email'
    )

    # Tracking
    source = models.CharField(max_length=50, blank=True, help_text='How customer found us')
    internal_notes = models.TextField(blank=True)
    tags = models.CharField(max_length=500, blank=True, help_text='Comma-separated tags')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_contact_date = models.DateTimeField(null=True, blank=True)

    # Statistics
    total_tickets = models.IntegerField(default=0)
    total_orders = models.IntegerField(default=0)
    total_spent = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    satisfaction_score = models.FloatField(default=0.0, help_text='Average satisfaction score')

    class Meta:
        db_table = 'customer_service_customer'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['customer_id']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]

    def save(self, *args, **kwargs):
        if not self.customer_id:
            self.customer_id = self.generate_customer_id()
        super().save(*args, **kwargs)

    def generate_customer_id(self):
        """Generate unique customer ID"""
        import random
        import string

        while True:
            # Format: CUST-YYYYMMDD-XXXX
            date_part = timezone.now().strftime('%Y%m%d')
            random_part = ''.join(random.choices(string.digits, k=4))
            customer_id = f"CUST-{date_part}-{random_part}"

            if not Customer.objects.filter(customer_id=customer_id).exists():
                return customer_id

    def get_full_name(self):
        """Get customer's full name"""
        if self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.first_name

    def update_last_contact(self):
        """Update last contact date"""
        self.last_contact_date = timezone.now()
        self.save(update_fields=['last_contact_date'])

    def add_tag(self, tag):
        """Add a tag to customer"""
        current_tags = [t.strip() for t in self.tags.split(',') if t.strip()]
        if tag not in current_tags:
            current_tags.append(tag)
            self.tags = ', '.join(current_tags)
            self.save(update_fields=['tags'])

    def get_tags_list(self):
        """Get tags as a list"""
        return [t.strip() for t in self.tags.split(',') if t.strip()]

    def __str__(self):
        return f"{self.customer_id} - {self.get_full_name()} ({self.email})"

class CustomerInteraction(models.Model):
    """Track all customer interactions"""

    INTERACTION_TYPES = [
        ('chat', 'Chat'),
        ('email', 'Email'),
        ('phone', 'Phone'),
        ('ticket', 'Support Ticket'),
        ('meeting', 'Meeting'),
        ('other', 'Other'),
    ]

    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='interactions')
    interaction_type = models.CharField(max_length=20, choices=INTERACTION_TYPES)
    subject = models.CharField(max_length=200)
    description = models.TextField()

    # Staff member who handled the interaction
    handled_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    # Interaction details
    duration_minutes = models.IntegerField(null=True, blank=True)
    outcome = models.CharField(max_length=200, blank=True)
    follow_up_required = models.BooleanField(default=False)
    follow_up_date = models.DateTimeField(null=True, blank=True)

    # Satisfaction
    customer_satisfaction = models.IntegerField(
        null=True,
        blank=True,
        choices=[(i, str(i)) for i in range(1, 6)],
        help_text='1-5 satisfaction rating'
    )

    # Metadata
    metadata = models.JSONField(default=dict, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'customer_service_customer_interaction'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.customer.customer_id} - {self.interaction_type} - {self.subject}"

class CustomerNote(models.Model):
    """Internal notes about customers"""

    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='customer_notes')
    note = models.TextField()
    is_important = models.BooleanField(default=False)
    is_private = models.BooleanField(default=False)

    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'customer_service_customer_note'
        ordering = ['-created_at']

    def __str__(self):
        return f"Note for {self.customer.customer_id} by {self.created_by.username}"

class CustomerDocument(models.Model):
    """Documents related to customers"""

    DOCUMENT_TYPES = [
        ('contract', 'Contract'),
        ('invoice', 'Invoice'),
        ('receipt', 'Receipt'),
        ('id_document', 'ID Document'),
        ('other', 'Other'),
    ]

    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='documents')
    document_type = models.CharField(max_length=20, choices=DOCUMENT_TYPES)
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    file = models.FileField(upload_to='customer_documents/')

    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE)
    uploaded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'customer_service_customer_document'
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"{self.customer.customer_id} - {self.title}"
