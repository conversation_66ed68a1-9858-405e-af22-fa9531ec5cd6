#!/usr/bin/env python3
"""
CRITICAL FIX: Pre-Migration Validation Script
Comprehensive checks before PostgreSQL migration
"""

import os
import sys
import sqlite3
import subprocess
from pathlib import Path
from datetime import datetime

# Add Django to path
sys.path.append(str(Path(__file__).parent.parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

import django
django.setup()

from django.db import connection
from django.core.management import call_command
from django.apps import apps

class PreMigrationValidator:
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.info = []
        
    def run_all_checks(self):
        """Run comprehensive pre-migration checks"""
        print("🔍 Running Pre-Migration Validation...")
        print("=" * 50)
        
        self.check_sqlite_database()
        self.check_data_integrity()
        self.check_disk_space()
        self.check_dependencies()
        self.check_postgresql_connection()
        self.estimate_migration_time()
        
        self.print_summary()
        return len(self.errors) == 0
    
    def check_sqlite_database(self):
        """Validate SQLite database health"""
        print("📊 Checking SQLite database...")
        
        try:
            # Check database file exists and is readable
            db_path = Path('db.sqlite3')
            if not db_path.exists():
                self.errors.append("SQLite database file not found")
                return
            
            # Check file size
            file_size = db_path.stat().st_size
            file_size_mb = file_size / (1024 * 1024)
            self.info.append(f"Database size: {file_size_mb:.2f} MB")
            
            # Check database integrity
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            cursor.execute("PRAGMA integrity_check")
            integrity_result = cursor.fetchone()[0]
            
            if integrity_result == "ok":
                self.info.append("SQLite integrity check: PASSED")
            else:
                self.errors.append(f"SQLite integrity check failed: {integrity_result}")
            
            # Check for locked database
            cursor.execute("BEGIN IMMEDIATE")
            cursor.execute("ROLLBACK")
            self.info.append("Database lock check: PASSED")
            
            conn.close()
            
        except Exception as e:
            self.errors.append(f"SQLite check failed: {e}")
    
    def check_data_integrity(self):
        """Check data integrity and count records"""
        print("🔢 Checking data integrity...")
        
        try:
            models = apps.get_models()
            total_records = 0
            
            for model in models:
                try:
                    count = model.objects.count()
                    total_records += count
                    
                    if count > 0:
                        self.info.append(f"{model._meta.label}: {count} records")
                        
                        # Check for orphaned foreign keys
                        for field in model._meta.fields:
                            if field.related_model and hasattr(field, 'to'):
                                # This is a basic check - more sophisticated checks could be added
                                pass
                                
                except Exception as e:
                    self.warnings.append(f"Could not count {model._meta.label}: {e}")
            
            self.info.append(f"Total records to migrate: {total_records}")
            
            if total_records == 0:
                self.warnings.append("No data found in database")
            elif total_records > 100000:
                self.warnings.append(f"Large dataset ({total_records} records) - migration may take longer")
                
        except Exception as e:
            self.errors.append(f"Data integrity check failed: {e}")
    
    def check_disk_space(self):
        """Check available disk space"""
        print("💾 Checking disk space...")
        
        try:
            # Get current directory disk usage
            statvfs = os.statvfs('.')
            free_bytes = statvfs.f_frsize * statvfs.f_bavail
            free_gb = free_bytes / (1024**3)
            
            # Get database size
            db_size = Path('db.sqlite3').stat().st_size if Path('db.sqlite3').exists() else 0
            db_size_gb = db_size / (1024**3)
            
            # Estimate space needed (database size * 3 for safety)
            estimated_needed = db_size_gb * 3
            
            self.info.append(f"Available disk space: {free_gb:.2f} GB")
            self.info.append(f"Estimated space needed: {estimated_needed:.2f} GB")
            
            if free_gb < estimated_needed:
                self.errors.append(f"Insufficient disk space. Need {estimated_needed:.2f} GB, have {free_gb:.2f} GB")
            elif free_gb < estimated_needed * 2:
                self.warnings.append("Low disk space - consider freeing up space before migration")
                
        except Exception as e:
            self.warnings.append(f"Could not check disk space: {e}")
    
    def check_dependencies(self):
        """Check required dependencies"""
        print("📦 Checking dependencies...")
        
        required_packages = [
            'psycopg2',
            'django_redis',
            'django_ratelimit'
        ]
        
        for package in required_packages:
            try:
                __import__(package)
                self.info.append(f"✅ {package} installed")
            except ImportError:
                self.errors.append(f"❌ {package} not installed")
    
    def check_postgresql_connection(self):
        """Test PostgreSQL connection"""
        print("🐘 Testing PostgreSQL connection...")
        
        try:
            # Try to connect to PostgreSQL using environment variables
            import psycopg2
            
            conn_params = {
                'host': os.environ.get('DB_HOST', 'localhost'),
                'port': os.environ.get('DB_PORT', '5432'),
                'database': os.environ.get('DB_NAME', 'ems_production'),
                'user': os.environ.get('DB_USER', 'ems_user'),
                'password': os.environ.get('DB_PASSWORD', ''),
            }
            
            if not conn_params['password']:
                self.warnings.append("DB_PASSWORD not set - PostgreSQL connection may fail")
                return
            
            conn = psycopg2.connect(**conn_params)
            cursor = conn.cursor()
            cursor.execute("SELECT version()")
            version = cursor.fetchone()[0]
            
            self.info.append(f"PostgreSQL connection: SUCCESS")
            self.info.append(f"PostgreSQL version: {version}")
            
            conn.close()
            
        except ImportError:
            self.errors.append("psycopg2 not installed")
        except Exception as e:
            self.errors.append(f"PostgreSQL connection failed: {e}")
    
    def estimate_migration_time(self):
        """Estimate migration time based on data size"""
        print("⏱️ Estimating migration time...")
        
        try:
            # Count total records
            models = apps.get_models()
            total_records = sum(model.objects.count() for model in models)
            
            # Rough estimation: 1000 records per second
            estimated_seconds = total_records / 1000
            estimated_minutes = estimated_seconds / 60
            
            if estimated_minutes < 1:
                self.info.append("Estimated migration time: < 1 minute")
            elif estimated_minutes < 60:
                self.info.append(f"Estimated migration time: {estimated_minutes:.1f} minutes")
            else:
                estimated_hours = estimated_minutes / 60
                self.info.append(f"Estimated migration time: {estimated_hours:.1f} hours")
                self.warnings.append("Long migration time - consider maintenance window")
                
        except Exception as e:
            self.warnings.append(f"Could not estimate migration time: {e}")
    
    def print_summary(self):
        """Print validation summary"""
        print("\n" + "=" * 50)
        print("📋 VALIDATION SUMMARY")
        print("=" * 50)
        
        if self.info:
            print("\n✅ INFORMATION:")
            for item in self.info:
                print(f"  • {item}")
        
        if self.warnings:
            print("\n⚠️ WARNINGS:")
            for item in self.warnings:
                print(f"  • {item}")
        
        if self.errors:
            print("\n❌ ERRORS:")
            for item in self.errors:
                print(f"  • {item}")
            print("\n🚫 Migration cannot proceed until errors are resolved!")
        else:
            print("\n🎉 All checks passed! Ready for migration.")

if __name__ == '__main__':
    validator = PreMigrationValidator()
    success = validator.run_all_checks()
    sys.exit(0 if success else 1)
