/**
 * MOBILE FIX: Mobile-Responsive CSS Styles
 * Comprehensive mobile optimizations and responsive design
 */

/* Mobile device detection classes */
.device-mobile {
  /* Mobile-specific optimizations */
}

.device-tablet {
  /* Tablet-specific optimizations */
}

.device-desktop {
  /* Desktop-specific optimizations */
}

.device-touch {
  /* Touch device optimizations */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.device-low-end {
  /* Low-end device optimizations */
}

/* MOBILE FIX: Touch-friendly interactions */
.touch-active {
  /* Active touch state optimizations */
}

.touch-active * {
  /* Disable hover effects during touch */
  pointer-events: none;
}

.touch-active button,
.touch-active a,
.touch-active [role="button"] {
  pointer-events: auto;
}

/* MOBILE FIX: Safe area support for notched devices */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

.safe-area-all {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* MOBILE FIX: Responsive typography */
@media (max-width: 768px) {
  .responsive-text-xs { font-size: 0.75rem; }
  .responsive-text-sm { font-size: 0.875rem; }
  .responsive-text-base { font-size: 1rem; }
  .responsive-text-lg { font-size: 1.125rem; }
  .responsive-text-xl { font-size: 1.25rem; }
  .responsive-text-2xl { font-size: 1.5rem; }
  .responsive-text-3xl { font-size: 1.875rem; }
}

/* MOBILE FIX: Touch-friendly button sizes */
@media (max-width: 768px) {
  .touch-button {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
  }
  
  .touch-button-sm {
    min-height: 36px;
    min-width: 36px;
    padding: 8px 12px;
  }
  
  .touch-button-lg {
    min-height: 52px;
    min-width: 52px;
    padding: 16px 24px;
  }
}

/* MOBILE FIX: Responsive spacing */
@media (max-width: 768px) {
  .mobile-p-2 { padding: 0.5rem; }
  .mobile-p-4 { padding: 1rem; }
  .mobile-p-6 { padding: 1.5rem; }
  .mobile-p-8 { padding: 2rem; }
  
  .mobile-m-2 { margin: 0.5rem; }
  .mobile-m-4 { margin: 1rem; }
  .mobile-m-6 { margin: 1.5rem; }
  .mobile-m-8 { margin: 2rem; }
  
  .mobile-gap-2 { gap: 0.5rem; }
  .mobile-gap-4 { gap: 1rem; }
  .mobile-gap-6 { gap: 1.5rem; }
  .mobile-gap-8 { gap: 2rem; }
}

/* MOBILE FIX: Responsive grid layouts */
@media (max-width: 768px) {
  .mobile-grid-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .mobile-grid-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  
  .mobile-flex-col { flex-direction: column; }
  .mobile-flex-row { flex-direction: row; }
  
  .mobile-w-full { width: 100%; }
  .mobile-w-auto { width: auto; }
}

/* MOBILE FIX: Responsive form elements */
@media (max-width: 768px) {
  .mobile-form-input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 16px;
    min-height: 44px;
  }
  
  .mobile-form-select {
    font-size: 16px;
    padding: 12px 16px;
    min-height: 44px;
  }
  
  .mobile-form-textarea {
    font-size: 16px;
    padding: 12px 16px;
    min-height: 88px;
  }
}

/* MOBILE FIX: Responsive tables */
@media (max-width: 768px) {
  .mobile-table-card {
    display: block;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .mobile-table-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .mobile-table-row:last-child {
    border-bottom: none;
  }
  
  .mobile-table-label {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
    min-width: 0;
    flex: 1;
  }
  
  .mobile-table-value {
    color: white;
    font-size: 0.875rem;
    text-align: right;
    margin-left: 8px;
    flex: 1;
    word-break: break-word;
  }
}

/* MOBILE FIX: Responsive modals and drawers */
@media (max-width: 768px) {
  .mobile-modal {
    margin: 0;
    max-height: 90vh;
    border-radius: 16px 16px 0 0;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    transform: translateY(100%);
    transition: transform 0.3s ease-out;
  }
  
  .mobile-modal.open {
    transform: translateY(0);
  }
  
  .mobile-drawer-handle {
    width: 40px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin: 8px auto;
  }
}

/* MOBILE FIX: Performance optimizations for low-end devices */
.device-low-end .reduce-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
}

.device-low-end .disable-blur {
  backdrop-filter: none !important;
  filter: none !important;
}

.device-low-end .disable-shadows {
  box-shadow: none !important;
  text-shadow: none !important;
}

/* MOBILE FIX: Pause animations when not visible */
.paused * {
  animation-play-state: paused !important;
}

/* MOBILE FIX: Responsive navigation */
@media (max-width: 768px) {
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 8px;
    z-index: 50;
  }
  
  .mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    min-height: 44px;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s ease;
  }
  
  .mobile-nav-item.active {
    color: white;
    background: rgba(255, 255, 255, 0.1);
  }
  
  .mobile-nav-item:hover {
    color: white;
    background: rgba(255, 255, 255, 0.05);
  }
  
  .mobile-nav-icon {
    width: 20px;
    height: 20px;
    margin-bottom: 4px;
  }
  
  .mobile-nav-label {
    font-size: 0.75rem;
    font-weight: 500;
  }
}

/* MOBILE FIX: Responsive scrolling */
@media (max-width: 768px) {
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
  
  .mobile-scroll-x {
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
  }
  
  .mobile-scroll-y {
    overflow-x: hidden;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* MOBILE FIX: Responsive images */
@media (max-width: 768px) {
  .mobile-img {
    max-width: 100%;
    height: auto;
    object-fit: cover;
  }
  
  .mobile-img-contain {
    object-fit: contain;
  }
  
  .mobile-img-cover {
    object-fit: cover;
  }
}

/* MOBILE FIX: Accessibility improvements for mobile */
@media (max-width: 768px) {
  .mobile-focus:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
  
  .mobile-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
}

/* MOBILE FIX: Dark mode optimizations for mobile */
@media (max-width: 768px) and (prefers-color-scheme: dark) {
  .mobile-dark-optimized {
    background: #000;
    color: #fff;
  }
  
  .mobile-dark-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .mobile-dark-text {
    color: rgba(255, 255, 255, 0.9);
  }
  
  .mobile-dark-text-muted {
    color: rgba(255, 255, 255, 0.6);
  }
}
