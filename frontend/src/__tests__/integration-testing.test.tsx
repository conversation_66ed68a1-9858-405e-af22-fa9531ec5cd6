/**
 * CRITICAL INTEGRATION TESTING
 * End-to-end tests to verify all bug fixes work together correctly
 */

import { render, screen, waitFor, fireEvent, act } from '@testing-library/react'
import { Provider } from 'react-redux'
import { BrowserRouter } from 'react-router-dom'
import { store } from '../store'
import { securityValidator } from '../utils/securityValidator'
import { criticalPerformanceMonitor } from '../utils/criticalPerformanceMonitor'
import { automatedMemoryLeakMonitor } from './memory-leak-detection.test'
import { webSocketService } from '../services/websocket'
import { MemoryLeakPrevention } from '../utils/performanceBugFixes'

// Mock components for testing
const MockApp = ({ children }: { children: React.ReactNode }) => (
  <Provider store={store}>
    <BrowserRouter>
      {children}
    </BrowserRouter>
  </Provider>
)

// Mock fetch for API testing
const mockFetch = jest.fn()
global.fetch = mockFetch

// Mock WebSocket
global.WebSocket = jest.fn().mockImplementation(() => ({
  close: jest.fn(),
  send: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  readyState: WebSocket.OPEN
}))

describe('🔗 CRITICAL INTEGRATION TESTING', () => {
  
  beforeEach(() => {
    mockFetch.mockClear()
    jest.useFakeTimers()
    
    // Reset all monitoring systems
    criticalPerformanceMonitor.stopMonitoring()
    automatedMemoryLeakMonitor.stopMonitoring()
    MemoryLeakPrevention.cleanup()
    
    // Mock successful API responses
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ 
        results: [],
        count: 0,
        query_count: 3 // Optimized query count
      }),
      headers: new Headers({
        'Set-Cookie': 'access_token=secure_token; HttpOnly; Secure'
      })
    })
  })

  afterEach(() => {
    jest.useRealTimers()
    criticalPerformanceMonitor.stopMonitoring()
    automatedMemoryLeakMonitor.stopMonitoring()
    MemoryLeakPrevention.cleanup()
  })

  describe('🎯 All Fixes Integration Test', () => {
    
    test('CRITICAL: All 6 bug fixes should work together seamlessly', async () => {
      console.log('🚀 Starting comprehensive integration test...')
      
      // Start all monitoring systems
      criticalPerformanceMonitor.startMonitoring()
      automatedMemoryLeakMonitor.startMonitoring()
      
      const integrationResults = {
        securityFix: false,
        apiDeduplicationFix: false,
        databaseOptimizationFix: false,
        memoryLeakFix: false,
        webSocketFix: false,
        useEffectCleanupFix: false
      }

      // Test Fix #1: JWT Token Security
      console.log('Testing Fix #1: JWT Token Security...')
      const securityReport = await securityValidator.runAllChecks()
      integrationResults.securityFix = securityReport.overallStatus === 'secure'
      
      // Test Fix #2: API Deduplication
      console.log('Testing Fix #2: API Deduplication...')
      let apiCallCount = 0
      const mockApiCall = () => {
        apiCallCount++
        return Promise.resolve({ data: 'test' })
      }
      
      const { deduplicateRequest } = await import('../utils/apiCache')
      const promises = Array(10).fill(null).map(() => 
        deduplicateRequest('integration-test', mockApiCall)
      )
      await Promise.all(promises)
      integrationResults.apiDeduplicationFix = apiCallCount === 1

      // Test Fix #3: Database Optimization
      console.log('Testing Fix #3: Database Optimization...')
      const apiStart = performance.now()
      await fetch('/api/employees/')
      const apiTime = performance.now() - apiStart
      integrationResults.databaseOptimizationFix = apiTime < 500

      // Test Fix #4: Memory Leak Prevention
      console.log('Testing Fix #4: Memory Leak Prevention...')
      const initialMemory = performance.memory?.usedJSHeapSize || 0
      
      // Create and destroy components
      for (let i = 0; i < 10; i++) {
        const TestComponent = () => {
          React.useEffect(() => {
            const interval = MemoryLeakPrevention.setInterval(() => {}, 1000)
            return () => clearInterval(interval)
          }, [])
          return <div>Test {i}</div>
        }
        
        const { unmount } = render(<MockApp><TestComponent /></MockApp>)
        unmount()
      }
      
      MemoryLeakPrevention.cleanup()
      const finalMemory = performance.memory?.usedJSHeapSize || 0
      const memoryIncrease = finalMemory - initialMemory
      integrationResults.memoryLeakFix = memoryIncrease < 10 * 1024 * 1024 // < 10MB

      // Test Fix #5: WebSocket Connection
      console.log('Testing Fix #5: WebSocket Connection...')
      let connectionCount = 0
      global.WebSocket = jest.fn().mockImplementation(() => {
        connectionCount++
        return {
          close: jest.fn(() => connectionCount--),
          send: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          readyState: WebSocket.OPEN
        }
      })
      
      // Multiple connection attempts
      for (let i = 0; i < 5; i++) {
        webSocketService.connect()
        webSocketService.disconnect()
      }
      integrationResults.webSocketFix = connectionCount <= 1

      // Test Fix #6: useEffect Cleanup
      console.log('Testing Fix #6: useEffect Cleanup...')
      const initialResources = MemoryLeakPrevention.getResourceCounts()
      
      const CleanupTestComponent = () => {
        React.useEffect(() => {
          const interval = setInterval(() => {}, 1000)
          const timeout = setTimeout(() => {}, 5000)
          
          return () => {
            clearInterval(interval)
            clearTimeout(timeout)
          }
        }, [])
        return <div>Cleanup Test</div>
      }
      
      // Mount and unmount multiple components
      for (let i = 0; i < 5; i++) {
        const { unmount } = render(<MockApp><CleanupTestComponent /></MockApp>)
        unmount()
      }
      
      const finalResources = MemoryLeakPrevention.getResourceCounts()
      const resourceIncrease = Object.values(finalResources).reduce((sum: number, count: number) => sum + count, 0) -
                              Object.values(initialResources).reduce((sum: number, count: number) => sum + count, 0)
      integrationResults.useEffectCleanupFix = resourceIncrease <= 0

      // Generate comprehensive report
      const passedFixes = Object.values(integrationResults).filter(Boolean).length
      const totalFixes = Object.keys(integrationResults).length
      const successRate = (passedFixes / totalFixes) * 100

      console.log('🎯 INTEGRATION TEST RESULTS:')
      console.log('================================')
      Object.entries(integrationResults).forEach(([fix, passed]) => {
        console.log(`${passed ? '✅' : '❌'} ${fix}: ${passed ? 'PASSED' : 'FAILED'}`)
      })
      console.log(`\nOverall Success Rate: ${successRate}%`)
      console.log(`Fixes Working: ${passedFixes}/${totalFixes}`)

      // All fixes should pass
      expect(successRate).toBeGreaterThanOrEqual(90) // At least 90% success rate
      expect(passedFixes).toBeGreaterThanOrEqual(5) // At least 5 out of 6 fixes working

      console.log('✅ INTEGRATION TEST COMPLETED')
    }, 30000) // 30 second timeout

    test('CRITICAL: Production-like scenario stress test', async () => {
      console.log('🌪️ Starting production-like stress test...')
      
      // Start monitoring
      criticalPerformanceMonitor.startMonitoring()
      automatedMemoryLeakMonitor.startMonitoring()
      
      const stressResults = {
        apiCallsHandled: 0,
        memoryStable: false,
        webSocketStable: false,
        resourcesClean: false,
        performanceGood: false
      }

      // Simulate heavy API usage
      console.log('Simulating heavy API usage...')
      const apiPromises = []
      for (let i = 0; i < 50; i++) {
        apiPromises.push(fetch(`/api/test-endpoint-${i % 5}`))
      }
      
      await Promise.all(apiPromises)
      stressResults.apiCallsHandled = apiPromises.length

      // Simulate component lifecycle stress
      console.log('Simulating component lifecycle stress...')
      const StressComponent = ({ id }: { id: number }) => {
        const [data, setData] = React.useState<number[]>([])
        
        React.useEffect(() => {
          // Simulate heavy data loading
          const heavyData = Array(1000).fill(0).map(() => Math.random())
          setData(heavyData)
          
          const interval = setInterval(() => {
            setData(prev => [...prev, Math.random()])
          }, 100)
          
          return () => {
            clearInterval(interval)
            setData([])
          }
        }, [])
        
        return <div>Stress Component {id}: {data.length} items</div>
      }
      
      // Mount and unmount many components rapidly
      const components = []
      for (let i = 0; i < 20; i++) {
        components.push(render(<MockApp><StressComponent id={i} /></MockApp>))
        jest.advanceTimersByTime(50)
      }
      
      // Unmount all components
      components.forEach(({ unmount }) => unmount())
      
      // Check memory stability
      jest.advanceTimersByTime(1000)
      const memoryReport = automatedMemoryLeakMonitor.getMemoryReport()
      stressResults.memoryStable = !memoryReport.includes('CRITICAL LEAKS DETECTED')
      
      // Check WebSocket stability
      for (let i = 0; i < 10; i++) {
        webSocketService.connect()
        jest.advanceTimersByTime(100)
        webSocketService.disconnect()
      }
      stressResults.webSocketStable = true // No crashes
      
      // Check resource cleanup
      MemoryLeakPrevention.cleanup()
      const resources = MemoryLeakPrevention.getResourceCounts()
      const totalResources = Object.values(resources).reduce((sum: number, count: number) => sum + count, 0)
      stressResults.resourcesClean = totalResources < 10
      
      // Check performance
      const performanceReport = criticalPerformanceMonitor.getFixVerificationReport()
      stressResults.performanceGood = performanceReport.includes('ALL FIXES WORKING')
      
      console.log('🌪️ STRESS TEST RESULTS:')
      console.log('========================')
      Object.entries(stressResults).forEach(([metric, result]) => {
        console.log(`${typeof result === 'boolean' ? (result ? '✅' : '❌') : '📊'} ${metric}: ${result}`)
      })
      
      // Verify stress test results
      expect(stressResults.apiCallsHandled).toBe(50)
      expect(stressResults.memoryStable).toBe(true)
      expect(stressResults.webSocketStable).toBe(true)
      expect(stressResults.resourcesClean).toBe(true)
      
      console.log('✅ STRESS TEST COMPLETED')
    }, 45000) // 45 second timeout

    test('CRITICAL: Real-world usage simulation', async () => {
      console.log('🌍 Starting real-world usage simulation...')
      
      const usageResults = {
        loginFlow: false,
        dataFetching: false,
        navigation: false,
        realTimeUpdates: false,
        cleanup: false
      }

      // Simulate login flow
      console.log('Simulating login flow...')
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ 
          user: { id: 1, name: 'Test User' },
          success: true
        }),
        headers: new Headers({
          'Set-Cookie': 'access_token=secure_token; HttpOnly; Secure'
        })
      })
      
      const loginResponse = await fetch('/api/auth/login/', {
        method: 'POST',
        credentials: 'include',
        body: JSON.stringify({ email: '<EMAIL>', password: 'password' })
      })
      
      usageResults.loginFlow = loginResponse.ok
      
      // Simulate data fetching
      console.log('Simulating data fetching...')
      const dataEndpoints = [
        '/api/employees/',
        '/api/departments/',
        '/api/attendance/',
        '/api/kpi-dashboard/'
      ]
      
      const dataPromises = dataEndpoints.map(endpoint => fetch(endpoint))
      const dataResponses = await Promise.all(dataPromises)
      usageResults.dataFetching = dataResponses.every(response => response.ok)
      
      // Simulate navigation
      console.log('Simulating navigation...')
      const NavigationComponent = () => {
        const [currentPage, setCurrentPage] = React.useState('home')
        
        React.useEffect(() => {
          const interval = setInterval(() => {
            setCurrentPage(prev => prev === 'home' ? 'employees' : 'home')
          }, 500)
          
          return () => clearInterval(interval)
        }, [])
        
        return <div>Current page: {currentPage}</div>
      }
      
      const { unmount: unmountNav } = render(<MockApp><NavigationComponent /></MockApp>)
      jest.advanceTimersByTime(2000)
      unmountNav()
      usageResults.navigation = true
      
      // Simulate real-time updates
      console.log('Simulating real-time updates...')
      webSocketService.connect()
      jest.advanceTimersByTime(1000)
      webSocketService.disconnect()
      usageResults.realTimeUpdates = true
      
      // Final cleanup verification
      console.log('Verifying final cleanup...')
      MemoryLeakPrevention.cleanup()
      const finalResources = MemoryLeakPrevention.getResourceCounts()
      const totalFinalResources = Object.values(finalResources).reduce((sum: number, count: number) => sum + count, 0)
      usageResults.cleanup = totalFinalResources === 0
      
      console.log('🌍 REAL-WORLD SIMULATION RESULTS:')
      console.log('==================================')
      Object.entries(usageResults).forEach(([scenario, result]) => {
        console.log(`${result ? '✅' : '❌'} ${scenario}: ${result ? 'SUCCESS' : 'FAILED'}`)
      })
      
      // All scenarios should pass
      const passedScenarios = Object.values(usageResults).filter(Boolean).length
      expect(passedScenarios).toBe(Object.keys(usageResults).length)
      
      console.log('✅ REAL-WORLD SIMULATION COMPLETED')
    }, 30000) // 30 second timeout
  })

  describe('🔄 Cross-Fix Interaction Testing', () => {
    
    test('CRITICAL: Security and performance fixes should not conflict', async () => {
      // Test that security fixes don't break performance
      const securityReport = await securityValidator.runAllChecks()
      
      criticalPerformanceMonitor.startMonitoring()
      jest.advanceTimersByTime(10000)
      
      const performanceReport = criticalPerformanceMonitor.getFixVerificationReport()
      
      expect(securityReport.overallStatus).toBe('secure')
      expect(performanceReport).toContain('Fix #1 (JWT Security): Secure')
      
      console.log('✅ Security and performance fixes compatible')
    })

    test('CRITICAL: Memory and WebSocket fixes should work together', async () => {
      automatedMemoryLeakMonitor.startMonitoring()
      
      // Multiple WebSocket operations
      for (let i = 0; i < 10; i++) {
        webSocketService.connect()
        jest.advanceTimersByTime(100)
        webSocketService.disconnect()
        jest.advanceTimersByTime(100)
      }
      
      jest.advanceTimersByTime(5000)
      
      const memoryReport = automatedMemoryLeakMonitor.getMemoryReport()
      expect(memoryReport).toContain('NO LEAKS DETECTED')
      
      console.log('✅ Memory and WebSocket fixes compatible')
    })
  })
})

/**
 * INTEGRATION TEST UTILITIES
 */
export const IntegrationTestUtils = {
  
  // Run comprehensive integration test
  runComprehensiveTest: async (): Promise<{ success: boolean; results: any }> => {
    const results = {
      security: false,
      performance: false,
      memory: false,
      webSocket: false,
      database: false,
      cleanup: false
    }

    try {
      // Test all fixes
      const securityReport = await securityValidator.runAllChecks()
      results.security = securityReport.overallStatus === 'secure'

      criticalPerformanceMonitor.startMonitoring()
      setTimeout(() => {
        const performanceReport = criticalPerformanceMonitor.getFixVerificationReport()
        results.performance = performanceReport.includes('ALL FIXES WORKING')
      }, 5000)

      automatedMemoryLeakMonitor.startMonitoring()
      setTimeout(() => {
        const memoryReport = automatedMemoryLeakMonitor.getMemoryReport()
        results.memory = memoryReport.includes('NO LEAKS DETECTED')
      }, 5000)

      // WebSocket test
      webSocketService.connect()
      webSocketService.disconnect()
      results.webSocket = true

      // Database test
      const response = await fetch('/api/employees/')
      results.database = response.ok

      // Cleanup test
      MemoryLeakPrevention.cleanup()
      const resources = MemoryLeakPrevention.getResourceCounts()
      results.cleanup = Object.values(resources).every((count: number) => count === 0)

    } catch (error) {
      console.error('Integration test failed:', error)
    }

    const success = Object.values(results).every(Boolean)
    return { success, results }
  },

  // Generate integration report
  generateIntegrationReport: (results: any): string => {
    const passedTests = Object.values(results).filter(Boolean).length
    const totalTests = Object.keys(results).length
    const successRate = (passedTests / totalTests) * 100

    return `
🔗 INTEGRATION TEST REPORT
==========================
Success Rate: ${successRate}%
Passed: ${passedTests}/${totalTests}

Test Results:
${Object.entries(results).map(([test, passed]) => 
  `${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`
).join('\n')}

Overall Status: ${successRate >= 90 ? '✅ ALL SYSTEMS GO' : '❌ ISSUES DETECTED'}
    `
  }
}
