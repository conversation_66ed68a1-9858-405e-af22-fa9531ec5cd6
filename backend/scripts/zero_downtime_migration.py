#!/usr/bin/env python3
"""
CRITICAL FIX: Zero-Downtime Database Migration
Advanced migration strategy for production environments
"""

import os
import sys
import time
import threading
import json
from pathlib import Path
from datetime import datetime

# Add Django to path
sys.path.append(str(Path(__file__).parent.parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

import django
django.setup()

from django.db import connections, transaction
from django.core.management import call_command
from django.apps import apps
from django.conf import settings

class ZeroDowntimeMigrator:
    def __init__(self):
        self.migration_status = {
            "phase": "initialization",
            "progress": 0,
            "errors": [],
            "start_time": None,
            "current_step": None
        }
        self.dual_write_active = False
        self.migration_thread = None
        
    def execute_zero_downtime_migration(self):
        """Execute zero-downtime migration strategy"""
        print("🚀 Starting Zero-Downtime Migration...")
        print("=" * 60)
        
        self.migration_status["start_time"] = datetime.now()
        
        try:
            # Phase 1: Setup PostgreSQL (no downtime)
            self.phase_1_setup_postgresql()
            
            # Phase 2: Enable dual-write mode (minimal downtime)
            self.phase_2_enable_dual_write()
            
            # Phase 3: Migrate historical data (no downtime)
            self.phase_3_migrate_historical_data()
            
            # Phase 4: Validate data consistency (no downtime)
            self.phase_4_validate_consistency()
            
            # Phase 5: Switch to PostgreSQL (minimal downtime)
            self.phase_5_switch_to_postgresql()
            
            # Phase 6: Cleanup (no downtime)
            self.phase_6_cleanup()
            
            print("\n🎉 Zero-downtime migration completed successfully!")
            return True
            
        except Exception as e:
            print(f"\n💥 Migration failed: {e}")
            self.rollback_migration()
            return False
    
    def phase_1_setup_postgresql(self):
        """Phase 1: Setup PostgreSQL database"""
        self.update_status("phase_1_setup", 10, "Setting up PostgreSQL database")
        print("\n📋 Phase 1: Setting up PostgreSQL database...")
        
        # Create PostgreSQL database configuration
        self.setup_postgresql_connection()
        
        # Run migrations on PostgreSQL
        print("  🔄 Running Django migrations on PostgreSQL...")
        call_command('migrate', '--database=postgresql', '--run-syncdb')
        
        # Create indexes
        print("  📇 Creating database indexes...")
        self.create_postgresql_indexes()
        
        print("  ✅ PostgreSQL setup complete")
    
    def phase_2_enable_dual_write(self):
        """Phase 2: Enable dual-write mode"""
        self.update_status("phase_2_dual_write", 25, "Enabling dual-write mode")
        print("\n📋 Phase 2: Enabling dual-write mode...")
        
        # This requires application restart (minimal downtime)
        print("  ⚠️ Application restart required for dual-write mode")
        print("  🔄 Enabling dual-write database router...")
        
        # Create dual-write database router
        self.create_dual_write_router()
        
        # Update Django settings
        self.enable_dual_write_settings()
        
        self.dual_write_active = True
        print("  ✅ Dual-write mode enabled")
    
    def phase_3_migrate_historical_data(self):
        """Phase 3: Migrate historical data in background"""
        self.update_status("phase_3_migration", 40, "Migrating historical data")
        print("\n📋 Phase 3: Migrating historical data...")
        
        # Start background migration thread
        self.migration_thread = threading.Thread(
            target=self.migrate_data_in_background,
            daemon=True
        )
        self.migration_thread.start()
        
        # Monitor migration progress
        while self.migration_thread.is_alive():
            time.sleep(5)
            print(f"  📈 Migration progress: {self.migration_status['progress']}%")
        
        print("  ✅ Historical data migration complete")
    
    def phase_4_validate_consistency(self):
        """Phase 4: Validate data consistency"""
        self.update_status("phase_4_validation", 70, "Validating data consistency")
        print("\n📋 Phase 4: Validating data consistency...")
        
        consistency_check = self.validate_data_consistency()
        
        if not consistency_check["success"]:
            raise Exception(f"Data consistency validation failed: {consistency_check['errors']}")
        
        print("  ✅ Data consistency validated")
    
    def phase_5_switch_to_postgresql(self):
        """Phase 5: Switch reads to PostgreSQL"""
        self.update_status("phase_5_switch", 85, "Switching to PostgreSQL")
        print("\n📋 Phase 5: Switching to PostgreSQL...")
        
        # Update database router to read from PostgreSQL
        print("  🔄 Switching reads to PostgreSQL...")
        self.switch_reads_to_postgresql()
        
        # Brief validation period
        time.sleep(10)
        
        # Stop dual-write mode
        print("  🔄 Stopping dual-write mode...")
        self.disable_dual_write_mode()
        
        print("  ✅ Successfully switched to PostgreSQL")
    
    def phase_6_cleanup(self):
        """Phase 6: Cleanup and optimization"""
        self.update_status("phase_6_cleanup", 95, "Cleanup and optimization")
        print("\n📋 Phase 6: Cleanup and optimization...")
        
        # Optimize PostgreSQL
        print("  🔧 Optimizing PostgreSQL...")
        self.optimize_postgresql()
        
        # Create backup of SQLite (for rollback)
        print("  💾 Creating SQLite backup...")
        self.create_sqlite_backup()
        
        self.update_status("completed", 100, "Migration completed")
        print("  ✅ Cleanup complete")
    
    def setup_postgresql_connection(self):
        """Setup PostgreSQL database connection"""
        
        # Add PostgreSQL database configuration
        postgresql_config = {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': os.environ.get('DB_NAME', 'ems_production'),
            'USER': os.environ.get('DB_USER', 'ems_user'),
            'PASSWORD': os.environ.get('DB_PASSWORD'),
            'HOST': os.environ.get('DB_HOST', 'localhost'),
            'PORT': os.environ.get('DB_PORT', '5432'),
            'OPTIONS': {
                'MAX_CONNS': 20,
                'CONN_MAX_AGE': 600,
            }
        }
        
        # Add to Django database connections
        connections.databases['postgresql'] = postgresql_config
        
        # Test connection
        postgresql_conn = connections['postgresql']
        with postgresql_conn.cursor() as cursor:
            cursor.execute("SELECT version()")
            version = cursor.fetchone()[0]
            print(f"    ✅ PostgreSQL connected: {version.split()[1]}")
    
    def create_postgresql_indexes(self):
        """Create PostgreSQL indexes"""
        
        postgresql_conn = connections['postgresql']
        
        with postgresql_conn.cursor() as cursor:
            # Create comprehensive indexes
            index_queries = [
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employee_dept_active ON ems_employee(department_id, is_active);",
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employee_hire_date ON ems_employee(hire_date DESC);",
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_activity_user_created ON ems_activity(user_id, created_at DESC);",
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leave_employee_status ON ems_leaverequest(employee_id, status);",
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_employee_date ON ems_attendance(employee_id, date DESC);"
            ]
            
            for query in index_queries:
                try:
                    cursor.execute(query)
                    print(f"    ✅ Created index: {query.split()[5]}")
                except Exception as e:
                    print(f"    ⚠️ Index creation warning: {e}")
    
    def create_dual_write_router(self):
        """Create dual-write database router"""
        
        router_code = '''
"""
CRITICAL FIX: Dual-Write Database Router
Routes writes to both SQLite and PostgreSQL during migration
"""

class DualWriteRouter:
    def db_for_read(self, model, **hints):
        """Route reads to SQLite during migration"""
        return 'default'  # SQLite
    
    def db_for_write(self, model, **hints):
        """Route writes to both databases"""
        return 'default'  # Primary write to SQLite
    
    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """Allow migrations on both databases"""
        return True
    
    def allow_relation(self, obj1, obj2, **hints):
        """Allow relations between objects"""
        return True

# Custom save method for dual-write
def dual_write_save(self, *args, **kwargs):
    """Save to both SQLite and PostgreSQL"""
    
    # Save to SQLite (primary)
    result = self._original_save(*args, **kwargs)
    
    # Save to PostgreSQL (secondary)
    try:
        using_postgresql = kwargs.copy()
        using_postgresql['using'] = 'postgresql'
        self._original_save(*using_postgresql)
    except Exception as e:
        # Log error but don't fail the primary save
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Dual-write to PostgreSQL failed: {e}")
    
    return result
'''
        
        # Save router to file
        router_file = Path('backend/dual_write_router.py')
        with open(router_file, 'w') as f:
            f.write(router_code)
        
        print("    ✅ Dual-write router created")
    
    def enable_dual_write_settings(self):
        """Enable dual-write in Django settings"""
        
        # This would typically be done through environment variables
        # or configuration management in a real deployment
        print("    ⚠️ Manual step: Add dual-write router to Django settings")
        print("    📝 Add to DATABASES: 'postgresql': {...}")
        print("    📝 Add to DATABASE_ROUTERS: ['backend.dual_write_router.DualWriteRouter']")
    
    def migrate_data_in_background(self):
        """Migrate data in background thread"""
        
        try:
            models = apps.get_models()
            total_models = len(models)
            
            for i, model in enumerate(models):
                print(f"    🔄 Migrating {model.__name__}...")
                
                # Get all records from SQLite
                sqlite_records = model.objects.using('default').all()
                
                # Bulk create in PostgreSQL
                batch_size = 1000
                records_list = list(sqlite_records)
                
                for j in range(0, len(records_list), batch_size):
                    batch = records_list[j:j + batch_size]
                    
                    # Clear primary keys for bulk create
                    for record in batch:
                        record.pk = None
                    
                    model.objects.using('postgresql').bulk_create(batch, ignore_conflicts=True)
                
                # Update progress
                progress = int((i + 1) / total_models * 30) + 40  # 40-70% range
                self.update_status("phase_3_migration", progress, f"Migrated {model.__name__}")
                
        except Exception as e:
            self.migration_status["errors"].append(f"Background migration failed: {e}")
            raise
    
    def validate_data_consistency(self):
        """Validate data consistency between databases"""
        
        consistency_results = {
            "success": True,
            "errors": [],
            "warnings": []
        }
        
        models = apps.get_models()
        
        for model in models:
            try:
                sqlite_count = model.objects.using('default').count()
                postgresql_count = model.objects.using('postgresql').count()
                
                if sqlite_count != postgresql_count:
                    error_msg = f"{model.__name__}: SQLite={sqlite_count}, PostgreSQL={postgresql_count}"
                    consistency_results["errors"].append(error_msg)
                    consistency_results["success"] = False
                else:
                    print(f"    ✅ {model.__name__}: {sqlite_count} records")
                    
            except Exception as e:
                warning_msg = f"{model.__name__}: Validation failed - {e}"
                consistency_results["warnings"].append(warning_msg)
        
        return consistency_results
    
    def switch_reads_to_postgresql(self):
        """Switch read operations to PostgreSQL"""
        
        # This would typically involve updating the database router
        # or configuration to route reads to PostgreSQL
        print("    ⚠️ Manual step: Update database router to read from PostgreSQL")
        print("    📝 Modify db_for_read() to return 'postgresql'")
    
    def disable_dual_write_mode(self):
        """Disable dual-write mode"""
        
        self.dual_write_active = False
        print("    ⚠️ Manual step: Remove dual-write router from settings")
        print("    📝 Update DATABASE_ROUTERS to use standard router")
    
    def optimize_postgresql(self):
        """Optimize PostgreSQL after migration"""
        
        postgresql_conn = connections['postgresql']
        
        with postgresql_conn.cursor() as cursor:
            # Analyze tables for better query planning
            cursor.execute("ANALYZE;")
            print("    ✅ Database statistics updated")
            
            # Vacuum to reclaim space
            cursor.execute("VACUUM;")
            print("    ✅ Database vacuumed")
    
    def create_sqlite_backup(self):
        """Create SQLite backup for rollback"""
        
        import shutil
        
        backup_name = f"db_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sqlite3"
        shutil.copy2('db.sqlite3', backup_name)
        
        print(f"    💾 SQLite backup created: {backup_name}")
    
    def update_status(self, phase, progress, step):
        """Update migration status"""
        
        self.migration_status.update({
            "phase": phase,
            "progress": progress,
            "current_step": step
        })
    
    def rollback_migration(self):
        """Rollback migration in case of failure"""
        
        print("\n🔄 Rolling back migration...")
        
        try:
            # Disable dual-write mode
            if self.dual_write_active:
                self.disable_dual_write_mode()
            
            # Restore original settings
            print("  🔄 Restoring original database settings...")
            
            # Remove PostgreSQL database configuration
            if 'postgresql' in connections.databases:
                del connections.databases['postgresql']
            
            print("  ✅ Rollback completed")
            
        except Exception as e:
            print(f"  ❌ Rollback failed: {e}")

if __name__ == '__main__':
    migrator = ZeroDowntimeMigrator()
    success = migrator.execute_zero_downtime_migration()
    sys.exit(0 if success else 1)
