/**
 * CRITICAL FIX: Multi-Factor Authentication Setup Component
 * Enterprise-grade MFA setup with QR code and backup codes
 */

import React, { useState, useEffect } from 'react'
import { apiClient } from '../services/api'

interface MFASetupData {
  qr_code: string
  manual_entry_key: string
  backup_codes: string[]
  message: string
}

interface MFAStatus {
  mfa_enabled: boolean
  enabled_at?: string
  backup_codes_remaining?: number
}

const MFASetup: React.FC = () => {
  const [mfaStatus, setMfaStatus] = useState<MFAStatus | null>(null)
  const [setupData, setSetupData] = useState<MFASetupData | null>(null)
  const [verificationCode, setVerificationCode] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [step, setStep] = useState<'status' | 'setup' | 'verify' | 'disable'>('status')
  const [showBackupCodes, setShowBackupCodes] = useState(false)

  useEffect(() => {
    fetchMFAStatus()
  }, [])

  const fetchMFAStatus = async () => {
    try {
      const response = await apiClient.get('/api/auth/mfa/status/')
      setMfaStatus(response.data)
    } catch (error) {
      console.error('Failed to fetch MFA status:', error)
      setError('Failed to load MFA status')
    }
  }

  const initiateMFASetup = async () => {
    setLoading(true)
    setError('')
    
    try {
      const response = await apiClient.post('/api/auth/mfa/setup/')
      setSetupData(response.data)
      setStep('setup')
      setSuccess('MFA setup initiated. Scan the QR code with your authenticator app.')
    } catch (error: any) {
      setError(error.response?.data?.error || 'Failed to setup MFA')
    } finally {
      setLoading(false)
    }
  }

  const verifyMFASetup = async () => {
    if (!verificationCode.trim()) {
      setError('Please enter the verification code')
      return
    }

    setLoading(true)
    setError('')

    try {
      const response = await apiClient.post('/api/auth/mfa/verify-setup/', {
        code: verificationCode
      })
      
      setSuccess('MFA has been successfully enabled!')
      setShowBackupCodes(true)
      setStep('status')
      await fetchMFAStatus()
    } catch (error: any) {
      setError(error.response?.data?.error || 'Invalid verification code')
    } finally {
      setLoading(false)
    }
  }

  const disableMFA = async () => {
    if (!password.trim()) {
      setError('Please enter your password')
      return
    }

    setLoading(true)
    setError('')

    try {
      await apiClient.post('/api/auth/mfa/disable/', {
        password: password
      })
      
      setSuccess('MFA has been disabled')
      setStep('status')
      setPassword('')
      await fetchMFAStatus()
    } catch (error: any) {
      setError(error.response?.data?.error || 'Failed to disable MFA')
    } finally {
      setLoading(false)
    }
  }

  const downloadBackupCodes = () => {
    if (!setupData?.backup_codes) return

    const content = [
      'EMS Multi-Factor Authentication Backup Codes',
      '===========================================',
      '',
      'IMPORTANT: Store these codes in a safe place.',
      'Each code can only be used once.',
      '',
      ...setupData.backup_codes.map((code, index) => `${index + 1}. ${code}`),
      '',
      'Generated on: ' + new Date().toLocaleString()
    ].join('\n')

    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'ems-mfa-backup-codes.txt'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setSuccess('Copied to clipboard!')
      setTimeout(() => setSuccess(''), 3000)
    })
  }

  const renderMFAStatus = () => (
    <div className="mfa-status">
      <h3>Multi-Factor Authentication</h3>
      
      {mfaStatus?.mfa_enabled ? (
        <div className="mfa-enabled">
          <div className="status-indicator enabled">
            <span className="icon">🔒</span>
            <span>MFA is enabled</span>
          </div>
          
          {mfaStatus.enabled_at && (
            <p className="enabled-date">
              Enabled on: {new Date(mfaStatus.enabled_at).toLocaleDateString()}
            </p>
          )}
          
          {mfaStatus.backup_codes_remaining !== undefined && (
            <p className="backup-codes-info">
              Backup codes remaining: {mfaStatus.backup_codes_remaining}
            </p>
          )}
          
          <button 
            onClick={() => setStep('disable')}
            className="btn btn-danger"
          >
            Disable MFA
          </button>
        </div>
      ) : (
        <div className="mfa-disabled">
          <div className="status-indicator disabled">
            <span className="icon">🔓</span>
            <span>MFA is not enabled</span>
          </div>
          
          <p className="security-recommendation">
            We recommend enabling MFA to secure your account.
          </p>
          
          <button 
            onClick={initiateMFASetup}
            disabled={loading}
            className="btn btn-primary"
          >
            {loading ? 'Setting up...' : 'Enable MFA'}
          </button>
        </div>
      )}
    </div>
  )

  const renderMFASetup = () => (
    <div className="mfa-setup">
      <h3>Setup Multi-Factor Authentication</h3>
      
      <div className="setup-steps">
        <div className="step">
          <h4>Step 1: Scan QR Code</h4>
          <p>Use your authenticator app (Google Authenticator, Authy, etc.) to scan this QR code:</p>
          
          {setupData?.qr_code && (
            <div className="qr-code-container">
              <img src={setupData.qr_code} alt="MFA QR Code" className="qr-code" />
            </div>
          )}
        </div>
        
        <div className="step">
          <h4>Step 2: Manual Entry (Alternative)</h4>
          <p>If you can't scan the QR code, enter this key manually:</p>
          
          {setupData?.manual_entry_key && (
            <div className="manual-key">
              <code>{setupData.manual_entry_key}</code>
              <button 
                onClick={() => copyToClipboard(setupData.manual_entry_key)}
                className="btn btn-sm btn-secondary"
              >
                Copy
              </button>
            </div>
          )}
        </div>
        
        <div className="step">
          <h4>Step 3: Verify Setup</h4>
          <p>Enter the 6-digit code from your authenticator app:</p>
          
          <div className="verification-form">
            <input
              type="text"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
              placeholder="000000"
              className="form-control verification-input"
              maxLength={6}
            />
            
            <button 
              onClick={verifyMFASetup}
              disabled={loading || verificationCode.length !== 6}
              className="btn btn-primary"
            >
              {loading ? 'Verifying...' : 'Verify & Enable MFA'}
            </button>
          </div>
        </div>
        
        {setupData?.backup_codes && (
          <div className="step">
            <h4>Step 4: Save Backup Codes</h4>
            <p>Save these backup codes in a secure location. Each can only be used once:</p>
            
            <div className="backup-codes">
              {setupData.backup_codes.map((code, index) => (
                <div key={index} className="backup-code">
                  <span className="code-number">{index + 1}.</span>
                  <code>{code}</code>
                </div>
              ))}
            </div>
            
            <button 
              onClick={downloadBackupCodes}
              className="btn btn-secondary"
            >
              Download Backup Codes
            </button>
          </div>
        )}
      </div>
      
      <button 
        onClick={() => setStep('status')}
        className="btn btn-link"
      >
        Cancel Setup
      </button>
    </div>
  )

  const renderMFADisable = () => (
    <div className="mfa-disable">
      <h3>Disable Multi-Factor Authentication</h3>
      
      <div className="warning">
        <span className="icon">⚠️</span>
        <p>
          Disabling MFA will make your account less secure. 
          You will need to enter your password to confirm.
        </p>
      </div>
      
      <div className="disable-form">
        <div className="form-group">
          <label htmlFor="password">Current Password:</label>
          <input
            type="password"
            id="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="form-control"
            placeholder="Enter your current password"
          />
        </div>
        
        <div className="form-actions">
          <button 
            onClick={disableMFA}
            disabled={loading || !password.trim()}
            className="btn btn-danger"
          >
            {loading ? 'Disabling...' : 'Disable MFA'}
          </button>
          
          <button 
            onClick={() => {
              setStep('status')
              setPassword('')
            }}
            className="btn btn-secondary"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  )

  return (
    <div className="mfa-container">
      {error && (
        <div className="alert alert-danger">
          <span className="icon">❌</span>
          {error}
        </div>
      )}
      
      {success && (
        <div className="alert alert-success">
          <span className="icon">✅</span>
          {success}
        </div>
      )}
      
      {step === 'status' && renderMFAStatus()}
      {step === 'setup' && renderMFASetup()}
      {step === 'disable' && renderMFADisable()}
      
      {showBackupCodes && setupData?.backup_codes && (
        <div className="backup-codes-modal">
          <div className="modal-content">
            <h4>Important: Save Your Backup Codes</h4>
            <p>These codes can be used to access your account if you lose your authenticator device:</p>
            
            <div className="backup-codes">
              {setupData.backup_codes.map((code, index) => (
                <div key={index} className="backup-code">
                  <span className="code-number">{index + 1}.</span>
                  <code>{code}</code>
                </div>
              ))}
            </div>
            
            <div className="modal-actions">
              <button onClick={downloadBackupCodes} className="btn btn-primary">
                Download Codes
              </button>
              <button 
                onClick={() => setShowBackupCodes(false)} 
                className="btn btn-secondary"
              >
                I've Saved Them
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default MFASetup
