"""
DATA CLEANUP: Django Management Command for Database Cleanup
Usage: python manage.py cleanup_data [--dry-run]
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth.models import User
from django.db.models import Q, Count
from ems.models import Employee, Department

class Command(BaseCommand):
    help = 'Clean up orphaned data, test data, and fix data quality issues'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be cleaned up without making changes',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Actually perform the cleanup (use with caution)',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run'] or not options['force']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('=== DRY RUN MODE - NO CHANGES WILL BE MADE ===')
            )
        else:
            self.stdout.write(
                self.style.ERROR('=== LIVE CLEANUP MODE - CHANGES WILL BE MADE ===')
            )

        self.cleanup_orphaned_users(dry_run)
        self.cleanup_test_data(dry_run)
        self.fix_data_quality_issues(dry_run)
        self.cleanup_duplicate_data(dry_run)
        self.validate_data_integrity()

        if dry_run:
            self.stdout.write(
                self.style.SUCCESS('\n=== DRY RUN COMPLETE - Use --force to apply changes ===')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('\n=== CLEANUP COMPLETE ===')
            )

    def cleanup_orphaned_users(self, dry_run=True):
        """Clean up orphaned user records"""
        self.stdout.write('\n1. CLEANING UP ORPHANED USERS:')
        
        # Find users without employee records (excluding staff/superusers)
        orphaned_users = User.objects.exclude(employee__isnull=False).exclude(
            Q(is_staff=True) | Q(is_superuser=True)
        )
        
        self.stdout.write(f'Found {orphaned_users.count()} orphaned users')
        
        deleted_count = 0
        for user in orphaned_users:
            self.stdout.write(f'  - {user.username} ({user.email}) - Active: {user.is_active}')
            
            # Only delete inactive orphaned users
            if not user.is_active:
                if not dry_run:
                    user.delete()
                    deleted_count += 1
                self.stdout.write('    → Would delete (inactive orphaned user)')
            else:
                self.stdout.write('    → Needs manual review (active user without employee)')
        
        if not dry_run and deleted_count > 0:
            self.stdout.write(f'Deleted {deleted_count} orphaned users')

    def cleanup_test_data(self, dry_run=True):
        """Remove test and placeholder data"""
        self.stdout.write('\n2. CLEANING UP TEST DATA:')
        
        # Find test employees
        test_employees = Employee.objects.filter(
            Q(user__first_name__icontains='test') | 
            Q(user__last_name__icontains='test') |
            Q(position__icontains='test') |
            Q(employee_id__icontains='test')
        )
        
        self.stdout.write(f'Found {test_employees.count()} test employees')
        
        deleted_count = 0
        for emp in test_employees:
            self.stdout.write(f'  - {emp.user.get_full_name()} ({emp.position})')
            if not dry_run:
                user = emp.user
                emp.delete()
                user.delete()
                deleted_count += 1
            self.stdout.write('    → Would delete test employee and user')
        
        # Fix placeholder data instead of deleting
        placeholder_employees = Employee.objects.filter(
            Q(phone='1234567890') |
            Q(phone__startswith='123') |
            Q(address__icontains='test') |
            Q(address__icontains='placeholder') |
            Q(national_id__icontains='test')
        )
        
        self.stdout.write(f'Found {placeholder_employees.count()} employees with placeholder data')
        
        fixed_count = 0
        for emp in placeholder_employees:
            self.stdout.write(f'  - {emp.user.get_full_name()} - Phone: {emp.phone}')
            
            if not dry_run:
                # Fix placeholder data
                if emp.phone in ['1234567890'] or emp.phone.startswith('123'):
                    emp.phone = '+966501234567'
                
                if emp.address and ('test' in emp.address.lower() or 'placeholder' in emp.address.lower()):
                    emp.address = 'الرياض، المملكة العربية السعودية'
                
                if emp.national_id and 'test' in emp.national_id.lower():
                    emp.national_id = f'ID{emp.id:06d}'
                
                emp.save()
                fixed_count += 1
            
            self.stdout.write('    → Would fix placeholder data')
        
        if not dry_run:
            if deleted_count > 0:
                self.stdout.write(f'Deleted {deleted_count} test employees')
            if fixed_count > 0:
                self.stdout.write(f'Fixed {fixed_count} employees with placeholder data')

    def fix_data_quality_issues(self, dry_run=True):
        """Fix data quality issues"""
        self.stdout.write('\n3. FIXING DATA QUALITY ISSUES:')
        
        # Fix missing Arabic names
        missing_arabic = Employee.objects.filter(
            Q(first_name_ar__isnull=True) | Q(first_name_ar='') |
            Q(last_name_ar__isnull=True) | Q(last_name_ar='')
        )
        
        self.stdout.write(f'Found {missing_arabic.count()} employees missing Arabic names')
        
        # Arabic name mappings
        arabic_names = {
            'john': 'جون', 'jane': 'جين', 'ahmed': 'أحمد', 'mohammed': 'محمد',
            'ali': 'علي', 'fatima': 'فاطمة', 'omar': 'عمر', 'sara': 'سارة',
            'hassan': 'حسن', 'layla': 'ليلى', 'khalid': 'خالد', 'noor': 'نور',
            'smith': 'سميث', 'johnson': 'جونسون', 'brown': 'براون', 'davis': 'ديفيس'
        }
        
        fixed_arabic_count = 0
        for emp in missing_arabic:
            if not emp.first_name_ar:
                english_name = emp.user.first_name.lower()
                emp.first_name_ar = arabic_names.get(english_name, emp.user.first_name)
            
            if not emp.last_name_ar:
                english_surname = emp.user.last_name.lower()
                emp.last_name_ar = arabic_names.get(english_surname, emp.user.last_name)
            
            if not dry_run:
                emp.save()
                fixed_arabic_count += 1
            
            self.stdout.write(f'  - Would fix Arabic names for {emp.user.get_full_name()}')
        
        # Fix invalid salaries
        bad_salaries = Employee.objects.filter(Q(salary__lte=0) | Q(salary__isnull=True))
        self.stdout.write(f'Found {bad_salaries.count()} employees with invalid salaries')
        
        default_salaries = {
            'manager': 15000, 'developer': 8000, 'analyst': 7000,
            'coordinator': 6000, 'assistant': 5000, 'intern': 3000
        }
        
        fixed_salary_count = 0
        for emp in bad_salaries:
            position_lower = emp.position.lower()
            default_salary = 6000  # Default
            
            for pos, salary in default_salaries.items():
                if pos in position_lower:
                    default_salary = salary
                    break
            
            if not dry_run:
                emp.salary = default_salary
                emp.save()
                fixed_salary_count += 1
            
            self.stdout.write(f'  - Would fix salary for {emp.user.get_full_name()}: {default_salary}')
        
        if not dry_run:
            if fixed_arabic_count > 0:
                self.stdout.write(f'Fixed Arabic names for {fixed_arabic_count} employees')
            if fixed_salary_count > 0:
                self.stdout.write(f'Fixed salaries for {fixed_salary_count} employees')

    def cleanup_duplicate_data(self, dry_run=True):
        """Remove duplicate data"""
        self.stdout.write('\n4. CLEANING UP DUPLICATE DATA:')
        
        # Find duplicate departments
        duplicate_depts = Department.objects.values('name').annotate(
            count=Count('name')
        ).filter(count__gt=1)
        
        self.stdout.write(f'Found {duplicate_depts.count()} duplicate department names')
        
        merged_count = 0
        for dup in duplicate_depts:
            dept_name = dup['name']
            departments = Department.objects.filter(name=dept_name).order_by('id')
            
            primary_dept = departments.first()
            duplicate_depts_to_merge = departments[1:]
            
            self.stdout.write(f'  - Merging duplicates for department: {dept_name}')
            
            for dept in duplicate_depts_to_merge:
                employees_to_move = Employee.objects.filter(department=dept)
                
                if not dry_run:
                    employees_to_move.update(department=primary_dept)
                    dept.delete()
                    merged_count += 1
                
                self.stdout.write(f'    → Would move {employees_to_move.count()} employees and delete duplicate')
        
        if not dry_run and merged_count > 0:
            self.stdout.write(f'Merged {merged_count} duplicate departments')

    def validate_data_integrity(self):
        """Validate data integrity"""
        self.stdout.write('\n5. VALIDATING DATA INTEGRITY:')
        
        # Check remaining issues
        orphaned_users = User.objects.exclude(employee__isnull=False).exclude(
            Q(is_staff=True) | Q(is_superuser=True)
        )
        orphaned_employees = Employee.objects.filter(user__isnull=True)
        
        self.stdout.write(f'Remaining orphaned users (non-staff): {orphaned_users.count()}')
        self.stdout.write(f'Remaining orphaned employees: {orphaned_employees.count()}')
        
        employees_missing_data = Employee.objects.filter(
            Q(first_name_ar__isnull=True) | Q(first_name_ar='') |
            Q(salary__lte=0) | Q(salary__isnull=True)
        )
        self.stdout.write(f'Employees missing required data: {employees_missing_data.count()}')
        
        remaining_test_data = Employee.objects.filter(
            Q(user__first_name__icontains='test') | 
            Q(position__icontains='test')
        )
        self.stdout.write(f'Remaining test data: {remaining_test_data.count()}')
        
        if (orphaned_users.count() == 0 and orphaned_employees.count() == 0 and 
            employees_missing_data.count() == 0 and remaining_test_data.count() == 0):
            self.stdout.write(self.style.SUCCESS('✅ Data integrity validation PASSED'))
        else:
            self.stdout.write(self.style.WARNING('⚠️ Data integrity validation found issues'))
