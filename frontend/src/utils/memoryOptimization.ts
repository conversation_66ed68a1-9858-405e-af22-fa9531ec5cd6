/**
 * MEMORY OPTIMIZATION UTILITIES
 * Tools to reduce memory usage and optimize bundle size
 */

import React, { lazy, ComponentType, Suspense } from 'react'

/**
 * Memory usage monitoring
 */
export class MemoryMonitor {
  private static instance: MemoryMonitor
  private memoryBaseline: number = 0
  private checkInterval: NodeJS.Timeout | null = null

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor()
    }
    return MemoryMonitor.instance
  }

  /**
   * Get current memory usage
   */
  getCurrentMemoryUsage(): number {
    if (performance.memory) {
      return Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) // MB
    }
    return 0
  }

  /**
   * Set memory baseline for comparison
   */
  setBaseline(): void {
    this.memoryBaseline = this.getCurrentMemoryUsage()
    console.log(`🧠 Memory baseline set: ${this.memoryBaseline}MB`)
  }

  /**
   * Get memory usage since baseline
   */
  getMemoryDelta(): number {
    return this.getCurrentMemoryUsage() - this.memoryBaseline
  }

  /**
   * Check for memory leaks
   */
  checkForLeaks(): boolean {
    const currentUsage = this.getCurrentMemoryUsage()
    const delta = this.getMemoryDelta()
    
    // Consider it a potential leak if memory increased by more than 50MB
    if (delta > 50) {
      console.warn(`⚠️ Potential memory leak detected: +${delta}MB since baseline`)
      return true
    }
    
    return false
  }

  /**
   * Start periodic memory monitoring
   */
  startMonitoring(intervalMs: number = 30000): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
    }

    this.setBaseline()
    
    this.checkInterval = setInterval(() => {
      const usage = this.getCurrentMemoryUsage()
      const delta = this.getMemoryDelta()
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`🧠 Memory: ${usage}MB (Δ${delta > 0 ? '+' : ''}${delta}MB)`)
      }
      
      this.checkForLeaks()
    }, intervalMs)
  }

  /**
   * Stop memory monitoring
   */
  stopMonitoring(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }
  }
}

/**
 * Lazy loading utilities for heavy components
 */
export class LazyLoader {
  /**
   * Create a lazy-loaded component with loading fallback
   */
  static createLazyComponent<T extends ComponentType<any>>(
    importFn: () => Promise<{ default: T }>,
    fallback?: React.ReactNode
  ): ComponentType<any> {
    const LazyComponent = lazy(importFn)

    return (props: any) => React.createElement(
      Suspense,
      {
        fallback: fallback || React.createElement('div', {
          className: 'animate-pulse bg-white/10 rounded-lg h-32'
        })
      },
      React.createElement(LazyComponent, props)
    )
  }

  /**
   * Lazy load chart components
   */
  static createLazyChart(chartType: 'line' | 'bar' | 'pie' | 'area') {
    return this.createLazyComponent(
      () => import(`../components/charts/Chart${chartType.charAt(0).toUpperCase() + chartType.slice(1)}`),
      React.createElement('div', {
        className: 'animate-pulse bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg h-64 flex items-center justify-center'
      }, React.createElement('span', {
        className: 'text-white/60'
      }, 'Loading chart...'))
    )
  }
}

/**
 * Bundle size optimization utilities
 */
export class BundleOptimizer {
  /**
   * Preload critical resources
   */
  static preloadResource(href: string, as: string): void {
    if (typeof document !== 'undefined') {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = href
      link.as = as
      document.head.appendChild(link)
    }
  }

  /**
   * Remove unused preloaded resources
   */
  static cleanupUnusedPreloads(): void {
    if (typeof document !== 'undefined') {
      const preloadLinks = document.querySelectorAll('link[rel="preload"]')
      preloadLinks.forEach(link => {
        const href = link.getAttribute('href')
        if (href && !document.querySelector(`[src="${href}"], [href="${href}"]`)) {
          link.remove()
          console.debug(`🧹 Removed unused preload: ${href}`)
        }
      })
    }
  }

  /**
   * Optimize images by lazy loading
   */
  static optimizeImages(): void {
    if (typeof document !== 'undefined' && 'IntersectionObserver' in window) {
      const images = document.querySelectorAll('img[data-src]')
      
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            const src = img.getAttribute('data-src')
            if (src) {
              img.src = src
              img.removeAttribute('data-src')
              imageObserver.unobserve(img)
            }
          }
        })
      })

      images.forEach(img => imageObserver.observe(img))
    }
  }
}

/**
 * Memory cleanup utilities
 */
export class MemoryCleanup {
  /**
   * Clear all caches
   */
  static clearAllCaches(): void {
    // Clear localStorage cache items
    if (typeof localStorage !== 'undefined') {
      const cacheKeys = Object.keys(localStorage).filter(key => key.startsWith('cache_'))
      cacheKeys.forEach(key => localStorage.removeItem(key))
      console.log(`🧹 Cleared ${cacheKeys.length} cache items from localStorage`)
    }

    // Clear sessionStorage
    if (typeof sessionStorage !== 'undefined') {
      sessionStorage.clear()
      console.log('🧹 Cleared sessionStorage')
    }
  }

  /**
   * Force garbage collection (if available)
   */
  static forceGarbageCollection(): void {
    if (window.gc) {
      window.gc()
      console.log('🗑️ Forced garbage collection')
    }
  }

  /**
   * Clean up DOM nodes
   */
  static cleanupDOMNodes(): void {
    // Remove empty text nodes
    const walker = document.createTreeWalker(
      document.body,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: (node) => {
          return node.textContent?.trim() === '' ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT
        }
      }
    )

    const emptyNodes: Node[] = []
    let node
    while (node = walker.nextNode()) {
      emptyNodes.push(node)
    }

    emptyNodes.forEach(node => node.remove())
    console.log(`🧹 Removed ${emptyNodes.length} empty text nodes`)
  }
}

// Export singleton instance
export const memoryMonitor = MemoryMonitor.getInstance()

// PERFORMANCE OPTIMIZATION: Only start memory monitoring if needed
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  const shouldMonitor = window.location.search.includes('debug=memory') ||
                       localStorage.getItem('enableMemoryMonitoring') === 'true'

  if (shouldMonitor) {
    memoryMonitor.startMonitoring(5 * 60 * 1000) // Check every 5 minutes instead of 1 minute
    console.log('🧠 Memory monitoring enabled (debug mode)')
  }
}
