#!/usr/bin/env node

/**
 * Bundle Analysis Script
 * Analyzes the current project structure and dependencies for optimization opportunities
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getDirectorySize(dirPath) {
  let totalSize = 0;
  
  function calculateSize(currentPath) {
    const stats = fs.statSync(currentPath);
    
    if (stats.isFile()) {
      totalSize += stats.size;
    } else if (stats.isDirectory()) {
      const files = fs.readdirSync(currentPath);
      files.forEach(file => {
        calculateSize(path.join(currentPath, file));
      });
    }
  }
  
  try {
    calculateSize(dirPath);
  } catch (error) {
    // Directory doesn't exist or can't be read
    return 0;
  }
  
  return totalSize;
}

function analyzePackageJson() {
  log('\n📦 Package Analysis', 'cyan');
  log('='.repeat(50), 'cyan');

  const packagePath = path.join(path.dirname(__dirname), 'package.json');
  if (!fs.existsSync(packagePath)) {
    log('❌ package.json not found', 'red');
    return;
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  const dependencies = Object.keys(packageJson.dependencies || {});
  const devDependencies = Object.keys(packageJson.devDependencies || {});
  
  log(`📋 Dependencies: ${dependencies.length}`, 'green');
  log(`🔧 Dev Dependencies: ${devDependencies.length}`, 'yellow');
  
  // Analyze heavy dependencies
  const heavyDeps = [
    '@radix-ui/react-accordion',
    '@radix-ui/react-alert-dialog',
    '@radix-ui/react-avatar',
    '@radix-ui/react-checkbox',
    '@radix-ui/react-dialog',
    '@radix-ui/react-dropdown-menu',
    '@radix-ui/react-label',
    '@radix-ui/react-popover',
    '@radix-ui/react-progress',
    '@radix-ui/react-scroll-area',
    '@radix-ui/react-select',
    '@radix-ui/react-separator',
    '@radix-ui/react-slider',
    '@radix-ui/react-switch',
    '@radix-ui/react-tabs',
    '@radix-ui/react-toast',
    '@radix-ui/react-tooltip',
    'framer-motion',
    'recharts',
    'react-hook-form',
    'react-router-dom',
    'lucide-react'
  ];
  
  const foundHeavyDeps = heavyDeps.filter(dep => dependencies.includes(dep));
  
  if (foundHeavyDeps.length > 0) {
    log('\n⚠️  Heavy Dependencies Found:', 'yellow');
    foundHeavyDeps.forEach(dep => {
      log(`  • ${dep}`, 'yellow');
    });
  }
  
  return { dependencies, devDependencies, heavyDeps: foundHeavyDeps };
}

function analyzeDirectoryStructure() {
  log('\n📁 Directory Structure Analysis', 'cyan');
  log('='.repeat(50), 'cyan');
  
  const directories = [
    'node_modules',
    'src',
    'dist',
    'public',
    '.next',
    'build'
  ];
  
  directories.forEach(dir => {
    const dirPath = path.join(path.dirname(__dirname), dir);
    if (fs.existsSync(dirPath)) {
      const size = getDirectorySize(dirPath);
      const color = size > 100 * 1024 * 1024 ? 'red' : size > 10 * 1024 * 1024 ? 'yellow' : 'green';
      log(`📂 ${dir.padEnd(15)} ${formatBytes(size)}`, color);
    } else {
      log(`📂 ${dir.padEnd(15)} Not found`, 'magenta');
    }
  });
}

function analyzeSourceCode() {
  log('\n💻 Source Code Analysis', 'cyan');
  log('='.repeat(50), 'cyan');
  
  const srcPath = path.join(path.dirname(__dirname), 'src');
  if (!fs.existsSync(srcPath)) {
    log('❌ src directory not found', 'red');
    return;
  }
  
  let totalFiles = 0;
  let totalLines = 0;
  const fileTypes = {};
  
  function analyzeDirectory(dirPath) {
    const files = fs.readdirSync(dirPath);
    
    files.forEach(file => {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        analyzeDirectory(filePath);
      } else {
        totalFiles++;
        const ext = path.extname(file);
        fileTypes[ext] = (fileTypes[ext] || 0) + 1;
        
        // Count lines for text files
        if (['.ts', '.tsx', '.js', '.jsx', '.css', '.scss'].includes(ext)) {
          try {
            const content = fs.readFileSync(filePath, 'utf8');
            totalLines += content.split('\n').length;
          } catch (error) {
            // Skip files that can't be read
          }
        }
      }
    });
  }
  
  analyzeDirectory(srcPath);
  
  log(`📄 Total Files: ${totalFiles}`, 'green');
  log(`📝 Total Lines: ${totalLines.toLocaleString()}`, 'green');
  
  log('\n📊 File Types:', 'blue');
  Object.entries(fileTypes)
    .sort(([,a], [,b]) => b - a)
    .forEach(([ext, count]) => {
      log(`  ${ext || 'no extension'}: ${count}`, 'blue');
    });
}

function analyzeImports() {
  log('\n🔗 Import Analysis', 'cyan');
  log('='.repeat(50), 'cyan');
  
  const srcPath = path.join(path.dirname(__dirname), 'src');
  if (!fs.existsSync(srcPath)) {
    log('❌ src directory not found', 'red');
    return;
  }
  
  const imports = new Map();
  const heavyImports = [];
  
  function analyzeFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g;
      let match;
      
      while ((match = importRegex.exec(content)) !== null) {
        const importPath = match[1];
        imports.set(importPath, (imports.get(importPath) || 0) + 1);
        
        // Check for heavy imports
        if (importPath.includes('framer-motion') || 
            importPath.includes('recharts') || 
            importPath.includes('@radix-ui') ||
            importPath.includes('lucide-react')) {
          heavyImports.push({ file: filePath, import: importPath });
        }
      }
    } catch (error) {
      // Skip files that can't be read
    }
  }
  
  function scanDirectory(dirPath) {
    const files = fs.readdirSync(dirPath);
    
    files.forEach(file => {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        scanDirectory(filePath);
      } else if (['.ts', '.tsx', '.js', '.jsx'].includes(path.extname(file))) {
        analyzeFile(filePath);
      }
    });
  }
  
  scanDirectory(srcPath);
  
  // Show most used imports
  const sortedImports = Array.from(imports.entries())
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10);
  
  log('🔥 Most Used Imports:', 'green');
  sortedImports.forEach(([importPath, count]) => {
    log(`  ${importPath}: ${count} times`, 'green');
  });
  
  if (heavyImports.length > 0) {
    log('\n⚠️  Heavy Imports Found:', 'yellow');
    const uniqueHeavyImports = [...new Set(heavyImports.map(h => h.import))];
    uniqueHeavyImports.slice(0, 10).forEach(imp => {
      const count = heavyImports.filter(h => h.import === imp).length;
      log(`  ${imp}: ${count} files`, 'yellow');
    });
  }
}

function generateOptimizationRecommendations(analysis) {
  log('\n🚀 Optimization Recommendations', 'cyan');
  log('='.repeat(50), 'cyan');
  
  const recommendations = [];
  
  // Bundle size recommendations
  recommendations.push({
    category: '📦 Bundle Size',
    items: [
      'Implement code splitting with React.lazy()',
      'Use dynamic imports for heavy components',
      'Tree shake unused exports',
      'Optimize Radix UI imports (use specific components)',
      'Consider replacing heavy libraries with lighter alternatives'
    ]
  });
  
  // Performance recommendations
  recommendations.push({
    category: '⚡ Performance',
    items: [
      'Implement route-based code splitting',
      'Add service worker for caching',
      'Optimize images with modern formats (WebP, AVIF)',
      'Use React.memo for expensive components',
      'Implement virtual scrolling for large lists'
    ]
  });
  
  // Build optimization
  recommendations.push({
    category: '🔧 Build Optimization',
    items: [
      'Enable Vite build optimizations',
      'Configure proper chunk splitting',
      'Use compression (gzip/brotli)',
      'Optimize CSS with PurgeCSS',
      'Implement proper caching headers'
    ]
  });
  
  recommendations.forEach(({ category, items }) => {
    log(`\n${category}:`, 'green');
    items.forEach(item => {
      log(`  • ${item}`, 'white');
    });
  });
}

function main() {
  log('🔍 EMS Frontend Bundle Analysis', 'bright');
  log('='.repeat(60), 'bright');
  
  const analysis = {
    package: analyzePackageJson(),
    directories: analyzeDirectoryStructure(),
    sourceCode: analyzeSourceCode(),
    imports: analyzeImports()
  };
  
  generateOptimizationRecommendations(analysis);
  
  log('\n✅ Analysis Complete!', 'green');
  log('📊 Use this information to optimize your bundle size and performance.', 'blue');
}

// Run the analysis
main();
