# CRITICAL FIX: EMS Alert Rules for Production Monitoring

groups:
  - name: ems_critical_alerts
    rules:
      # Application Health Alerts
      - alert: EMSBackendDown
        expr: up{job="ems-backend"} == 0
        for: 1m
        labels:
          severity: critical
          service: backend
        annotations:
          summary: "EMS Backend is down"
          description: "EMS Backend has been down for more than 1 minute"

      - alert: EMSFrontendDown
        expr: up{job="ems-frontend"} == 0
        for: 2m
        labels:
          severity: critical
          service: frontend
        annotations:
          summary: "EMS Frontend is down"
          description: "EMS Frontend has been down for more than 2 minutes"

      # Database Alerts
      - alert: PostgreSQLDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
          service: database
        annotations:
          summary: "PostgreSQL database is down"
          description: "PostgreSQL database has been down for more than 1 minute"

      - alert: PostgreSQLHighConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "PostgreSQL connection usage is high"
          description: "PostgreSQL is using {{ $value }}% of available connections"

      - alert: PostgreSQLSlowQueries
        expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "PostgreSQL has slow queries"
          description: "PostgreSQL query efficiency is below 10%"

      # Redis Alerts
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: cache
        annotations:
          summary: "Redis cache is down"
          description: "Redis cache has been down for more than 1 minute"

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
          service: cache
        annotations:
          summary: "Redis memory usage is high"
          description: "Redis is using {{ $value }}% of available memory"

      - alert: RedisLowHitRate
        expr: rate(redis_keyspace_hits_total[5m]) / (rate(redis_keyspace_hits_total[5m]) + rate(redis_keyspace_misses_total[5m])) < 0.8
        for: 10m
        labels:
          severity: warning
          service: cache
        annotations:
          summary: "Redis cache hit rate is low"
          description: "Redis cache hit rate is {{ $value }}%"

  - name: ems_performance_alerts
    rules:
      # API Performance Alerts
      - alert: HighAPIResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="ems-backend"}[5m])) > 1
        for: 5m
        labels:
          severity: warning
          service: api
        annotations:
          summary: "High API response time"
          description: "95th percentile API response time is {{ $value }}s"

      - alert: HighAPIErrorRate
        expr: rate(http_requests_total{job="ems-backend",status=~"5.."}[5m]) / rate(http_requests_total{job="ems-backend"}[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
          service: api
        annotations:
          summary: "High API error rate"
          description: "API error rate is {{ $value }}%"

      - alert: RateLimitingTriggered
        expr: rate(http_requests_total{job="ems-backend",status="429"}[5m]) > 10
        for: 2m
        labels:
          severity: info
          service: security
        annotations:
          summary: "Rate limiting is being triggered"
          description: "Rate limiting is blocking {{ $value }} requests per second"

      # System Resource Alerts
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 10m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
        for: 10m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: LowDiskSpace
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes * 100 > 90
        for: 5m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "Low disk space"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }}"

  - name: ems_security_alerts
    rules:
      # Security Alerts
      - alert: HighFailedLoginAttempts
        expr: rate(http_requests_total{job="ems-backend",endpoint="/api/auth/login/",status="401"}[5m]) > 5
        for: 2m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "High number of failed login attempts"
          description: "{{ $value }} failed login attempts per second"

      - alert: SuspiciousAPIActivity
        expr: rate(http_requests_total{job="ems-backend"}[1m]) > 100
        for: 2m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "Suspicious API activity detected"
          description: "Unusually high API request rate: {{ $value }} requests per second"

      - alert: UnauthorizedAccessAttempts
        expr: rate(http_requests_total{job="ems-backend",status="403"}[5m]) > 10
        for: 5m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "High number of unauthorized access attempts"
          description: "{{ $value }} unauthorized access attempts per second"

  - name: ems_business_alerts
    rules:
      # Business Logic Alerts
      - alert: NoUserActivity
        expr: rate(http_requests_total{job="ems-backend",endpoint!~"/api/health/|/api/metrics/"}[1h]) == 0
        for: 30m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "No user activity detected"
          description: "No user activity for the past 30 minutes"

      - alert: DatabaseConnectionPoolExhaustion
        expr: pg_stat_activity_count > pg_settings_max_connections * 0.9
        for: 5m
        labels:
          severity: critical
          service: database
        annotations:
          summary: "Database connection pool near exhaustion"
          description: "{{ $value }} active connections out of {{ pg_settings_max_connections }} maximum"
