/**
 * FIXED: Loading State Manager to prevent layout shifts and improve UX
 * Provides consistent loading states across the application
 */

import React, { useEffect, useState, useRef } from 'react'
import { Loader2 } from 'lucide-react'

interface LoadingStateManagerProps {
  loading: boolean
  children: React.ReactNode
  minHeight?: number
  preserveLayout?: boolean
  showSpinner?: boolean
  delayMs?: number
  className?: string
  ariaLabel?: string
}

interface SkeletonProps {
  width?: string | number
  height?: string | number
  className?: string
  rounded?: boolean
}

// FIXED: Skeleton component for better loading UX
export const Skeleton: React.FC<SkeletonProps> = ({ 
  width = '100%', 
  height = '1rem', 
  className = '', 
  rounded = false 
}) => {
  return (
    <div
      className={`animate-pulse bg-white/10 ${rounded ? 'rounded-full' : 'rounded'} ${className}`}
      style={{ width, height }}
      aria-hidden="true"
    />
  )
}

// FIXED: Table skeleton for consistent loading states
export const TableSkeleton: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
}) => {
  return (
    <div className="space-y-3" aria-hidden="true">
      {/* Header skeleton */}
      <div className="flex space-x-4 p-4 bg-white/5 rounded-lg">
        {Array.from({ length: columns }).map((_, i) => (
          <Skeleton key={i} height="1.5rem" className="flex-1" />
        ))}
      </div>
      
      {/* Row skeletons */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4 p-4 border-b border-white/10">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton key={colIndex} height="1rem" className="flex-1" />
          ))}
        </div>
      ))}
    </div>
  )
}

// FIXED: Modal skeleton for consistent modal loading
export const ModalSkeleton: React.FC = () => {
  return (
    <div className="space-y-6 p-6" aria-hidden="true">
      <Skeleton height="2rem" width="60%" />
      
      <div className="space-y-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton height="1rem" width="30%" />
            <Skeleton height="2.5rem" />
          </div>
        ))}
      </div>
      
      <div className="flex justify-end space-x-2">
        <Skeleton height="2.5rem" width="5rem" />
        <Skeleton height="2.5rem" width="5rem" />
      </div>
    </div>
  )
}

// FIXED: Main loading state manager
export const LoadingStateManager: React.FC<LoadingStateManagerProps> = ({
  loading,
  children,
  minHeight = 200,
  preserveLayout = true,
  showSpinner = true,
  delayMs = 200,
  className = '',
  ariaLabel = 'Loading content'
}) => {
  const [showLoading, setShowLoading] = useState(false)
  const [dimensions, setDimensions] = useState<{ width: number; height: number } | null>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  // FIXED: Measure content dimensions when not loading
  useEffect(() => {
    if (!loading && contentRef.current && !dimensions) {
      const rect = contentRef.current.getBoundingClientRect()
      setDimensions({
        width: rect.width,
        height: Math.max(rect.height, minHeight)
      })
    }
  }, [loading, minHeight, dimensions])

  // FIXED: Delayed loading state to prevent flashing
  useEffect(() => {
    if (loading) {
      timeoutRef.current = setTimeout(() => {
        setShowLoading(true)
      }, delayMs)
    } else {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      setShowLoading(false)
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [loading, delayMs])

  if (loading && showLoading) {
    const containerStyle = preserveLayout && dimensions ? {
      width: dimensions.width,
      height: dimensions.height,
      minHeight
    } : {
      minHeight
    }

    return (
      <div
        className={`flex items-center justify-center ${className}`}
        style={containerStyle}
        role="status"
        aria-label={ariaLabel}
        aria-live="polite"
      >
        {showSpinner && (
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-white/60" aria-hidden="true" />
            <span className="text-white/60 text-sm">{ariaLabel}</span>
          </div>
        )}
      </div>
    )
  }

  return (
    <div ref={contentRef} className={className}>
      {children}
    </div>
  )
}

// FIXED: Hook for managing loading states with accessibility
export const useLoadingState = (initialLoading = false) => {
  const [loading, setLoading] = useState(initialLoading)
  const [error, setError] = useState<string | null>(null)
  
  const startLoading = () => {
    setLoading(true)
    setError(null)
  }
  
  const stopLoading = () => {
    setLoading(false)
  }
  
  const setLoadingError = (errorMessage: string) => {
    setLoading(false)
    setError(errorMessage)
  }
  
  return {
    loading,
    error,
    startLoading,
    stopLoading,
    setLoadingError,
    setLoading
  }
}

// FIXED: Error boundary for loading states
interface LoadingErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  onError?: (error: Error) => void
}

export class LoadingErrorBoundary extends React.Component<
  LoadingErrorBoundaryProps,
  { hasError: boolean; error?: Error }
> {
  constructor(props: LoadingErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Loading Error Boundary caught an error:', error, errorInfo)
    this.props.onError?.(error)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div 
          className="flex items-center justify-center min-h-[200px] text-white/60"
          role="alert"
          aria-live="assertive"
        >
          <div className="text-center space-y-2">
            <p>Something went wrong while loading.</p>
            <button
              onClick={() => this.setState({ hasError: false, error: undefined })}
              className="text-blue-400 hover:text-blue-300 underline"
            >
              Try again
            </button>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default LoadingStateManager
