from rest_framework import serializers
from django.contrib.auth.models import User
from .models import (
    SupportTicketCategory, SupportAgent, ServiceLevelAgreement,
    SupportTicket, TicketComment, KnowledgeBaseCategory,
    KnowledgeBaseArticle, CustomerFeedback, LiveChatSession, ChatMessage
)

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'email']

class SupportTicketCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = SupportTicketCategory
        fields = '__all__'

class SupportAgentSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)

    class Meta:
        model = SupportAgent
        fields = '__all__'

class ServiceLevelAgreementSerializer(serializers.ModelSerializer):
    class Meta:
        model = ServiceLevelAgreement
        fields = '__all__'

class TicketCommentSerializer(serializers.ModelSerializer):
    author = UserSerializer(read_only=True)

    class Meta:
        model = TicketComment
        fields = '__all__'

class SupportTicketSerializer(serializers.ModelSerializer):
    customer = UserSerializer(read_only=True)
    assigned_agent = SupportAgentSerializer(read_only=True)
    category = SupportTicketCategorySerializer(read_only=True)
    sla = ServiceLevelAgreementSerializer(read_only=True)
    comments = TicketCommentSerializer(many=True, read_only=True)

    class Meta:
        model = SupportTicket
        fields = '__all__'
        read_only_fields = ['ticket_id', 'created_at', 'updated_at']

class SupportTicketCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = SupportTicket
        fields = ['title', 'description', 'customer_email', 'customer_phone',
                 'category', 'priority', 'source', 'tags']

class KnowledgeBaseCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = KnowledgeBaseCategory
        fields = '__all__'

class KnowledgeBaseArticleSerializer(serializers.ModelSerializer):
    author = UserSerializer(read_only=True)
    category = KnowledgeBaseCategorySerializer(read_only=True)

    class Meta:
        model = KnowledgeBaseArticle
        fields = '__all__'
        read_only_fields = ['slug', 'view_count', 'helpful_votes', 'not_helpful_votes', 'created_at', 'updated_at']

class CustomerFeedbackSerializer(serializers.ModelSerializer):
    customer = UserSerializer(read_only=True)
    related_ticket = SupportTicketSerializer(read_only=True)

    class Meta:
        model = CustomerFeedback
        fields = '__all__'

class ChatMessageSerializer(serializers.ModelSerializer):
    sender = UserSerializer(read_only=True)

    class Meta:
        model = ChatMessage
        fields = '__all__'

class LiveChatSessionSerializer(serializers.ModelSerializer):
    customer = UserSerializer(read_only=True)
    agent = SupportAgentSerializer(read_only=True)
    messages = ChatMessageSerializer(many=True, read_only=True)

    class Meta:
        model = LiveChatSession
        fields = '__all__'
        read_only_fields = ['session_id', 'started_at', 'ended_at']

# Dashboard Statistics Serializers
class SupportDashboardSerializer(serializers.Serializer):
    total_tickets = serializers.IntegerField()
    open_tickets = serializers.IntegerField()
    in_progress_tickets = serializers.IntegerField()
    resolved_tickets = serializers.IntegerField()
    avg_resolution_time = serializers.CharField()
    customer_satisfaction = serializers.FloatField()
    sla_compliance = serializers.FloatField()
    active_chat_sessions = serializers.IntegerField()

class TicketTrendSerializer(serializers.Serializer):
    date = serializers.DateField()
    created = serializers.IntegerField()
    resolved = serializers.IntegerField()

class AgentPerformanceSerializer(serializers.Serializer):
    agent_name = serializers.CharField()
    tickets_resolved = serializers.IntegerField()
    avg_resolution_time = serializers.CharField()
    customer_rating = serializers.FloatField()


