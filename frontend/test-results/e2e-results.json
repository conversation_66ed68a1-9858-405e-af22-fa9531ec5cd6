{"config": {"configFile": "/Users/<USER>/Desktop/EMS/frontend/playwright.config.ts", "rootDir": "/Users/<USER>/Desktop/EMS/frontend/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/e2e-results.json"}], ["junit", {"outputFile": "test-results/e2e-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Desktop/EMS/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Desktop/EMS/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/EMS/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Desktop/EMS/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/EMS/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Desktop/EMS/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/EMS/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Desktop/EMS/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Desktop/EMS/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Desktop/EMS/frontend/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 4, "webServer": {"command": "npm run dev", "url": "http://localhost:5173", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "advanced-features.e2e.test.ts", "file": "advanced-features.e2e.test.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Advanced Features E2E Tests", "file": "advanced-features.e2e.test.ts", "line": 3, "column": 6, "specs": [], "suites": [{"title": "Global Search", "file": "advanced-features.e2e.test.ts", "line": 12, "column": 8, "specs": [{"title": "should open search with keyboard shortcut", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 10061, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"global-search\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"global-search\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"global-search\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"global-search\"]')\u001b[22m\n\n    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:18:67", "location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 67, "line": 18}, "snippet": "\u001b[0m \u001b[90m 16 |\u001b[39m       \n \u001b[90m 17 |\u001b[39m       \u001b[90m// Verify search dialog is open\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 18 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"global-search\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m    |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 19 |\u001b[39m       \n \u001b[90m 20 |\u001b[39m       \u001b[90m// Verify search input is focused\u001b[39m\n \u001b[90m 21 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"search-input\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeFocused()\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 67, "line": 18}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"global-search\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"global-search\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 16 |\u001b[39m       \n \u001b[90m 17 |\u001b[39m       \u001b[90m// Verify search dialog is open\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 18 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"global-search\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m    |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 19 |\u001b[39m       \n \u001b[90m 20 |\u001b[39m       \u001b[90m// Verify search input is focused\u001b[39m\n \u001b[90m 21 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"search-input\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeFocused()\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:18:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-18T11:27:41.194Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-3bbb1-arch-with-keyboard-shortcut-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-3bbb1-arch-with-keyboard-shortcut-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-3bbb1-arch-with-keyboard-shortcut-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 67, "line": 18}}], "status": "unexpected"}], "id": "4a08677700602d14e75a-2d741e6f2c5a64813169", "file": "advanced-features.e2e.test.ts", "line": 13, "column": 5}, {"title": "should perform search and show results", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "timedOut", "duration": 31648, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 18, "line": 29}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 27 |\u001b[39m       \n \u001b[90m 28 |\u001b[39m       \u001b[90m// Type search query\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 29 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'employee'\u001b[39m)\n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 30 |\u001b[39m       \n \u001b[90m 31 |\u001b[39m       \u001b[90m// Wait for search results\u001b[39m\n \u001b[90m 32 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"search-results\"]'\u001b[39m)\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:29:18\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-18T11:27:41.190Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-d42e3-orm-search-and-show-results-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-d42e3-orm-search-and-show-results-chromium/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-d42e3-orm-search-and-show-results-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "4a08677700602d14e75a-483aa3d0e912108ee5cb", "file": "advanced-features.e2e.test.ts", "line": 24, "column": 5}, {"title": "should show search suggestions", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "timedOut", "duration": 31707, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 18, "line": 43}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 41 |\u001b[39m       \n \u001b[90m 42 |\u001b[39m       \u001b[90m// Type partial query\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 43 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"search-input\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'emp'\u001b[39m)\n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 44 |\u001b[39m       \n \u001b[90m 45 |\u001b[39m       \u001b[90m// Wait for suggestions\u001b[39m\n \u001b[90m 46 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'[data-testid=\"search-suggestions\"]'\u001b[39m)\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:43:18\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-18T11:27:41.187Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-813f7-uld-show-search-suggestions-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-813f7-uld-show-search-suggestions-chromium/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-813f7-uld-show-search-suggestions-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "4a08677700602d14e75a-a2b4e9f5b31d790f243c", "file": "advanced-features.e2e.test.ts", "line": 38, "column": 5}, {"title": "should close search with Escape key", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 10054, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"global-search\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"global-search\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"global-search\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"global-search\"]')\u001b[22m\n\n    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:57:67", "location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 67, "line": 57}, "snippet": "\u001b[0m \u001b[90m 55 |\u001b[39m       \n \u001b[90m 56 |\u001b[39m       \u001b[90m// Verify search is open\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 57 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"global-search\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m    |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 58 |\u001b[39m       \n \u001b[90m 59 |\u001b[39m       \u001b[90m// Press Escape to close\u001b[39m\n \u001b[90m 60 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mkeyboard\u001b[33m.\u001b[39mpress(\u001b[32m'Escape'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 67, "line": 57}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"global-search\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"global-search\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 55 |\u001b[39m       \n \u001b[90m 56 |\u001b[39m       \u001b[90m// Verify search is open\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 57 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"global-search\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m    |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 58 |\u001b[39m       \n \u001b[90m 59 |\u001b[39m       \u001b[90m// Press Escape to close\u001b[39m\n \u001b[90m 60 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mkeyboard\u001b[33m.\u001b[39mpress(\u001b[32m'Escape'\u001b[39m)\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:57:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-18T11:27:41.194Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-da298-lose-search-with-Escape-key-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-da298-lose-search-with-Escape-key-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-da298-lose-search-with-Escape-key-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 67, "line": 57}}], "status": "unexpected"}], "id": "4a08677700602d14e75a-bf523cb0a36c85f39750", "file": "advanced-features.e2e.test.ts", "line": 52, "column": 5}, {"title": "should open search with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-623b1753d50db7ad6382", "file": "advanced-features.e2e.test.ts", "line": 13, "column": 5}, {"title": "should perform search and show results", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-6e49e8ccccf8e6d3b5e4", "file": "advanced-features.e2e.test.ts", "line": 24, "column": 5}, {"title": "should show search suggestions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-3072f704715a27da04af", "file": "advanced-features.e2e.test.ts", "line": 38, "column": 5}, {"title": "should close search with Escape key", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-a71909b3735ea99af0d6", "file": "advanced-features.e2e.test.ts", "line": 52, "column": 5}, {"title": "should open search with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-0cbc2c40c8286a8a51d2", "file": "advanced-features.e2e.test.ts", "line": 13, "column": 5}, {"title": "should perform search and show results", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-bdd7cd5c5dd843fa565c", "file": "advanced-features.e2e.test.ts", "line": 24, "column": 5}, {"title": "should show search suggestions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-3d9ef7caa6a0388a9fb2", "file": "advanced-features.e2e.test.ts", "line": 38, "column": 5}, {"title": "should close search with Escape key", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-789964b762b4d95aeafc", "file": "advanced-features.e2e.test.ts", "line": 52, "column": 5}, {"title": "should open search with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-fa92a5096bd8e1b07329", "file": "advanced-features.e2e.test.ts", "line": 13, "column": 5}, {"title": "should perform search and show results", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-9cc0e884397efa11d025", "file": "advanced-features.e2e.test.ts", "line": 24, "column": 5}, {"title": "should show search suggestions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-1418725bd182b29455be", "file": "advanced-features.e2e.test.ts", "line": 38, "column": 5}, {"title": "should close search with Escape key", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-91924de6ad95cbb759b4", "file": "advanced-features.e2e.test.ts", "line": 52, "column": 5}, {"title": "should open search with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-035abf8b0156dacab08b", "file": "advanced-features.e2e.test.ts", "line": 13, "column": 5}, {"title": "should perform search and show results", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-9242e535feb962de280c", "file": "advanced-features.e2e.test.ts", "line": 24, "column": 5}, {"title": "should show search suggestions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-f787e1446e6804ac156b", "file": "advanced-features.e2e.test.ts", "line": 38, "column": 5}, {"title": "should close search with Escape key", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-90c3cf03aae3e0ed0a22", "file": "advanced-features.e2e.test.ts", "line": 52, "column": 5}]}, {"title": "Export Functionality", "file": "advanced-features.e2e.test.ts", "line": 67, "column": 8, "specs": [{"title": "should open export dialog with keyboard shortcut", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "failed", "duration": 7461, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"export-dialog\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"export-dialog\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"export-dialog\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"export-dialog\"]')\u001b[22m\n\n    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:73:67", "location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 67, "line": 73}, "snippet": "\u001b[0m \u001b[90m 71 |\u001b[39m       \n \u001b[90m 72 |\u001b[39m       \u001b[90m// Verify export dialog is open\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 73 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"export-dialog\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m    |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 74 |\u001b[39m     })\n \u001b[90m 75 |\u001b[39m\n \u001b[90m 76 |\u001b[39m     test(\u001b[32m'should select export format'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 67, "line": 73}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"export-dialog\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"export-dialog\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 71 |\u001b[39m       \n \u001b[90m 72 |\u001b[39m       \u001b[90m// Verify export dialog is open\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 73 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"export-dialog\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m    |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 74 |\u001b[39m     })\n \u001b[90m 75 |\u001b[39m\n \u001b[90m 76 |\u001b[39m     test(\u001b[32m'should select export format'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:73:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-18T11:27:55.908Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-7273d-alog-with-keyboard-shortcut-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-7273d-alog-with-keyboard-shortcut-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-7273d-alog-with-keyboard-shortcut-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 67, "line": 73}}], "status": "unexpected"}], "id": "4a08677700602d14e75a-2de561bac74395f24f0c", "file": "advanced-features.e2e.test.ts", "line": 68, "column": 5}, {"title": "should select export format", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 3, "status": "timedOut", "duration": 31538, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 18, "line": 81}, "message": "Error: page.click: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"format-pdf\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 79 |\u001b[39m       \n \u001b[90m 80 |\u001b[39m       \u001b[90m// Select PDF format\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 81 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"format-pdf\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 82 |\u001b[39m       \n \u001b[90m 83 |\u001b[39m       \u001b[90m// Verify PDF format is selected\u001b[39m\n \u001b[90m 84 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"format-pdf\"]'\u001b[39m))\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/selected|active/\u001b[39m)\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:81:18\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-18T11:27:55.908Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-68485-should-select-export-format-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-68485-should-select-export-format-chromium/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-68485-should-select-export-format-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "4a08677700602d14e75a-0a264563f7b2c070484c", "file": "advanced-features.e2e.test.ts", "line": 76, "column": 5}, {"title": "should configure export columns", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "timedOut", "duration": 31454, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 18, "line": 92}, "message": "Error: page.uncheck: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"column-checkbox\"]:first-child')\u001b[22m\n\n\n\u001b[0m \u001b[90m 90 |\u001b[39m       \n \u001b[90m 91 |\u001b[39m       \u001b[90m// Uncheck a column\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 92 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39muncheck(\u001b[32m'[data-testid=\"column-checkbox\"]:first-child'\u001b[39m)\n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 93 |\u001b[39m       \n \u001b[90m 94 |\u001b[39m       \u001b[90m// Verify column is unchecked\u001b[39m\n \u001b[90m 95 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"column-checkbox\"]:first-child'\u001b[39m))\u001b[33m.\u001b[39mnot\u001b[33m.\u001b[39mtoBeChecked()\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:92:18\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-18T11:28:05.142Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-8238e-ld-configure-export-columns-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-8238e-ld-configure-export-columns-chromium/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-8238e-ld-configure-export-columns-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "4a08677700602d14e75a-3e97458db7832ff87325", "file": "advanced-features.e2e.test.ts", "line": 87, "column": 5}, {"title": "should start export process", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 1, "status": "timedOut", "duration": 31480, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 18, "line": 103}, "message": "Error: page.click: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"export-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 101 |\u001b[39m       \n \u001b[90m 102 |\u001b[39m       \u001b[90m// Click export button\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 103 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"export-button\"]'\u001b[39m)\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 104 |\u001b[39m       \n \u001b[90m 105 |\u001b[39m       \u001b[90m// Verify export progress is shown\u001b[39m\n \u001b[90m 106 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"export-progress\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:103:18\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-18T11:28:16.879Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-eda9e-should-start-export-process-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-eda9e-should-start-export-process-chromium/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-eda9e-should-start-export-process-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "4a08677700602d14e75a-8fb9464d52f4424aaa75", "file": "advanced-features.e2e.test.ts", "line": 98, "column": 5}, {"title": "should open export dialog with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-10719d437651db938a75", "file": "advanced-features.e2e.test.ts", "line": 68, "column": 5}, {"title": "should select export format", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-8265e38895c9fbbfab72", "file": "advanced-features.e2e.test.ts", "line": 76, "column": 5}, {"title": "should configure export columns", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-c171685f695d7e063bfd", "file": "advanced-features.e2e.test.ts", "line": 87, "column": 5}, {"title": "should start export process", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-5b4252c7275cfd4c6319", "file": "advanced-features.e2e.test.ts", "line": 98, "column": 5}, {"title": "should open export dialog with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-95e4e562321cad87e52c", "file": "advanced-features.e2e.test.ts", "line": 68, "column": 5}, {"title": "should select export format", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-b3867c1064c642858d3f", "file": "advanced-features.e2e.test.ts", "line": 76, "column": 5}, {"title": "should configure export columns", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-615df9c34a4a891c3299", "file": "advanced-features.e2e.test.ts", "line": 87, "column": 5}, {"title": "should start export process", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-49b9fc997d4f7a6e3aa9", "file": "advanced-features.e2e.test.ts", "line": 98, "column": 5}, {"title": "should open export dialog with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-ba0ae91806ba176d7e7d", "file": "advanced-features.e2e.test.ts", "line": 68, "column": 5}, {"title": "should select export format", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-a537c6b851d9e0444f2d", "file": "advanced-features.e2e.test.ts", "line": 76, "column": 5}, {"title": "should configure export columns", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-22572c6e7dd348a465d9", "file": "advanced-features.e2e.test.ts", "line": 87, "column": 5}, {"title": "should start export process", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-3c26df1c837821472f63", "file": "advanced-features.e2e.test.ts", "line": 98, "column": 5}, {"title": "should open export dialog with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-991c1a91d376a434b74a", "file": "advanced-features.e2e.test.ts", "line": 68, "column": 5}, {"title": "should select export format", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-5981857641934f0ac6c0", "file": "advanced-features.e2e.test.ts", "line": 76, "column": 5}, {"title": "should configure export columns", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-c62d6e6130f2bea97443", "file": "advanced-features.e2e.test.ts", "line": 87, "column": 5}, {"title": "should start export process", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-dc23e40c9f35ff567c55", "file": "advanced-features.e2e.test.ts", "line": 98, "column": 5}]}, {"title": "Notification Center", "file": "advanced-features.e2e.test.ts", "line": 110, "column": 8, "specs": [{"title": "should open notifications with keyboard shortcut", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 2, "status": "failed", "duration": 7342, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"notification-center\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"notification-center\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"notification-center\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"notification-center\"]')\u001b[22m\n\n    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:116:73", "location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 73, "line": 116}, "snippet": "\u001b[0m \u001b[90m 114 |\u001b[39m       \n \u001b[90m 115 |\u001b[39m       \u001b[90m// Verify notification center is open\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"notification-center\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m     })\n \u001b[90m 118 |\u001b[39m\n \u001b[90m 119 |\u001b[39m     test(\u001b[32m'should filter notifications'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 73, "line": 116}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"notification-center\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"notification-center\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 114 |\u001b[39m       \n \u001b[90m 115 |\u001b[39m       \u001b[90m// Verify notification center is open\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 116 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"notification-center\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 117 |\u001b[39m     })\n \u001b[90m 118 |\u001b[39m\n \u001b[90m 119 |\u001b[39m     test(\u001b[32m'should filter notifications'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:116:73\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-18T11:28:16.879Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-fd2d4-ions-with-keyboard-shortcut-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-fd2d4-ions-with-keyboard-shortcut-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-fd2d4-ions-with-keyboard-shortcut-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 73, "line": 116}}], "status": "unexpected"}], "id": "4a08677700602d14e75a-bc26c78dc0c618abecea", "file": "advanced-features.e2e.test.ts", "line": 111, "column": 5}, {"title": "should filter notifications", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 2, "status": "interrupted", "duration": 26579, "error": {"message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"filter-unread\"]')\u001b[22m\n", "stack": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"filter-unread\"]')\u001b[22m\n\n    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:124:18", "location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 18, "line": 124}, "snippet": "\u001b[0m \u001b[90m 122 |\u001b[39m       \n \u001b[90m 123 |\u001b[39m       \u001b[90m// Click unread filter\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 124 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"filter-unread\"]'\u001b[39m)\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 125 |\u001b[39m       \n \u001b[90m 126 |\u001b[39m       \u001b[90m// Verify filter is applied\u001b[39m\n \u001b[90m 127 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"filter-unread\"]'\u001b[39m))\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/active|selected/\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 18, "line": 124}, "message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"filter-unread\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 122 |\u001b[39m       \n \u001b[90m 123 |\u001b[39m       \u001b[90m// Click unread filter\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 124 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"filter-unread\"]'\u001b[39m)\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 125 |\u001b[39m       \n \u001b[90m 126 |\u001b[39m       \u001b[90m// Verify filter is applied\u001b[39m\n \u001b[90m 127 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"filter-unread\"]'\u001b[39m))\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/active|selected/\u001b[39m)\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:124:18\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-18T11:28:24.967Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-571db-should-filter-notifications-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-571db-should-filter-notifications-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-571db-should-filter-notifications-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 18, "line": 124}}], "status": "skipped"}], "id": "4a08677700602d14e75a-18b7e3c0c34ddd7d3907", "file": "advanced-features.e2e.test.ts", "line": 119, "column": 5}, {"title": "should mark notification as read", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 3, "status": "interrupted", "duration": 22527, "error": {"message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"notification-item\"]:first-child [data-testid=\"mark-read\"]')\u001b[22m\n", "stack": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"notification-item\"]:first-child [data-testid=\"mark-read\"]')\u001b[22m\n\n    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:135:18", "location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 18, "line": 135}, "snippet": "\u001b[0m \u001b[90m 133 |\u001b[39m       \n \u001b[90m 134 |\u001b[39m       \u001b[90m// Click mark as read button on first notification\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 135 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"notification-item\"]:first-child [data-testid=\"mark-read\"]'\u001b[39m)\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 136 |\u001b[39m       \n \u001b[90m 137 |\u001b[39m       \u001b[90m// Verify notification is marked as read\u001b[39m\n \u001b[90m 138 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"notification-item\"]:first-child'\u001b[39m))\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/read/\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 18, "line": 135}, "message": "Error: page.click: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"notification-item\"]:first-child [data-testid=\"mark-read\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 133 |\u001b[39m       \n \u001b[90m 134 |\u001b[39m       \u001b[90m// Click mark as read button on first notification\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 135 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"notification-item\"]:first-child [data-testid=\"mark-read\"]'\u001b[39m)\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 136 |\u001b[39m       \n \u001b[90m 137 |\u001b[39m       \u001b[90m// Verify notification is marked as read\u001b[39m\n \u001b[90m 138 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"notification-item\"]:first-child'\u001b[39m))\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/read/\u001b[39m)\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:135:18\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-18T11:28:28.874Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-412d0-d-mark-notification-as-read-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-412d0-d-mark-notification-as-read-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-412d0-d-mark-notification-as-read-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 18, "line": 135}}], "status": "skipped"}], "id": "4a08677700602d14e75a-ad8c34de3f8b5164fea1", "file": "advanced-features.e2e.test.ts", "line": 130, "column": 5}, {"title": "should search notifications", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 11, "parallelIndex": 0, "status": "interrupted", "duration": 13294, "error": {"message": "Error: page.fill: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"notification-search\"]')\u001b[22m\n", "stack": "Error: page.fill: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"notification-search\"]')\u001b[22m\n\n    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:146:18", "location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 18, "line": 146}, "snippet": "\u001b[0m \u001b[90m 144 |\u001b[39m       \n \u001b[90m 145 |\u001b[39m       \u001b[90m// Type in search input\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 146 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"notification-search\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'system'\u001b[39m)\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 147 |\u001b[39m       \n \u001b[90m 148 |\u001b[39m       \u001b[90m// Verify filtered results\u001b[39m\n \u001b[90m 149 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"notification-item\"]'\u001b[39m))\u001b[33m.\u001b[39mtoHaveCount\u001b[33m.\u001b[39mgreater<PERSON>han(\u001b[35m0\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 18, "line": 146}, "message": "Error: page.fill: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"notification-search\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 144 |\u001b[39m       \n \u001b[90m 145 |\u001b[39m       \u001b[90m// Type in search input\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 146 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"notification-search\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'system'\u001b[39m)\n \u001b[90m     |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 147 |\u001b[39m       \n \u001b[90m 148 |\u001b[39m       \u001b[90m// Verify filtered results\u001b[39m\n \u001b[90m 149 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"notification-item\"]'\u001b[39m))\u001b[33m.\u001b[39mtoHaveCount\u001b[33m.\u001b[39mgreaterThan(\u001b[35m0\u001b[39m)\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:146:18\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-18T11:28:37.562Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-f9f88-should-search-notifications-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-f9f88-should-search-notifications-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-f9f88-should-search-notifications-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 18, "line": 146}}], "status": "skipped"}], "id": "4a08677700602d14e75a-26755cf8aa295f19ec7d", "file": "advanced-features.e2e.test.ts", "line": 141, "column": 5}, {"title": "should open notifications with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-77e818216b92f7586e0a", "file": "advanced-features.e2e.test.ts", "line": 111, "column": 5}, {"title": "should filter notifications", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-f85600fa3e4baab239b7", "file": "advanced-features.e2e.test.ts", "line": 119, "column": 5}, {"title": "should mark notification as read", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-9ee1459fffa313234218", "file": "advanced-features.e2e.test.ts", "line": 130, "column": 5}, {"title": "should search notifications", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-1bf9bbed4c7fdffbe6ef", "file": "advanced-features.e2e.test.ts", "line": 141, "column": 5}, {"title": "should open notifications with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-018b9f0e8b413871c4cb", "file": "advanced-features.e2e.test.ts", "line": 111, "column": 5}, {"title": "should filter notifications", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-23302b8f609fa7c8ef6a", "file": "advanced-features.e2e.test.ts", "line": 119, "column": 5}, {"title": "should mark notification as read", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-7240eaca0c661c46fcb5", "file": "advanced-features.e2e.test.ts", "line": 130, "column": 5}, {"title": "should search notifications", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-0d1f007ac70d3b3b401e", "file": "advanced-features.e2e.test.ts", "line": 141, "column": 5}, {"title": "should open notifications with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-db50fff521bcf4ed615d", "file": "advanced-features.e2e.test.ts", "line": 111, "column": 5}, {"title": "should filter notifications", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-5ecc6a3ab8fcfa05e7c2", "file": "advanced-features.e2e.test.ts", "line": 119, "column": 5}, {"title": "should mark notification as read", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-ccf60ff9da13c41d3ec0", "file": "advanced-features.e2e.test.ts", "line": 130, "column": 5}, {"title": "should search notifications", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-2b81178fb70c9eabbc54", "file": "advanced-features.e2e.test.ts", "line": 141, "column": 5}, {"title": "should open notifications with keyboard shortcut", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-b9235ebe4843f213886e", "file": "advanced-features.e2e.test.ts", "line": 111, "column": 5}, {"title": "should filter notifications", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-91a8fc63eca9ed7b98dd", "file": "advanced-features.e2e.test.ts", "line": 119, "column": 5}, {"title": "should mark notification as read", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-064e8fb49a3ca3a8ad1a", "file": "advanced-features.e2e.test.ts", "line": 130, "column": 5}, {"title": "should search notifications", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-454dfb6a90164ad3603f", "file": "advanced-features.e2e.test.ts", "line": 141, "column": 5}]}, {"title": "Responsive Design", "file": "advanced-features.e2e.test.ts", "line": 153, "column": 8, "specs": [{"title": "should work on mobile devices", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 1, "status": "interrupted", "duration": 1000, "error": {"message": "Error: page.waitForLoadState: Test ended.", "stack": "Error: page.waitForLoadState: Test ended.\n    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:9:16", "location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the application to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Global Search'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 16, "line": 9}, "message": "Error: page.waitForLoadState: Test ended.\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for the application to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Global Search'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:9:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-18T11:28:49.192Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-f593e-ould-work-on-mobile-devices-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/EMS/frontend/test-results/advanced-features.e2e-Adva-f593e-ould-work-on-mobile-devices-chromium/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts", "column": 16, "line": 9}}], "status": "skipped"}], "id": "4a08677700602d14e75a-70723f630287ba4de080", "file": "advanced-features.e2e.test.ts", "line": 154, "column": 5}, {"title": "should work on tablet devices", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-796f73d1d68b5502f536", "file": "advanced-features.e2e.test.ts", "line": 165, "column": 5}, {"title": "should work on mobile devices", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-551f9a7f3ea9b4ce0e78", "file": "advanced-features.e2e.test.ts", "line": 154, "column": 5}, {"title": "should work on tablet devices", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-ae3bd02a23d484d7ff0e", "file": "advanced-features.e2e.test.ts", "line": 165, "column": 5}, {"title": "should work on mobile devices", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-50fb563e9ec4e498c95c", "file": "advanced-features.e2e.test.ts", "line": 154, "column": 5}, {"title": "should work on tablet devices", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-c24a0f5d331d9bc6791e", "file": "advanced-features.e2e.test.ts", "line": 165, "column": 5}, {"title": "should work on mobile devices", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-e66ddd8ec22c30e71a10", "file": "advanced-features.e2e.test.ts", "line": 154, "column": 5}, {"title": "should work on tablet devices", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-0bcea42a3995023373c2", "file": "advanced-features.e2e.test.ts", "line": 165, "column": 5}, {"title": "should work on mobile devices", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-b6d8944373182b2bef5b", "file": "advanced-features.e2e.test.ts", "line": 154, "column": 5}, {"title": "should work on tablet devices", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-919ac970a920a2b98a7c", "file": "advanced-features.e2e.test.ts", "line": 165, "column": 5}]}, {"title": "Accessibility", "file": "advanced-features.e2e.test.ts", "line": 177, "column": 8, "specs": [{"title": "should be keyboard navigable", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-b38a9cf801ae01cce135", "file": "advanced-features.e2e.test.ts", "line": 178, "column": 5}, {"title": "should have proper ARIA labels", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-693a5e63a4f933fe1731", "file": "advanced-features.e2e.test.ts", "line": 191, "column": 5}, {"title": "should be keyboard navigable", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-5f28e307fa7ea7e696af", "file": "advanced-features.e2e.test.ts", "line": 178, "column": 5}, {"title": "should have proper ARIA labels", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-2d6e0d2d4bb4d1695da0", "file": "advanced-features.e2e.test.ts", "line": 191, "column": 5}, {"title": "should be keyboard navigable", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-4f74b1a85609b6208356", "file": "advanced-features.e2e.test.ts", "line": 178, "column": 5}, {"title": "should have proper ARIA labels", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-6eb50d602ce0f098e38d", "file": "advanced-features.e2e.test.ts", "line": 191, "column": 5}, {"title": "should be keyboard navigable", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-651198620176146076f5", "file": "advanced-features.e2e.test.ts", "line": 178, "column": 5}, {"title": "should have proper ARIA labels", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-f4fcdd22d972249f4cc6", "file": "advanced-features.e2e.test.ts", "line": 191, "column": 5}, {"title": "should be keyboard navigable", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-f5189607ebfef590f9c0", "file": "advanced-features.e2e.test.ts", "line": 178, "column": 5}, {"title": "should have proper ARIA labels", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-eeb5279b125d174ee457", "file": "advanced-features.e2e.test.ts", "line": 191, "column": 5}]}, {"title": "Performance", "file": "advanced-features.e2e.test.ts", "line": 200, "column": 8, "specs": [{"title": "should load within acceptable time", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-db1f3ac3fde6e1f7a4f2", "file": "advanced-features.e2e.test.ts", "line": 201, "column": 5}, {"title": "should handle large datasets efficiently", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-64968b23ca0a458d683c", "file": "advanced-features.e2e.test.ts", "line": 214, "column": 5}, {"title": "should load within acceptable time", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-e655f4798456191c85a9", "file": "advanced-features.e2e.test.ts", "line": 201, "column": 5}, {"title": "should handle large datasets efficiently", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-f3919b16e4cba12f9df5", "file": "advanced-features.e2e.test.ts", "line": 214, "column": 5}, {"title": "should load within acceptable time", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-3a226e49db157d08bcda", "file": "advanced-features.e2e.test.ts", "line": 201, "column": 5}, {"title": "should handle large datasets efficiently", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-fc3ad769e145805118b8", "file": "advanced-features.e2e.test.ts", "line": 214, "column": 5}, {"title": "should load within acceptable time", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-42566d22d9046578584e", "file": "advanced-features.e2e.test.ts", "line": 201, "column": 5}, {"title": "should handle large datasets efficiently", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-65f96c5a542ed241cd9d", "file": "advanced-features.e2e.test.ts", "line": 214, "column": 5}, {"title": "should load within acceptable time", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-36a7c769953893b8e862", "file": "advanced-features.e2e.test.ts", "line": 201, "column": 5}, {"title": "should handle large datasets efficiently", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "4a08677700602d14e75a-05808b074183ae364405", "file": "advanced-features.e2e.test.ts", "line": 214, "column": 5}]}]}]}, {"title": "api.spec.ts", "file": "api.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "API Integration Tests", "file": "api.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should authenticate and get user profile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-4e3bc5fd7ed69d066449", "file": "api.spec.ts", "line": 21, "column": 3}, {"title": "should get dashboard stats", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-5aa78b26172882f3cce0", "file": "api.spec.ts", "line": 34, "column": 3}, {"title": "should get departments list", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-75505115a07919539b0b", "file": "api.spec.ts", "line": 51, "column": 3}, {"title": "should get employees list", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-1aabf4eff58582d21587", "file": "api.spec.ts", "line": 65, "column": 3}, {"title": "should handle unauthorized requests", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-1b54b83ef8ea51886b3b", "file": "api.spec.ts", "line": 79, "column": 3}, {"title": "should handle invalid endpoints", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-d9546f8c337fb4b7e3bc", "file": "api.spec.ts", "line": 87, "column": 3}, {"title": "should validate CORS headers", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-9f030eb7bd91839660f2", "file": "api.spec.ts", "line": 97, "column": 3}, {"title": "should refresh authentication token", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-41dd666d71f2c2bbdf15", "file": "api.spec.ts", "line": 110, "column": 3}, {"title": "should handle API rate limiting gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-1ead7798b8de95f2b4fb", "file": "api.spec.ts", "line": 135, "column": 3}, {"title": "should validate API response structure", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-2dfffa846d94432cf1bb", "file": "api.spec.ts", "line": 153, "column": 3}, {"title": "should authenticate and get user profile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-f4fd6b1f354a63a069bb", "file": "api.spec.ts", "line": 21, "column": 3}, {"title": "should get dashboard stats", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-4d26f9cd9a1c7d117d84", "file": "api.spec.ts", "line": 34, "column": 3}, {"title": "should get departments list", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-f5e868f383786c83a36c", "file": "api.spec.ts", "line": 51, "column": 3}, {"title": "should get employees list", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-2e8e7d56c1b78023cbe3", "file": "api.spec.ts", "line": 65, "column": 3}, {"title": "should handle unauthorized requests", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-6873bf15438048afcd40", "file": "api.spec.ts", "line": 79, "column": 3}, {"title": "should handle invalid endpoints", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-13a7e4c860bf6ca6f089", "file": "api.spec.ts", "line": 87, "column": 3}, {"title": "should validate CORS headers", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-4bbef9e59f88e96328f5", "file": "api.spec.ts", "line": 97, "column": 3}, {"title": "should refresh authentication token", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-f2d4b3a119ddbe0da3d9", "file": "api.spec.ts", "line": 110, "column": 3}, {"title": "should handle API rate limiting gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-0bb6f3ebebace94825bb", "file": "api.spec.ts", "line": 135, "column": 3}, {"title": "should validate API response structure", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-47b56de2af537bcbf6de", "file": "api.spec.ts", "line": 153, "column": 3}, {"title": "should authenticate and get user profile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-045175e994051b8e9436", "file": "api.spec.ts", "line": 21, "column": 3}, {"title": "should get dashboard stats", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-f2c4e1404921d97a3d63", "file": "api.spec.ts", "line": 34, "column": 3}, {"title": "should get departments list", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-2d3530a1538a4863cab6", "file": "api.spec.ts", "line": 51, "column": 3}, {"title": "should get employees list", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-3ed82ede0d464317b06d", "file": "api.spec.ts", "line": 65, "column": 3}, {"title": "should handle unauthorized requests", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-da50d7f6e633d016b12f", "file": "api.spec.ts", "line": 79, "column": 3}, {"title": "should handle invalid endpoints", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-6ba55ab3e01f9d8385a7", "file": "api.spec.ts", "line": 87, "column": 3}, {"title": "should validate CORS headers", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-0bad1d88385e350ebabf", "file": "api.spec.ts", "line": 97, "column": 3}, {"title": "should refresh authentication token", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-929b0d9e372f305c57ed", "file": "api.spec.ts", "line": 110, "column": 3}, {"title": "should handle API rate limiting gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-e36404625590756bf224", "file": "api.spec.ts", "line": 135, "column": 3}, {"title": "should validate API response structure", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-8ef460579761844df338", "file": "api.spec.ts", "line": 153, "column": 3}, {"title": "should authenticate and get user profile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-1293167e5dac3ef35bb4", "file": "api.spec.ts", "line": 21, "column": 3}, {"title": "should get dashboard stats", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-67f52d0d29a7cffa108f", "file": "api.spec.ts", "line": 34, "column": 3}, {"title": "should get departments list", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-20bb69bf001a0b0a7c9c", "file": "api.spec.ts", "line": 51, "column": 3}, {"title": "should get employees list", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-2ad3ff98b38e72eadde2", "file": "api.spec.ts", "line": 65, "column": 3}, {"title": "should handle unauthorized requests", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-d7135cae7a7dd5c8f9c7", "file": "api.spec.ts", "line": 79, "column": 3}, {"title": "should handle invalid endpoints", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-c14dd48<PERSON>d23c867b34", "file": "api.spec.ts", "line": 87, "column": 3}, {"title": "should validate CORS headers", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-1580d6d786bda6177f0a", "file": "api.spec.ts", "line": 97, "column": 3}, {"title": "should refresh authentication token", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-66f42ffc22753ccca986", "file": "api.spec.ts", "line": 110, "column": 3}, {"title": "should handle API rate limiting gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-a7c97fc91686e2b25c01", "file": "api.spec.ts", "line": 135, "column": 3}, {"title": "should validate API response structure", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-8c8dd4d6ffb6071800e4", "file": "api.spec.ts", "line": 153, "column": 3}, {"title": "should authenticate and get user profile", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-d5d5c4654d99d5131d1d", "file": "api.spec.ts", "line": 21, "column": 3}, {"title": "should get dashboard stats", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-666107ae7ce4602169f2", "file": "api.spec.ts", "line": 34, "column": 3}, {"title": "should get departments list", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-ce3fd233af6804ff5335", "file": "api.spec.ts", "line": 51, "column": 3}, {"title": "should get employees list", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-abb1def20cf56d02d05b", "file": "api.spec.ts", "line": 65, "column": 3}, {"title": "should handle unauthorized requests", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-971a21472d36dc233639", "file": "api.spec.ts", "line": 79, "column": 3}, {"title": "should handle invalid endpoints", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-a1c1e319b5a46926c56c", "file": "api.spec.ts", "line": 87, "column": 3}, {"title": "should validate CORS headers", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-c0b1d9a945c6c9f41537", "file": "api.spec.ts", "line": 97, "column": 3}, {"title": "should refresh authentication token", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-18503683ee9dca90875c", "file": "api.spec.ts", "line": 110, "column": 3}, {"title": "should handle API rate limiting gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-5683a25c11e4af527cd6", "file": "api.spec.ts", "line": 135, "column": 3}, {"title": "should validate API response structure", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "1ea1b1599cbc08f16936-41b13638a10588bb742b", "file": "api.spec.ts", "line": 153, "column": 3}]}]}, {"title": "auth.spec.ts", "file": "auth.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Authentication Flow", "file": "auth.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display login page for unauthenticated users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-e2e848a096acc0e262bd", "file": "auth.spec.ts", "line": 9, "column": 3}, {"title": "should show validation errors for empty login form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-6c67a9b2bc614bfba0fd", "file": "auth.spec.ts", "line": 19, "column": 3}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-05954164880eaca3a0f9", "file": "auth.spec.ts", "line": 27, "column": 3}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-2825506ca7913f2dbd4e", "file": "auth.spec.ts", "line": 39, "column": 3}, {"title": "should maintain session after page refresh", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-aacdbfe2484cf9d13e6a", "file": "auth.spec.ts", "line": 54, "column": 3}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-c0ce6ccc7b7e02794edb", "file": "auth.spec.ts", "line": 71, "column": 3}, {"title": "should display login page for unauthenticated users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-b990b860ea4d1839cb65", "file": "auth.spec.ts", "line": 9, "column": 3}, {"title": "should show validation errors for empty login form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-a0de05df178face6908c", "file": "auth.spec.ts", "line": 19, "column": 3}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-5579b1115e5e26c1ba43", "file": "auth.spec.ts", "line": 27, "column": 3}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-be4a0cc21a9f71ab9a66", "file": "auth.spec.ts", "line": 39, "column": 3}, {"title": "should maintain session after page refresh", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-a201d3485b1520cc4be8", "file": "auth.spec.ts", "line": 54, "column": 3}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-4410c42745b7a4d89d03", "file": "auth.spec.ts", "line": 71, "column": 3}, {"title": "should display login page for unauthenticated users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-5667a91f5bdee5f2d967", "file": "auth.spec.ts", "line": 9, "column": 3}, {"title": "should show validation errors for empty login form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-bbb7ebef962626bec567", "file": "auth.spec.ts", "line": 19, "column": 3}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-4e575a42f2e3a5f20db2", "file": "auth.spec.ts", "line": 27, "column": 3}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-61a32ad5b38d503c68f7", "file": "auth.spec.ts", "line": 39, "column": 3}, {"title": "should maintain session after page refresh", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-1feb24f990d13467675f", "file": "auth.spec.ts", "line": 54, "column": 3}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-1f08bff9df919d288ae6", "file": "auth.spec.ts", "line": 71, "column": 3}, {"title": "should display login page for unauthenticated users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-bfb7dd89dd5fc38e80c1", "file": "auth.spec.ts", "line": 9, "column": 3}, {"title": "should show validation errors for empty login form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-7759d1f125f174d80d63", "file": "auth.spec.ts", "line": 19, "column": 3}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-12287ecea93857666643", "file": "auth.spec.ts", "line": 27, "column": 3}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-db2e2a1ad85c3d9ef330", "file": "auth.spec.ts", "line": 39, "column": 3}, {"title": "should maintain session after page refresh", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-fa7692c643d5a1f772e5", "file": "auth.spec.ts", "line": 54, "column": 3}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-8a3b860ad917d672a1c0", "file": "auth.spec.ts", "line": 71, "column": 3}, {"title": "should display login page for unauthenticated users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-852da45ce5ab822151ee", "file": "auth.spec.ts", "line": 9, "column": 3}, {"title": "should show validation errors for empty login form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-cb50db32e24d0f10f751", "file": "auth.spec.ts", "line": 19, "column": 3}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-0c3a71e4f2facbc6e673", "file": "auth.spec.ts", "line": 27, "column": 3}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-e883f91b94093e126c60", "file": "auth.spec.ts", "line": 39, "column": 3}, {"title": "should maintain session after page refresh", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-3f98ee8522bcdbcda244", "file": "auth.spec.ts", "line": 54, "column": 3}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d748ac400d08b85935ef-5e89033e25aea64867f4", "file": "auth.spec.ts", "line": 71, "column": 3}]}]}, {"title": "dashboard.spec.ts", "file": "dashboard.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Dashboard Functionality", "file": "dashboard.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display dashboard with key metrics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-47e5b6635715b08f6b89", "file": "dashboard.spec.ts", "line": 20, "column": 3}, {"title": "should display employee statistics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-8b2d567a4f9c7c1f9484", "file": "dashboard.spec.ts", "line": 31, "column": 3}, {"title": "should have working navigation menu", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-f6a2e6831b1dcc66df7d", "file": "dashboard.spec.ts", "line": 39, "column": 3}, {"title": "should display charts and visualizations", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-0ac7966f04c8c6e924cb", "file": "dashboard.spec.ts", "line": 66, "column": 3}, {"title": "should be responsive on mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-d0575fd16b2bf749bb9c", "file": "dashboard.spec.ts", "line": 92, "column": 3}, {"title": "should handle Arabic/English language toggle", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-7090e5e6bb8fe5ce118c", "file": "dashboard.spec.ts", "line": 118, "column": 3}, {"title": "should load without JavaScript errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-f9ac50430263cb8e673c", "file": "dashboard.spec.ts", "line": 150, "column": 3}, {"title": "should display dashboard with key metrics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-ae8c0bef951cd4923c7c", "file": "dashboard.spec.ts", "line": 20, "column": 3}, {"title": "should display employee statistics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-9b611d069fdee3dead35", "file": "dashboard.spec.ts", "line": 31, "column": 3}, {"title": "should have working navigation menu", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-b75dbc16a2142e80fd88", "file": "dashboard.spec.ts", "line": 39, "column": 3}, {"title": "should display charts and visualizations", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-da621b18e49af82fce76", "file": "dashboard.spec.ts", "line": 66, "column": 3}, {"title": "should be responsive on mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-58d3f26e7016cb739596", "file": "dashboard.spec.ts", "line": 92, "column": 3}, {"title": "should handle Arabic/English language toggle", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-3970ab82daf05a92fb38", "file": "dashboard.spec.ts", "line": 118, "column": 3}, {"title": "should load without JavaScript errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-93f05803aab7af04e472", "file": "dashboard.spec.ts", "line": 150, "column": 3}, {"title": "should display dashboard with key metrics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-777ee45c9796f5d33782", "file": "dashboard.spec.ts", "line": 20, "column": 3}, {"title": "should display employee statistics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-a7a40d3a1750450bf51a", "file": "dashboard.spec.ts", "line": 31, "column": 3}, {"title": "should have working navigation menu", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-30c5289424bcadc358f2", "file": "dashboard.spec.ts", "line": 39, "column": 3}, {"title": "should display charts and visualizations", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-68b656a70de4e8c213b4", "file": "dashboard.spec.ts", "line": 66, "column": 3}, {"title": "should be responsive on mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-766febde79a29f963233", "file": "dashboard.spec.ts", "line": 92, "column": 3}, {"title": "should handle Arabic/English language toggle", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-4fd4e988d3e1e04b51dc", "file": "dashboard.spec.ts", "line": 118, "column": 3}, {"title": "should load without JavaScript errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-87b54f2a8b6203e812b7", "file": "dashboard.spec.ts", "line": 150, "column": 3}, {"title": "should display dashboard with key metrics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-399d1ece31921f056514", "file": "dashboard.spec.ts", "line": 20, "column": 3}, {"title": "should display employee statistics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-b0b508266d715b0bff0e", "file": "dashboard.spec.ts", "line": 31, "column": 3}, {"title": "should have working navigation menu", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-f73def6c63f5b1c96a39", "file": "dashboard.spec.ts", "line": 39, "column": 3}, {"title": "should display charts and visualizations", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-3d8a7f6088b1c52e23d2", "file": "dashboard.spec.ts", "line": 66, "column": 3}, {"title": "should be responsive on mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-d48bb34bf1d4d67ae168", "file": "dashboard.spec.ts", "line": 92, "column": 3}, {"title": "should handle Arabic/English language toggle", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-ed7a49a87525c46cad54", "file": "dashboard.spec.ts", "line": 118, "column": 3}, {"title": "should load without JavaScript errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-2f36708e7766c89e3ba7", "file": "dashboard.spec.ts", "line": 150, "column": 3}, {"title": "should display dashboard with key metrics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-58cce0cdeb9cf5e6bbcf", "file": "dashboard.spec.ts", "line": 20, "column": 3}, {"title": "should display employee statistics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-18f91b251abd4b60357f", "file": "dashboard.spec.ts", "line": 31, "column": 3}, {"title": "should have working navigation menu", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-463a906136659b2db4e0", "file": "dashboard.spec.ts", "line": 39, "column": 3}, {"title": "should display charts and visualizations", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-774c637abcd61d93c59f", "file": "dashboard.spec.ts", "line": 66, "column": 3}, {"title": "should be responsive on mobile viewport", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-3947a19fda162d698c70", "file": "dashboard.spec.ts", "line": 92, "column": 3}, {"title": "should handle Arabic/English language toggle", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-4115755cdd770bdc91f4", "file": "dashboard.spec.ts", "line": 118, "column": 3}, {"title": "should load without JavaScript errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "90cda532ab82d274b30b-ef0915db444932b85ef8", "file": "dashboard.spec.ts", "line": 150, "column": 3}]}]}, {"title": "visual-regression.e2e.test.ts", "file": "visual-regression.e2e.test.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Visual Regression Tests", "file": "visual-regression.e2e.test.ts", "line": 3, "column": 6, "specs": [], "suites": [{"title": "Component Screenshots", "file": "visual-regression.e2e.test.ts", "line": 15, "column": 8, "specs": [{"title": "should match global search component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-135ad25f8091d90447dc", "file": "visual-regression.e2e.test.ts", "line": 16, "column": 5}, {"title": "should match export dialog component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-b9297d2fee2bfdaa87b0", "file": "visual-regression.e2e.test.ts", "line": 27, "column": 5}, {"title": "should match notification center component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-9ccb596284841a85cfdd", "file": "visual-regression.e2e.test.ts", "line": 38, "column": 5}, {"title": "should match global search component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-d1e2bb86248ade87a7c8", "file": "visual-regression.e2e.test.ts", "line": 16, "column": 5}, {"title": "should match export dialog component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-2890dc4818c4c0832ca3", "file": "visual-regression.e2e.test.ts", "line": 27, "column": 5}, {"title": "should match notification center component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-dec96a6faff63e94f3dd", "file": "visual-regression.e2e.test.ts", "line": 38, "column": 5}, {"title": "should match global search component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-222d05a9712b6381ce21", "file": "visual-regression.e2e.test.ts", "line": 16, "column": 5}, {"title": "should match export dialog component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-e581a1c2a61a5127208b", "file": "visual-regression.e2e.test.ts", "line": 27, "column": 5}, {"title": "should match notification center component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-c251e7882456f535a93d", "file": "visual-regression.e2e.test.ts", "line": 38, "column": 5}, {"title": "should match global search component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-cf6133bdd56a713e640c", "file": "visual-regression.e2e.test.ts", "line": 16, "column": 5}, {"title": "should match export dialog component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-becbcc44c3e10db288d7", "file": "visual-regression.e2e.test.ts", "line": 27, "column": 5}, {"title": "should match notification center component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-3993239d13a7246555ce", "file": "visual-regression.e2e.test.ts", "line": 38, "column": 5}, {"title": "should match global search component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-30419518c69efd52f054", "file": "visual-regression.e2e.test.ts", "line": 16, "column": 5}, {"title": "should match export dialog component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-53c9755b43605425084b", "file": "visual-regression.e2e.test.ts", "line": 27, "column": 5}, {"title": "should match notification center component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-2710f6a37f22f0c5f3b0", "file": "visual-regression.e2e.test.ts", "line": 38, "column": 5}]}, {"title": "State Screenshots", "file": "visual-regression.e2e.test.ts", "line": 50, "column": 8, "specs": [{"title": "should match search with results", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-741ad27156fd20197acb", "file": "visual-regression.e2e.test.ts", "line": 51, "column": 5}, {"title": "should match search with suggestions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-5e628be36c579804c893", "file": "visual-regression.e2e.test.ts", "line": 65, "column": 5}, {"title": "should match export with progress", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-360bf91661f6968b31e5", "file": "visual-regression.e2e.test.ts", "line": 79, "column": 5}, {"title": "should match search with results", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-eef4f873539cdb4d6376", "file": "visual-regression.e2e.test.ts", "line": 51, "column": 5}, {"title": "should match search with suggestions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-eb77b5b4c4883dfcbded", "file": "visual-regression.e2e.test.ts", "line": 65, "column": 5}, {"title": "should match export with progress", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-1b8b53a4a234e2846e29", "file": "visual-regression.e2e.test.ts", "line": 79, "column": 5}, {"title": "should match search with results", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-715814ff9a980878e1ce", "file": "visual-regression.e2e.test.ts", "line": 51, "column": 5}, {"title": "should match search with suggestions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-e1eaa55ef5692175909b", "file": "visual-regression.e2e.test.ts", "line": 65, "column": 5}, {"title": "should match export with progress", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-0e288af79984bae9caf7", "file": "visual-regression.e2e.test.ts", "line": 79, "column": 5}, {"title": "should match search with results", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-e6d625821b199e7728a2", "file": "visual-regression.e2e.test.ts", "line": 51, "column": 5}, {"title": "should match search with suggestions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-a56643cf8c9b07e807ac", "file": "visual-regression.e2e.test.ts", "line": 65, "column": 5}, {"title": "should match export with progress", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-ee9d71824c00fd2df7e3", "file": "visual-regression.e2e.test.ts", "line": 79, "column": 5}, {"title": "should match search with results", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-4d1db97febbf93b10585", "file": "visual-regression.e2e.test.ts", "line": 51, "column": 5}, {"title": "should match search with suggestions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-c764b1e80dcb96cada9d", "file": "visual-regression.e2e.test.ts", "line": 65, "column": 5}, {"title": "should match export with progress", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-201f2b857d2547d67339", "file": "visual-regression.e2e.test.ts", "line": 79, "column": 5}]}, {"title": "Responsive Screenshots", "file": "visual-regression.e2e.test.ts", "line": 94, "column": 8, "specs": [{"title": "should match mobile layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-779cca7d9ea025509584", "file": "visual-regression.e2e.test.ts", "line": 95, "column": 5}, {"title": "should match tablet layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-34a5f4b789f733a56190", "file": "visual-regression.e2e.test.ts", "line": 107, "column": 5}, {"title": "should match desktop layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-040d6e6eb95b6b096758", "file": "visual-regression.e2e.test.ts", "line": 119, "column": 5}, {"title": "should match mobile layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-6dc6978a34764cfde3c3", "file": "visual-regression.e2e.test.ts", "line": 95, "column": 5}, {"title": "should match tablet layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-e51f1dc9809f9b8a27f4", "file": "visual-regression.e2e.test.ts", "line": 107, "column": 5}, {"title": "should match desktop layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-c8067a0587aa4ab54cbe", "file": "visual-regression.e2e.test.ts", "line": 119, "column": 5}, {"title": "should match mobile layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-c07dde6dbaf4b9980067", "file": "visual-regression.e2e.test.ts", "line": 95, "column": 5}, {"title": "should match tablet layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-4f1563deeb301de4c3fc", "file": "visual-regression.e2e.test.ts", "line": 107, "column": 5}, {"title": "should match desktop layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-d93eca94d3040f36d61b", "file": "visual-regression.e2e.test.ts", "line": 119, "column": 5}, {"title": "should match mobile layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-237d8bae3dc4bccfa188", "file": "visual-regression.e2e.test.ts", "line": 95, "column": 5}, {"title": "should match tablet layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-f48c3ebbf86f33e39294", "file": "visual-regression.e2e.test.ts", "line": 107, "column": 5}, {"title": "should match desktop layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-c0bad968a49165fad644", "file": "visual-regression.e2e.test.ts", "line": 119, "column": 5}, {"title": "should match mobile layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-162a1e6b24a7b31ac22f", "file": "visual-regression.e2e.test.ts", "line": 95, "column": 5}, {"title": "should match tablet layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-460b23bdce9a1c4ceaf7", "file": "visual-regression.e2e.test.ts", "line": 107, "column": 5}, {"title": "should match desktop layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-6a44a131513b30c07f37", "file": "visual-regression.e2e.test.ts", "line": 119, "column": 5}]}, {"title": "Theme Screenshots", "file": "visual-regression.e2e.test.ts", "line": 132, "column": 8, "specs": [{"title": "should match light theme", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-775c4c5055388945543d", "file": "visual-regression.e2e.test.ts", "line": 133, "column": 5}, {"title": "should match dark theme", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-a6ed383216e7f1c69b79", "file": "visual-regression.e2e.test.ts", "line": 146, "column": 5}, {"title": "should match light theme", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-be9464c213f54212d9ef", "file": "visual-regression.e2e.test.ts", "line": 133, "column": 5}, {"title": "should match dark theme", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-dda9798fbf3b841849c2", "file": "visual-regression.e2e.test.ts", "line": 146, "column": 5}, {"title": "should match light theme", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-f29bbeb69ed773449498", "file": "visual-regression.e2e.test.ts", "line": 133, "column": 5}, {"title": "should match dark theme", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-a8dfb6d84a8f3816846d", "file": "visual-regression.e2e.test.ts", "line": 146, "column": 5}, {"title": "should match light theme", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-7754a3ad842e76ed8f50", "file": "visual-regression.e2e.test.ts", "line": 133, "column": 5}, {"title": "should match dark theme", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-a72df22b5ae9a96c1e96", "file": "visual-regression.e2e.test.ts", "line": 146, "column": 5}, {"title": "should match light theme", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-f735c7322cffea17b745", "file": "visual-regression.e2e.test.ts", "line": 133, "column": 5}, {"title": "should match dark theme", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-3f0b488c3ed4d2e4ee0a", "file": "visual-regression.e2e.test.ts", "line": 146, "column": 5}]}, {"title": "RTL Screenshots", "file": "visual-regression.e2e.test.ts", "line": 160, "column": 8, "specs": [{"title": "should match Arabic RTL layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-7518ca52b1d6f4db1852", "file": "visual-regression.e2e.test.ts", "line": 161, "column": 5}, {"title": "should match RTL search component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-51029316e7e0592276a1", "file": "visual-regression.e2e.test.ts", "line": 176, "column": 5}, {"title": "should match Arabic RTL layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-3330fd8a69176dc2b18e", "file": "visual-regression.e2e.test.ts", "line": 161, "column": 5}, {"title": "should match RTL search component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-c3e15018feb59b6f279f", "file": "visual-regression.e2e.test.ts", "line": 176, "column": 5}, {"title": "should match Arabic RTL layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-aab6593cdbcc506d9f4c", "file": "visual-regression.e2e.test.ts", "line": 161, "column": 5}, {"title": "should match RTL search component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-23731a3b979d4561457e", "file": "visual-regression.e2e.test.ts", "line": 176, "column": 5}, {"title": "should match Arabic RTL layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-6d95a0cb2210987f952e", "file": "visual-regression.e2e.test.ts", "line": 161, "column": 5}, {"title": "should match RTL search component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-cd5666b3e6842a68e636", "file": "visual-regression.e2e.test.ts", "line": 176, "column": 5}, {"title": "should match Arabic RTL layout", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-f85085f218426212d343", "file": "visual-regression.e2e.test.ts", "line": 161, "column": 5}, {"title": "should match RTL search component", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-13f58ed617bfc9fa35c9", "file": "visual-regression.e2e.test.ts", "line": 176, "column": 5}]}, {"title": "Error State Screenshots", "file": "visual-regression.e2e.test.ts", "line": 194, "column": 8, "specs": [{"title": "should match search error state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-b6e7d0765894f57b3e6e", "file": "visual-regression.e2e.test.ts", "line": 195, "column": 5}, {"title": "should match export error state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-208f8856a2d9d35e133e", "file": "visual-regression.e2e.test.ts", "line": 218, "column": 5}, {"title": "should match search error state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-286269d4193fecd94452", "file": "visual-regression.e2e.test.ts", "line": 195, "column": 5}, {"title": "should match export error state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-e69e10b6911ce0aa6e55", "file": "visual-regression.e2e.test.ts", "line": 218, "column": 5}, {"title": "should match search error state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-54920f27e4753a6d065b", "file": "visual-regression.e2e.test.ts", "line": 195, "column": 5}, {"title": "should match export error state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-8d08103972fd79df71c9", "file": "visual-regression.e2e.test.ts", "line": 218, "column": 5}, {"title": "should match search error state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-f97474d8072b1eed8bbc", "file": "visual-regression.e2e.test.ts", "line": 195, "column": 5}, {"title": "should match export error state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-195abd92690976bd1efc", "file": "visual-regression.e2e.test.ts", "line": 218, "column": 5}, {"title": "should match search error state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-6fd40d78af07951058c8", "file": "visual-regression.e2e.test.ts", "line": 195, "column": 5}, {"title": "should match export error state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-1c07b684b14c88a3a57c", "file": "visual-regression.e2e.test.ts", "line": 218, "column": 5}]}, {"title": "Loading State Screenshots", "file": "visual-regression.e2e.test.ts", "line": 242, "column": 8, "specs": [{"title": "should match search loading state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-9510c5090630767acc0c", "file": "visual-regression.e2e.test.ts", "line": 243, "column": 5}, {"title": "should match search loading state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-a884768cb5702981f569", "file": "visual-regression.e2e.test.ts", "line": 243, "column": 5}, {"title": "should match search loading state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-376108ce6f20ead27d80", "file": "visual-regression.e2e.test.ts", "line": 243, "column": 5}, {"title": "should match search loading state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-e2ad6f1cce55542c68fd", "file": "visual-regression.e2e.test.ts", "line": 243, "column": 5}, {"title": "should match search loading state", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "6366c7dd14ef19104324-f3ec0ea396779060cb51", "file": "visual-regression.e2e.test.ts", "line": 243, "column": 5}]}]}]}], "errors": [], "stats": {"startTime": "2025-07-18T11:27:40.344Z", "duration": 71586.601, "expected": 0, "skipped": 276, "unexpected": 9, "flaky": 0}}