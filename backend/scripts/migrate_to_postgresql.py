#!/usr/bin/env python3
"""
CRITICAL FIX: SQLite to PostgreSQL Migration Script
Zero-downtime migration with data validation and rollback capability
"""

import os
import sys
import json
import subprocess
from datetime import datetime
from pathlib import Path

# Add Django project to path
sys.path.append(str(Path(__file__).parent.parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

import django
django.setup()

from django.core.management import call_command
from django.db import connections, transaction
from django.core.serializers import serialize
from django.apps import apps

class DatabaseMigrator:
    """Handles SQLite to PostgreSQL migration with validation"""
    
    def __init__(self):
        self.backup_dir = Path('backups') / f"migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        self.validation_errors = []
        
    def create_backup(self):
        """Create full backup of SQLite database"""
        print("🔄 Creating SQLite backup...")
        
        backup_file = self.backup_dir / 'sqlite_backup.json'
        
        # Export all data to JSON
        with open(backup_file, 'w') as f:
            call_command('dumpdata', 
                        '--natural-foreign', 
                        '--natural-primary',
                        stdout=f)
        
        # Also copy the SQLite file
        sqlite_path = Path('db.sqlite3')
        if sqlite_path.exists():
            import shutil
            shutil.copy2(sqlite_path, self.backup_dir / 'db.sqlite3.backup')
        
        print(f"✅ Backup created in {self.backup_dir}")
        return backup_file
    
    def setup_postgresql(self):
        """Setup PostgreSQL database and run migrations"""
        print("🔄 Setting up PostgreSQL database...")
        
        # Run migrations on PostgreSQL
        call_command('migrate', '--run-syncdb')
        
        print("✅ PostgreSQL database setup complete")
    
    def migrate_data(self, backup_file):
        """Migrate data from backup to PostgreSQL"""
        print("🔄 Migrating data to PostgreSQL...")
        
        # Load data into PostgreSQL
        call_command('loaddata', str(backup_file))
        
        print("✅ Data migration complete")
    
    def validate_migration(self):
        """Validate that migration was successful"""
        print("🔄 Validating migration...")
        
        sqlite_conn = connections['default']  # This will be PostgreSQL now
        
        # Get all models
        models = apps.get_models()
        
        validation_results = {}
        
        for model in models:
            model_name = f"{model._meta.app_label}.{model._meta.model_name}"
            
            try:
                count = model.objects.count()
                validation_results[model_name] = {
                    'count': count,
                    'status': 'success'
                }
                print(f"  ✅ {model_name}: {count} records")
                
            except Exception as e:
                validation_results[model_name] = {
                    'count': 0,
                    'status': 'error',
                    'error': str(e)
                }
                self.validation_errors.append(f"{model_name}: {e}")
                print(f"  ❌ {model_name}: ERROR - {e}")
        
        # Save validation results
        with open(self.backup_dir / 'validation_results.json', 'w') as f:
            json.dump(validation_results, f, indent=2)
        
        if self.validation_errors:
            print(f"⚠️  {len(self.validation_errors)} validation errors found")
            return False
        else:
            print("✅ Migration validation successful")
            return True
    
    def run_migration(self):
        """Execute the complete migration process with monitoring"""
        print("🚀 Starting SQLite to PostgreSQL migration...")
        print("=" * 50)

        start_time = time.time()

        try:
            # Step 1: Create backup
            print("📋 Step 1/5: Creating backup...")
            backup_file = self.create_backup()

            # Step 2: Setup PostgreSQL
            print("📋 Step 2/5: Setting up PostgreSQL...")
            self.setup_postgresql()

            # Step 3: Migrate data with progress monitoring
            print("📋 Step 3/5: Migrating data...")
            self.migrate_data_with_progress(backup_file)

            # Step 4: Validate migration
            print("📋 Step 4/5: Validating migration...")
            if self.validate_migration():
                # Step 5: Performance optimization
                print("📋 Step 5/5: Optimizing performance...")
                self.optimize_postgresql()

                elapsed_time = time.time() - start_time
                print("=" * 50)
                print("🎉 Migration completed successfully!")
                print(f"⏱️ Total time: {elapsed_time:.2f} seconds")
                print(f"📁 Backup stored in: {self.backup_dir}")
                return True
            else:
                print("=" * 50)
                print("❌ Migration validation failed!")
                print("Check validation_results.json for details")
                return False

        except Exception as e:
            elapsed_time = time.time() - start_time
            print(f"💥 Migration failed after {elapsed_time:.2f} seconds: {e}")
            print(f"📁 Backup available in: {self.backup_dir}")
            return False

    def migrate_data_with_progress(self, backup_file):
        """Migrate data with progress monitoring"""
        print("🔄 Migrating data to PostgreSQL with progress tracking...")

        # Load data into PostgreSQL with progress
        import json
        with open(backup_file, 'r') as f:
            data = json.load(f)

        total_objects = len(data)
        print(f"📊 Total objects to migrate: {total_objects}")

        # Process in chunks for better progress tracking
        chunk_size = 1000
        for i in range(0, total_objects, chunk_size):
            chunk = data[i:i + chunk_size]

            # Create temporary file for chunk
            chunk_file = self.backup_dir / f'chunk_{i}.json'
            with open(chunk_file, 'w') as f:
                json.dump(chunk, f)

            # Load chunk
            call_command('loaddata', str(chunk_file))

            # Progress update
            progress = min(i + chunk_size, total_objects)
            percentage = (progress / total_objects) * 100
            print(f"  📈 Progress: {progress}/{total_objects} ({percentage:.1f}%)")

            # Clean up chunk file
            chunk_file.unlink()

        print("✅ Data migration complete")

    def optimize_postgresql(self):
        """Optimize PostgreSQL after migration"""
        print("🔧 Optimizing PostgreSQL performance...")

        from django.db import connection

        with connection.cursor() as cursor:
            # Analyze tables for better query planning
            cursor.execute("ANALYZE;")
            print("  ✅ Database statistics updated")

            # Vacuum to reclaim space
            cursor.execute("VACUUM;")
            print("  ✅ Database vacuumed")

        print("✅ PostgreSQL optimization complete")

if __name__ == '__main__':
    migrator = DatabaseMigrator()
    success = migrator.run_migration()
    sys.exit(0 if success else 1)
