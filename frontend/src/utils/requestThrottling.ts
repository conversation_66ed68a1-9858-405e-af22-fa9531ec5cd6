/**
 * PERFORMANCE FIX: Request Throttling Utility
 * Prevents excessive API calls by implementing intelligent throttling
 */

interface ThrottleConfig {
  maxRequests: number
  timeWindow: number // in milliseconds
  cooldownPeriod: number // in milliseconds
}

interface RequestRecord {
  url: string
  timestamp: number
  count: number
}

class RequestThrottler {
  private static instance: RequestThrottler
  private requestHistory: Map<string, RequestRecord[]> = new Map()
  private blockedUrls: Map<string, number> = new Map() // URL -> unblock timestamp
  
  private defaultConfig: ThrottleConfig = {
    maxRequests: 5,
    timeWindow: 5000, // 5 seconds
    cooldownPeriod: 10000 // 10 seconds
  }

  private constructor() {}

  static getInstance(): RequestThrottler {
    if (!RequestThrottler.instance) {
      RequestThrottler.instance = new RequestThrottler()
    }
    return RequestThrottler.instance
  }

  // Check if request should be allowed
  shouldAllowRequest(url: string, config?: Partial<ThrottleConfig>): boolean {
    const finalConfig = { ...this.defaultConfig, ...config }
    const now = Date.now()
    
    // Check if URL is currently blocked
    const unblockTime = this.blockedUrls.get(url)
    if (unblockTime && now < unblockTime) {
      console.warn(`🚫 Request throttled: ${url} (blocked until ${new Date(unblockTime).toLocaleTimeString()})`)
      return false
    }

    // Clean up expired blocks
    if (unblockTime && now >= unblockTime) {
      this.blockedUrls.delete(url)
    }

    // Get request history for this URL
    const history = this.requestHistory.get(url) || []
    
    // Clean up old requests outside the time window
    const validRequests = history.filter(record => 
      now - record.timestamp < finalConfig.timeWindow
    )

    // Update history
    this.requestHistory.set(url, validRequests)

    // Check if we've exceeded the limit
    if (validRequests.length >= finalConfig.maxRequests) {
      console.warn(`🚫 Request throttled: ${url} (${validRequests.length}/${finalConfig.maxRequests} requests in ${finalConfig.timeWindow}ms)`)
      
      // Block this URL for the cooldown period
      this.blockedUrls.set(url, now + finalConfig.cooldownPeriod)
      return false
    }

    // Record this request
    validRequests.push({
      url,
      timestamp: now,
      count: validRequests.length + 1
    })

    this.requestHistory.set(url, validRequests)
    return true
  }

  // Get throttling status for debugging
  getThrottleStatus(url: string): {
    isBlocked: boolean
    requestCount: number
    timeUntilUnblock?: number
    recentRequests: RequestRecord[]
  } {
    const now = Date.now()
    const unblockTime = this.blockedUrls.get(url)
    const history = this.requestHistory.get(url) || []
    
    const recentRequests = history.filter(record => 
      now - record.timestamp < this.defaultConfig.timeWindow
    )

    return {
      isBlocked: !!(unblockTime && now < unblockTime),
      requestCount: recentRequests.length,
      timeUntilUnblock: unblockTime && now < unblockTime ? unblockTime - now : undefined,
      recentRequests
    }
  }

  // Clear throttling for a specific URL (for testing/debugging)
  clearThrottling(url: string): void {
    this.requestHistory.delete(url)
    this.blockedUrls.delete(url)
  }

  // Clear all throttling (for testing/debugging)
  clearAllThrottling(): void {
    this.requestHistory.clear()
    this.blockedUrls.clear()
  }

  // Get statistics for monitoring
  getStatistics(): {
    totalTrackedUrls: number
    blockedUrls: number
    totalRequests: number
    averageRequestsPerUrl: number
  } {
    const totalTrackedUrls = this.requestHistory.size
    const blockedUrls = this.blockedUrls.size
    const totalRequests = Array.from(this.requestHistory.values())
      .reduce((sum, history) => sum + history.length, 0)
    
    return {
      totalTrackedUrls,
      blockedUrls,
      totalRequests,
      averageRequestsPerUrl: totalTrackedUrls > 0 ? totalRequests / totalTrackedUrls : 0
    }
  }
}

// Export singleton instance
export const requestThrottler = RequestThrottler.getInstance()

// Utility function to wrap fetch with throttling
export const throttledFetch = async (
  url: string, 
  options?: RequestInit,
  throttleConfig?: Partial<ThrottleConfig>
): Promise<Response> => {
  // Check if request should be allowed
  if (!requestThrottler.shouldAllowRequest(url, throttleConfig)) {
    throw new Error(`Request throttled: ${url}`)
  }

  // Proceed with the request
  return fetch(url, options)
}

// Utility function to create throttled API client
export const createThrottledApiClient = (baseURL: string) => {
  return {
    get: async (endpoint: string, options?: RequestInit) => {
      const url = `${baseURL}${endpoint}`
      return throttledFetch(url, { ...options, method: 'GET' })
    },
    post: async (endpoint: string, data?: any, options?: RequestInit) => {
      const url = `${baseURL}${endpoint}`
      return throttledFetch(url, {
        ...options,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers
        },
        body: data ? JSON.stringify(data) : undefined
      })
    },
    put: async (endpoint: string, data?: any, options?: RequestInit) => {
      const url = `${baseURL}${endpoint}`
      return throttledFetch(url, {
        ...options,
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers
        },
        body: data ? JSON.stringify(data) : undefined
      })
    },
    delete: async (endpoint: string, options?: RequestInit) => {
      const url = `${baseURL}${endpoint}`
      return throttledFetch(url, { ...options, method: 'DELETE' })
    }
  }
}

export default requestThrottler
