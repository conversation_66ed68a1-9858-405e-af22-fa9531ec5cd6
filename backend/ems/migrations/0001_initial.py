# Generated by Django 4.2.7 on 2025-07-19 14:54

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AssetCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('name_ar', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name_plural': 'Asset Categories',
            },
        ),
        migrations.CreateModel(
            name='Budget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(max_length=200)),
                ('fiscal_year', models.IntegerField()),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('allocated_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('spent_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('remaining_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='ComplianceFramework',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('region', models.CharField(max_length=100)),
                ('type', models.CharField(choices=[('data_protection', 'Data Protection'), ('financial', 'Financial'), ('industry', 'Industry'), ('security', 'Security'), ('labor', 'Labor')], max_length=20)),
                ('version', models.CharField(default='1.0', max_length=20)),
                ('is_active', models.BooleanField(default=False)),
                ('compliance_score', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('last_assessment', models.DateTimeField(blank=True, null=True)),
                ('next_assessment', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer_id', models.CharField(editable=False, max_length=20, unique=True)),
                ('first_name', models.CharField(max_length=100)),
                ('last_name', models.CharField(blank=True, max_length=100)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('customer_type', models.CharField(choices=[('individual', 'Individual'), ('business', 'Business'), ('enterprise', 'Enterprise')], default='individual', max_length=20)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('suspended', 'Suspended'), ('prospect', 'Prospect')], default='prospect', max_length=20)),
                ('company_name', models.CharField(blank=True, max_length=200)),
                ('company_size', models.CharField(blank=True, max_length=50)),
                ('industry', models.CharField(blank=True, max_length=100)),
                ('address_line1', models.CharField(blank=True, max_length=255)),
                ('address_line2', models.CharField(blank=True, max_length=255)),
                ('city', models.CharField(blank=True, max_length=100)),
                ('state', models.CharField(blank=True, max_length=100)),
                ('postal_code', models.CharField(blank=True, max_length=20)),
                ('country', models.CharField(blank=True, max_length=100)),
                ('source', models.CharField(blank=True, help_text='How customer found us', max_length=50)),
                ('internal_notes', models.TextField(blank=True)),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags', max_length=500)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_contact_date', models.DateTimeField(blank=True, null=True)),
                ('total_orders', models.IntegerField(default=0)),
                ('total_spent', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('satisfaction_score', models.FloatField(default=0.0, help_text='Average satisfaction score')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('name_ar', models.CharField(help_text='Arabic name', max_length=100)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True, help_text='Arabic description')),
                ('budget_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('location', models.CharField(blank=True, max_length=200)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.CharField(max_length=20, unique=True)),
                ('position', models.CharField(max_length=100)),
                ('position_ar', models.CharField(help_text='Arabic position', max_length=100)),
                ('first_name_ar', models.CharField(blank=True, help_text='Arabic first name', max_length=150)),
                ('last_name_ar', models.CharField(blank=True, help_text='Arabic last name', max_length=150)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('gender', models.CharField(choices=[('M', 'Male'), ('F', 'Female')], max_length=1)),
                ('hire_date', models.DateField()),
                ('salary', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('employment_status', models.CharField(choices=[('FULL_TIME', 'Full Time'), ('PART_TIME', 'Part Time'), ('CONTRACT', 'Contract'), ('INTERN', 'Intern'), ('CONSULTANT', 'Consultant')], default='FULL_TIME', max_length=20)),
                ('work_location', models.CharField(blank=True, max_length=200)),
                ('contract_end_date', models.DateField(blank=True, null=True)),
                ('probation_end_date', models.DateField(blank=True, null=True)),
                ('national_id', models.CharField(blank=True, max_length=20)),
                ('passport_number', models.CharField(blank=True, max_length=20)),
                ('bank_account', models.CharField(blank=True, max_length=50)),
                ('emergency_contact', models.CharField(blank=True, max_length=100)),
                ('emergency_phone', models.CharField(blank=True, max_length=20)),
                ('skills', models.TextField(blank=True)),
                ('education', models.TextField(blank=True)),
                ('certifications', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='subordinates', to='ems.employee')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['employee_id'],
            },
        ),
        migrations.CreateModel(
            name='KPI',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(help_text='Arabic name', max_length=200)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(help_text='Arabic description')),
                ('measurement_type', models.CharField(choices=[('NUMBER', 'Number'), ('PERCENTAGE', 'Percentage'), ('CURRENCY', 'Currency'), ('RATIO', 'Ratio'), ('SCORE', 'Score'), ('TIME', 'Time Duration')], max_length=20)),
                ('unit', models.CharField(blank=True, help_text='e.g., %, $, hours', max_length=50)),
                ('unit_ar', models.CharField(blank=True, help_text='Arabic unit', max_length=50)),
                ('frequency', models.CharField(choices=[('DAILY', 'Daily'), ('WEEKLY', 'Weekly'), ('MONTHLY', 'Monthly'), ('QUARTERLY', 'Quarterly'), ('YEARLY', 'Yearly')], max_length=20)),
                ('trend_direction', models.CharField(choices=[('UP', 'Higher is Better'), ('DOWN', 'Lower is Better'), ('TARGET', 'Target Value')], max_length=10)),
                ('formula', models.TextField(blank=True, help_text='Calculation formula or description')),
                ('data_source', models.CharField(blank=True, help_text='Source of data', max_length=200)),
                ('calculation_method', models.TextField(blank=True)),
                ('current_value', models.DecimalField(blank=True, decimal_places=4, help_text='Current KPI value', max_digits=15, null=True)),
                ('target_value', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('warning_threshold', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('critical_threshold', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('last_updated', models.DateTimeField(blank=True, help_text='When the current value was last updated', null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('INACTIVE', 'Inactive'), ('DRAFT', 'Draft'), ('ARCHIVED', 'Archived')], default='ACTIVE', max_length=20)),
                ('is_automated', models.BooleanField(default=False, help_text='Automatically calculated')),
                ('automation_config', models.JSONField(blank=True, default=dict)),
                ('use_ml_prediction', models.BooleanField(default=False, help_text='Use ML for KPI prediction')),
                ('anomaly_detection', models.BooleanField(default=True, help_text='Enable anomaly detection')),
                ('auto_recommendations', models.BooleanField(default=True, help_text='Generate AI recommendations')),
                ('algorithm_config', models.JSONField(blank=True, default=dict, help_text='Algorithm configuration')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'KPI',
                'verbose_name_plural': 'KPIs',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='KPICategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('FINANCIAL', 'Financial'), ('HR', 'Human Resources'), ('OPERATIONS', 'Operations'), ('SALES', 'Sales & Marketing'), ('CUSTOMER', 'Customer Service'), ('QUALITY', 'Quality Management'), ('INNOVATION', 'Innovation & Development'), ('COMPLIANCE', 'Compliance & Risk'), ('SUSTAINABILITY', 'Sustainability'), ('STRATEGIC', 'Strategic Goals')], max_length=50, unique=True)),
                ('name_ar', models.CharField(help_text='Arabic name', max_length=100)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True, help_text='Arabic description')),
                ('icon', models.CharField(default='BarChart3', max_length=50)),
                ('color', models.CharField(default='from-blue-500 to-blue-600', max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('sort_order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'KPI Categories',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='LeaveType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('name_ar', models.CharField(max_length=50)),
                ('days_allowed', models.IntegerField()),
                ('is_paid', models.BooleanField(default=True)),
                ('requires_approval', models.BooleanField(default=True)),
                ('carry_forward', models.BooleanField(default=False)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='MLModel',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('type', models.CharField(choices=[('regression', 'Regression'), ('classification', 'Classification'), ('clustering', 'Clustering'), ('forecasting', 'Forecasting')], max_length=20)),
                ('version', models.CharField(max_length=20)),
                ('accuracy', models.FloatField(validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('features', models.JSONField(default=list)),
                ('hyperparameters', models.JSONField(default=dict)),
                ('training_data_info', models.JSONField(default=dict)),
                ('status', models.CharField(choices=[('active', 'Active'), ('training', 'Training'), ('deprecated', 'Deprecated')], default='training', max_length=20)),
                ('last_trained', models.DateTimeField(auto_now_add=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
            ],
        ),
        migrations.CreateModel(
            name='PayrollPeriod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('is_processed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='ProductCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('name_ar', models.CharField(blank=True, max_length=100)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Product Categories',
            },
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True)),
                ('client', models.CharField(blank=True, max_length=200)),
                ('budget_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('actual_start_date', models.DateField(blank=True, null=True)),
                ('actual_end_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('PLANNING', 'Planning'), ('IN_PROGRESS', 'In Progress'), ('ON_HOLD', 'On Hold'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')], default='PLANNING', max_length=20)),
                ('priority', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('CRITICAL', 'Critical')], default='MEDIUM', max_length=20)),
                ('progress_percentage', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department')),
                ('project_manager', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_projects', to='ems.employee')),
                ('team_members', models.ManyToManyField(blank=True, related_name='projects', to='ems.employee')),
            ],
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('SUPERADMIN', 'Super Administrator'), ('ADMIN', 'Administrator'), ('HR_MANAGER', 'HR Manager'), ('DEPARTMENT_MANAGER', 'Department Manager'), ('PROJECT_MANAGER', 'Project Manager'), ('FINANCE_MANAGER', 'Finance Manager'), ('EMPLOYEE', 'Employee'), ('INTERN', 'Intern')], max_length=50, unique=True)),
                ('name_ar', models.CharField(max_length=50)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('permissions', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('contact_person', models.CharField(blank=True, max_length=100)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('address', models.TextField(blank=True)),
                ('website', models.URLField(blank=True)),
                ('tax_number', models.CharField(blank=True, max_length=50)),
                ('payment_terms', models.CharField(blank=True, max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Tenant',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('domain', models.CharField(max_length=100, unique=True)),
                ('subdomain', models.CharField(max_length=50, unique=True)),
                ('plan', models.CharField(choices=[('basic', 'Basic'), ('professional', 'Professional'), ('enterprise', 'Enterprise'), ('custom', 'Custom')], default='basic', max_length=20)),
                ('status', models.CharField(choices=[('active', 'Active'), ('suspended', 'Suspended'), ('trial', 'Trial'), ('expired', 'Expired')], default='trial', max_length=20)),
                ('settings', models.JSONField(default=dict)),
                ('limits', models.JSONField(default=dict)),
                ('features', models.JSONField(default=dict)),
                ('billing_info', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('metadata', models.JSONField(default=dict)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Workflow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True)),
                ('category', models.CharField(choices=[('hr', 'Human Resources'), ('finance', 'Finance'), ('sales', 'Sales'), ('operations', 'Operations'), ('it', 'Information Technology'), ('general', 'General')], default='general', max_length=20)),
                ('category_ar', models.CharField(blank=True, max_length=100)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('draft', 'Draft'), ('archived', 'Archived')], default='draft', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=20)),
                ('trigger_event', models.CharField(blank=True, max_length=100)),
                ('conditions', models.TextField(blank=True)),
                ('conditions_ar', models.TextField(blank=True)),
                ('actions', models.TextField(blank=True)),
                ('actions_ar', models.TextField(blank=True)),
                ('is_automated', models.BooleanField(default=False)),
                ('next_run', models.DateTimeField(blank=True, null=True)),
                ('last_run', models.DateTimeField(blank=True, null=True)),
                ('run_count', models.IntegerField(default=0)),
                ('success_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='created_workflows', to='ems.employee')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/')),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('address', models.TextField(blank=True)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('emergency_contact_name', models.CharField(blank=True, max_length=100)),
                ('emergency_contact_phone', models.CharField(blank=True, max_length=20)),
                ('preferred_language', models.CharField(choices=[('ar', 'Arabic'), ('en', 'English')], default='ar', max_length=2)),
                ('timezone', models.CharField(default='Asia/Riyadh', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('role', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.role')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='TrainingProgram',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True)),
                ('instructor', models.CharField(max_length=200)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('duration_hours', models.IntegerField()),
                ('max_participants', models.IntegerField()),
                ('cost_per_participant', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('location', models.CharField(blank=True, max_length=200)),
                ('is_mandatory', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('planned', 'Planned'), ('active', 'Active'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='planned', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department')),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='Task',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True)),
                ('due_date', models.DateTimeField()),
                ('start_date', models.DateTimeField(blank=True, null=True)),
                ('completion_date', models.DateTimeField(blank=True, null=True)),
                ('estimated_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True)),
                ('actual_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True)),
                ('status', models.CharField(choices=[('TODO', 'To Do'), ('IN_PROGRESS', 'In Progress'), ('REVIEW', 'Under Review'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')], default='TODO', max_length=20)),
                ('priority', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('URGENT', 'Urgent')], default='MEDIUM', max_length=20)),
                ('progress_percentage', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('tags', models.CharField(blank=True, max_length=500)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_to', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_tasks', to='ems.employee')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_tasks', to='ems.employee')),
                ('dependencies', models.ManyToManyField(blank=True, to='ems.task')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='ems.project')),
            ],
        ),
        migrations.CreateModel(
            name='SalesOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=50, unique=True)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('processing', 'Processing'), ('shipped', 'Shipped'), ('delivered', 'Delivered'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=20)),
                ('order_date', models.DateField()),
                ('delivery_date', models.DateField(blank=True, null=True)),
                ('items_count', models.IntegerField(default=0)),
                ('discount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('tax', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_sales_orders', to='ems.employee')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales_orders', to='ems.customer')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('type', models.CharField(choices=[('employee', 'Employee Report'), ('department', 'Department Report'), ('financial', 'Financial Report'), ('performance', 'Performance Report'), ('attendance', 'Attendance Report'), ('payroll', 'Payroll Report')], max_length=20)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('completed_date', models.DateTimeField(blank=True, null=True)),
                ('file_size', models.CharField(blank=True, max_length=50)),
                ('file_url', models.URLField(blank=True)),
                ('parameters', models.JSONField(blank=True, default=dict)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='created_reports', to='ems.employee')),
            ],
            options={
                'ordering': ['-created_date'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('po_number', models.CharField(max_length=50, unique=True)),
                ('order_date', models.DateField()),
                ('expected_delivery', models.DateField(blank=True, null=True)),
                ('actual_delivery', models.DateField(blank=True, null=True)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('currency', models.CharField(default='SAR', max_length=3)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('PENDING', 'Pending Approval'), ('APPROVED', 'Approved'), ('ORDERED', 'Ordered'), ('RECEIVED', 'Received'), ('CANCELLED', 'Cancelled')], default='DRAFT', max_length=20)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_pos', to='ems.employee')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.project')),
                ('requested_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requested_pos', to='ems.employee')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.supplier')),
            ],
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('sku', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('brand', models.CharField(blank=True, max_length=100)),
                ('brand_ar', models.CharField(blank=True, max_length=100)),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('cost_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('quantity_in_stock', models.IntegerField(default=0)),
                ('minimum_stock_level', models.IntegerField(default=0)),
                ('maximum_stock_level', models.IntegerField(blank=True, null=True)),
                ('reorder_point', models.IntegerField(default=0)),
                ('unit_of_measure', models.CharField(default='piece', max_length=50)),
                ('unit_of_measure_ar', models.CharField(blank=True, max_length=50)),
                ('barcode', models.CharField(blank=True, max_length=100)),
                ('weight', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True)),
                ('dimensions', models.CharField(blank=True, max_length=100)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('discontinued', 'Discontinued')], default='active', max_length=20)),
                ('location', models.CharField(blank=True, max_length=100)),
                ('location_ar', models.CharField(blank=True, max_length=100)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('batch_number', models.CharField(blank=True, max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='ems.productcategory')),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.supplier')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PerformanceReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('review_period_start', models.DateField()),
                ('review_period_end', models.DateField()),
                ('review_type', models.CharField(choices=[('annual', 'Annual Review'), ('quarterly', 'Quarterly Review'), ('probation', 'Probation Review'), ('project', 'Project Review')], default='annual', max_length=20)),
                ('overall_rating', models.IntegerField(choices=[(1, 'Poor'), (2, 'Below Average'), (3, 'Average'), (4, 'Good'), (5, 'Excellent')])),
                ('goals_achievement', models.IntegerField(choices=[(1, 'Poor'), (2, 'Below Average'), (3, 'Average'), (4, 'Good'), (5, 'Excellent')])),
                ('communication_skills', models.IntegerField(choices=[(1, 'Poor'), (2, 'Below Average'), (3, 'Average'), (4, 'Good'), (5, 'Excellent')])),
                ('teamwork', models.IntegerField(choices=[(1, 'Poor'), (2, 'Below Average'), (3, 'Average'), (4, 'Good'), (5, 'Excellent')])),
                ('leadership', models.IntegerField(blank=True, choices=[(1, 'Poor'), (2, 'Below Average'), (3, 'Average'), (4, 'Good'), (5, 'Excellent')], null=True)),
                ('technical_skills', models.IntegerField(choices=[(1, 'Poor'), (2, 'Below Average'), (3, 'Average'), (4, 'Good'), (5, 'Excellent')])),
                ('strengths', models.TextField()),
                ('areas_for_improvement', models.TextField()),
                ('goals_for_next_period', models.TextField()),
                ('reviewer_comments', models.TextField()),
                ('employee_comments', models.TextField(blank=True)),
                ('hr_comments', models.TextField(blank=True)),
                ('is_final', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='performance_reviews', to='ems.employee')),
                ('reviewer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conducted_reviews', to='ems.employee')),
            ],
            options={
                'ordering': ['-review_period_end'],
            },
        ),
        migrations.CreateModel(
            name='MLPrediction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('prediction_type', models.CharField(choices=[('employee_performance', 'Employee Performance'), ('turnover_risk', 'Turnover Risk'), ('project_success', 'Project Success'), ('budget_forecast', 'Budget Forecast'), ('resource_demand', 'Resource Demand'), ('kpi_forecast', 'KPI Forecast'), ('kpi_anomaly', 'KPI Anomaly Detection'), ('kpi_optimization', 'KPI Optimization')], max_length=30)),
                ('input_data', models.JSONField()),
                ('prediction_result', models.JSONField()),
                ('confidence', models.FloatField(validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('factors', models.JSONField(default=list)),
                ('recommendations', models.JSONField(default=list)),
                ('related_object_type', models.CharField(blank=True, max_length=50)),
                ('related_object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('metadata', models.JSONField(default=dict)),
                ('model', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.mlmodel')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('is_read', models.BooleanField(default=False)),
                ('is_important', models.BooleanField(default=False)),
                ('attachment', models.FileField(blank=True, null=True, upload_to='messages/')),
                ('sent_at', models.DateTimeField(auto_now_add=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('parent_message', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='ems.message')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_messages', to='ems.employee')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_messages', to='ems.employee')),
            ],
            options={
                'ordering': ['-sent_at'],
            },
        ),
        migrations.CreateModel(
            name='Meeting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField()),
                ('location', models.CharField(blank=True, max_length=200)),
                ('meeting_link', models.URLField(blank=True)),
                ('agenda', models.TextField(blank=True)),
                ('agenda_ar', models.TextField(blank=True)),
                ('minutes', models.TextField(blank=True)),
                ('minutes_ar', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('SCHEDULED', 'Scheduled'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled'), ('POSTPONED', 'Postponed')], default='SCHEDULED', max_length=20)),
                ('is_recurring', models.BooleanField(default=False)),
                ('recurrence_pattern', models.CharField(blank=True, max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('attendees', models.ManyToManyField(related_name='meetings', to='ems.employee')),
                ('organizer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='organized_meetings', to='ems.employee')),
            ],
        ),
        migrations.CreateModel(
            name='LeaveRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('days_requested', models.IntegerField()),
                ('reason', models.TextField()),
                ('reason_ar', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('CANCELLED', 'Cancelled')], default='PENDING', max_length=20)),
                ('approval_date', models.DateTimeField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True)),
                ('rejection_reason_ar', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_leaves', to='ems.employee')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.leavetype')),
            ],
        ),
        migrations.CreateModel(
            name='KPITarget',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('target_value', models.DecimalField(decimal_places=4, max_digits=15)),
                ('target_type', models.CharField(choices=[('ABSOLUTE', 'Absolute Value'), ('PERCENTAGE_CHANGE', 'Percentage Change'), ('RELATIVE', 'Relative to Baseline')], default='ABSOLUTE', max_length=20)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('description', models.TextField(blank=True)),
                ('is_stretch_goal', models.BooleanField(default=False)),
                ('weight', models.DecimalField(decimal_places=2, default=1.0, max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_kpi_targets', to='ems.employee')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department')),
                ('employee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
                ('kpi', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='targets', to='ems.kpi')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.project')),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='KPIAlert',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('alert_type', models.CharField(choices=[('THRESHOLD_BREACH', 'Threshold Breach'), ('TARGET_MISSED', 'Target Missed'), ('TREND_CHANGE', 'Trend Change'), ('DATA_QUALITY', 'Data Quality Issue'), ('MISSING_DATA', 'Missing Data')], max_length=20)),
                ('severity', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('CRITICAL', 'Critical')], max_length=10)),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('message_ar', models.TextField()),
                ('current_value', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('threshold_value', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('target_value', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('ACKNOWLEDGED', 'Acknowledged'), ('RESOLVED', 'Resolved'), ('DISMISSED', 'Dismissed')], default='ACTIVE', max_length=20)),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('resolution_notes', models.TextField(blank=True)),
                ('notification_sent', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('acknowledged_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='acknowledged_alerts', to='ems.employee')),
                ('kpi', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='ems.kpi')),
                ('notified_users', models.ManyToManyField(blank=True, related_name='kpi_alert_notifications', to='ems.employee')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_alerts', to='ems.employee')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='kpi',
            name='category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='kpis', to='ems.kpicategory'),
        ),
        migrations.AddField(
            model_name='kpi',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_kpis', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='kpi',
            name='ml_model',
            field=models.ForeignKey(blank=True, help_text='ML model for predictions', null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.mlmodel'),
        ),
        migrations.AddField(
            model_name='kpi',
            name='owner',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='owned_kpis', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='kpi',
            name='updated_by',
            field=models.ForeignKey(blank=True, help_text='Who last updated the value', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_kpis', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='kpi',
            name='visible_to_roles',
            field=models.ManyToManyField(blank=True, help_text='Roles that can view this KPI', to='ems.role'),
        ),
        migrations.CreateModel(
            name='JobPosting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True)),
                ('requirements', models.TextField()),
                ('requirements_ar', models.TextField(blank=True)),
                ('salary_min', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('salary_max', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('employment_type', models.CharField(choices=[('full_time', 'Full Time'), ('part_time', 'Part Time'), ('contract', 'Contract'), ('internship', 'Internship')], default='full_time', max_length=50)),
                ('location', models.CharField(max_length=200)),
                ('location_ar', models.CharField(blank=True, max_length=200)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('active', 'Active'), ('closed', 'Closed'), ('on_hold', 'On Hold')], default='draft', max_length=20)),
                ('posted_date', models.DateField(auto_now_add=True)),
                ('closing_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.department')),
                ('posted_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
            ],
            options={
                'ordering': ['-posted_date'],
            },
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=50, unique=True)),
                ('issue_date', models.DateField()),
                ('due_date', models.DateField()),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=12)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('sent', 'Sent'), ('paid', 'Paid'), ('overdue', 'Overdue'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('notes', models.TextField(blank=True)),
                ('payment_terms', models.CharField(blank=True, max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoices', to='ems.customer')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Expense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True)),
                ('category', models.CharField(choices=[('TRAVEL', 'Travel'), ('OFFICE_SUPPLIES', 'Office Supplies'), ('EQUIPMENT', 'Equipment'), ('SOFTWARE', 'Software'), ('TRAINING', 'Training'), ('MARKETING', 'Marketing'), ('UTILITIES', 'Utilities'), ('RENT', 'Rent'), ('OTHER', 'Other')], max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='SAR', max_length=3)),
                ('expense_date', models.DateField()),
                ('receipt_file', models.FileField(blank=True, null=True, upload_to='receipts/')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('PAID', 'Paid')], default='PENDING', max_length=20)),
                ('approval_date', models.DateTimeField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_expenses', to='ems.employee')),
                ('budget', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.budget')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.project')),
            ],
        ),
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('category', models.CharField(choices=[('POLICY', 'Policy'), ('PROCEDURE', 'Procedure'), ('FORM', 'Form'), ('MANUAL', 'Manual'), ('REPORT', 'Report'), ('CONTRACT', 'Contract'), ('OTHER', 'Other')], max_length=20)),
                ('file', models.FileField(upload_to='documents/')),
                ('version', models.CharField(default='1.0', max_length=20)),
                ('is_public', models.BooleanField(default=False)),
                ('tags', models.CharField(blank=True, max_length=500)),
                ('download_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('access_permissions', models.ManyToManyField(blank=True, related_name='accessible_documents', to='ems.employee')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department')),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
            ],
        ),
        migrations.AddField(
            model_name='department',
            name='manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_departments', to='ems.employee'),
        ),
        migrations.CreateModel(
            name='DataSubjectRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('request_type', models.CharField(choices=[('access', 'Data Access'), ('rectification', 'Data Rectification'), ('erasure', 'Data Erasure'), ('portability', 'Data Portability'), ('restriction', 'Processing Restriction'), ('objection', 'Processing Objection')], max_length=20)),
                ('requester_name', models.CharField(max_length=200)),
                ('requester_email', models.EmailField(max_length=254)),
                ('description', models.TextField()),
                ('status', models.CharField(choices=[('received', 'Received'), ('processing', 'Processing'), ('completed', 'Completed'), ('rejected', 'Rejected')], default='received', max_length=20)),
                ('submitted_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('response', models.TextField(blank=True)),
                ('documents', models.JSONField(default=list)),
                ('metadata', models.JSONField(default=dict)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
            ],
            options={
                'ordering': ['-submitted_at'],
            },
        ),
        migrations.CreateModel(
            name='DataProtectionRecord',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('data_type', models.CharField(max_length=200)),
                ('purpose', models.TextField()),
                ('legal_basis', models.CharField(choices=[('consent', 'Consent'), ('contract', 'Contract'), ('legal_obligation', 'Legal Obligation'), ('vital_interests', 'Vital Interests'), ('public_task', 'Public Task'), ('legitimate_interests', 'Legitimate Interests')], max_length=30)),
                ('data_subjects', models.JSONField(default=list)),
                ('retention_period', models.PositiveIntegerField(help_text='Retention period in years')),
                ('processing_activities', models.JSONField(default=list)),
                ('third_party_sharing', models.BooleanField(default=False)),
                ('cross_border_transfer', models.BooleanField(default=False)),
                ('security_measures', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('data_controller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='controlled_data', to='ems.employee')),
            ],
        ),
        migrations.CreateModel(
            name='CostCenter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True)),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField(blank=True)),
                ('budget_allocated', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('budget_spent', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cost_centers', to='ems.department')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
            ],
            options={
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='ComplianceRequirement',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=300)),
                ('description', models.TextField()),
                ('category', models.CharField(max_length=100)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], max_length=20)),
                ('status', models.CharField(choices=[('compliant', 'Compliant'), ('non_compliant', 'Non-Compliant'), ('partial', 'Partial'), ('not_assessed', 'Not Assessed')], default='not_assessed', max_length=20)),
                ('evidence_required', models.JSONField(default=list)),
                ('controls', models.JSONField(default=list)),
                ('last_review', models.DateTimeField(blank=True, null=True)),
                ('next_review', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
                ('framework', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requirements', to='ems.complianceframework')),
            ],
        ),
        migrations.CreateModel(
            name='ComplianceEvidence',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('type', models.CharField(choices=[('document', 'Document'), ('policy', 'Policy'), ('procedure', 'Procedure'), ('training', 'Training'), ('audit', 'Audit'), ('certification', 'Certification')], max_length=20)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('file', models.FileField(blank=True, null=True, upload_to='compliance/evidence/')),
                ('url', models.URLField(blank=True)),
                ('status', models.CharField(choices=[('valid', 'Valid'), ('expired', 'Expired'), ('pending_review', 'Pending Review')], default='valid', max_length=20)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('expiry_date', models.DateTimeField(blank=True, null=True)),
                ('metadata', models.JSONField(default=dict)),
                ('requirement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='evidence', to='ems.compliancerequirement')),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
            ],
        ),
        migrations.CreateModel(
            name='ComplianceAssessment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('assessment_type', models.CharField(choices=[('self', 'Self Assessment'), ('internal', 'Internal Audit'), ('external', 'External Audit'), ('regulatory', 'Regulatory Inspection')], max_length=20)),
                ('assessor', models.CharField(max_length=200)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('planned', 'Planned'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='planned', max_length=20)),
                ('overall_score', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('findings', models.JSONField(default=list)),
                ('recommendations', models.JSONField(default=list)),
                ('next_assessment', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
                ('framework', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assessments', to='ems.complianceframework')),
            ],
        ),
        migrations.AddField(
            model_name='budget',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee'),
        ),
        migrations.AddField(
            model_name='budget',
            name='department',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='budgets', to='ems.department'),
        ),
        migrations.AddField(
            model_name='budget',
            name='project',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='budgets', to='ems.project'),
        ),
        migrations.CreateModel(
            name='AutomationRule',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('category', models.CharField(choices=[('hr', 'HR'), ('finance', 'Finance'), ('project', 'Project'), ('system', 'System'), ('custom', 'Custom')], max_length=20)),
                ('trigger_config', models.JSONField()),
                ('conditions', models.JSONField(default=list)),
                ('actions', models.JSONField(default=list)),
                ('is_active', models.BooleanField(default=True)),
                ('execution_count', models.PositiveIntegerField(default=0)),
                ('success_count', models.PositiveIntegerField(default=0)),
                ('last_executed', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
            ],
        ),
        migrations.CreateModel(
            name='AutomationExecution',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('start_time', models.DateTimeField(auto_now_add=True)),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('context_data', models.JSONField(default=dict)),
                ('result_data', models.JSONField(default=dict)),
                ('error_message', models.TextField(blank=True)),
                ('logs', models.JSONField(default=list)),
                ('rule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='executions', to='ems.automationrule')),
            ],
            options={
                'ordering': ['-start_time'],
            },
        ),
        migrations.CreateModel(
            name='AuditTrail',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('action', models.CharField(max_length=100)),
                ('resource', models.CharField(max_length=100)),
                ('resource_id', models.CharField(blank=True, max_length=100)),
                ('old_values', models.JSONField(blank=True, null=True)),
                ('new_values', models.JSONField(blank=True, null=True)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('compliance_relevant', models.BooleanField(default=False)),
                ('retention_date', models.DateTimeField()),
                ('metadata', models.JSONField(default=dict)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='Asset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('asset_id', models.CharField(max_length=50, unique=True)),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('serial_number', models.CharField(blank=True, max_length=100)),
                ('model', models.CharField(blank=True, max_length=100)),
                ('manufacturer', models.CharField(blank=True, max_length=100)),
                ('purchase_date', models.DateField()),
                ('purchase_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('current_value', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('warranty_expiry', models.DateField(blank=True, null=True)),
                ('location', models.CharField(blank=True, max_length=200)),
                ('status', models.CharField(choices=[('AVAILABLE', 'Available'), ('IN_USE', 'In Use'), ('MAINTENANCE', 'Under Maintenance'), ('RETIRED', 'Retired'), ('LOST', 'Lost'), ('DAMAGED', 'Damaged')], default='AVAILABLE', max_length=20)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.assetcategory')),
            ],
        ),
        migrations.CreateModel(
            name='Announcement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200)),
                ('content', models.TextField()),
                ('content_ar', models.TextField(blank=True)),
                ('priority', models.CharField(choices=[('LOW', 'Low'), ('NORMAL', 'Normal'), ('HIGH', 'High'), ('URGENT', 'Urgent')], default='NORMAL', max_length=10)),
                ('is_published', models.BooleanField(default=False)),
                ('publish_date', models.DateTimeField(blank=True, null=True)),
                ('expiry_date', models.DateTimeField(blank=True, null=True)),
                ('attachment', models.FileField(blank=True, null=True, upload_to='announcements/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
                ('target_departments', models.ManyToManyField(blank=True, to='ems.department')),
                ('target_employees', models.ManyToManyField(blank=True, related_name='received_announcements', to='ems.employee')),
            ],
        ),
        migrations.CreateModel(
            name='Activity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(choices=[('LOGIN', 'User Login'), ('LOGOUT', 'User Logout'), ('CREATE', 'Record Created'), ('UPDATE', 'Record Updated'), ('DELETE', 'Record Deleted'), ('REPORT', 'Report Generated'), ('APPROVAL', 'Approval Action'), ('MESSAGE', 'Message Sent'), ('MEETING', 'Meeting Action'), ('PROJECT', 'Project Action'), ('TASK', 'Task Action'), ('EXPENSE', 'Expense Action'), ('LEAVE', 'Leave Action'), ('ASSET', 'Asset Action')], max_length=20)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(help_text='Arabic description')),
                ('related_object_type', models.CharField(blank=True, max_length=50)),
                ('related_object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Activities',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='TenantUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(max_length=50)),
                ('permissions', models.JSONField(default=list)),
                ('is_active', models.BooleanField(default=True)),
                ('last_login', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('tenant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tenant_users', to='ems.tenant')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('tenant', 'user')},
            },
        ),
        migrations.CreateModel(
            name='PayrollEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('basic_salary', models.DecimalField(decimal_places=2, max_digits=10)),
                ('allowances', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('overtime_hours', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('overtime_rate', models.DecimalField(decimal_places=2, default=0, max_digits=8)),
                ('bonuses', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('deductions', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('tax_deduction', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('insurance_deduction', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('net_salary', models.DecimalField(decimal_places=2, max_digits=10)),
                ('is_paid', models.BooleanField(default=False)),
                ('payment_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
                ('payroll_period', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.payrollperiod')),
            ],
            options={
                'unique_together': {('employee', 'payroll_period')},
            },
        ),
        migrations.CreateModel(
            name='KPIValue',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('value', models.DecimalField(decimal_places=4, max_digits=15)),
                ('period_start', models.DateTimeField()),
                ('period_end', models.DateTimeField()),
                ('data_quality_score', models.DecimalField(decimal_places=2, default=100.0, max_digits=5)),
                ('confidence_level', models.DecimalField(decimal_places=2, default=100.0, max_digits=5)),
                ('notes', models.TextField(blank=True)),
                ('recorded_at', models.DateTimeField(auto_now_add=True)),
                ('is_estimated', models.BooleanField(default=False)),
                ('source_data', models.JSONField(blank=True, default=dict)),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department')),
                ('employee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
                ('kpi', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='values', to='ems.kpi')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.project')),
                ('recorded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recorded_kpi_values', to='ems.employee')),
            ],
            options={
                'ordering': ['-period_start'],
                'unique_together': {('kpi', 'period_start', 'department', 'project', 'employee')},
            },
        ),
        migrations.CreateModel(
            name='Attendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('check_in', models.TimeField(blank=True, null=True)),
                ('check_out', models.TimeField(blank=True, null=True)),
                ('break_start', models.TimeField(blank=True, null=True)),
                ('break_end', models.TimeField(blank=True, null=True)),
                ('total_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True)),
                ('overtime_hours', models.DecimalField(decimal_places=2, default=0, max_digits=4)),
                ('is_present', models.BooleanField(default=True)),
                ('is_late', models.BooleanField(default=False)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
            ],
            options={
                'ordering': ['-date'],
                'unique_together': {('employee', 'date')},
            },
        ),
    ]
