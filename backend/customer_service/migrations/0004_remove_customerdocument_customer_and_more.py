# Generated by Django 4.2.7 on 2025-06-04 10:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('customer_service', '0003_customer_customernote_customerinteraction_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='customerdocument',
            name='customer',
        ),
        migrations.RemoveField(
            model_name='customerdocument',
            name='uploaded_by',
        ),
        migrations.RemoveField(
            model_name='customerinteraction',
            name='customer',
        ),
        migrations.RemoveField(
            model_name='customerinteraction',
            name='handled_by',
        ),
        migrations.RemoveField(
            model_name='customernote',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='customernote',
            name='customer',
        ),
        migrations.AlterField(
            model_name='chatmessage',
            name='sender',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.DeleteModel(
            name='Customer',
        ),
        migrations.DeleteModel(
            name='CustomerDocument',
        ),
        migrations.DeleteModel(
            name='CustomerInteraction',
        ),
        migrations.DeleteModel(
            name='CustomerNote',
        ),
    ]
