"""
Customer Management Services
Handles automatic customer creation and management
"""

from django.contrib.auth.models import User
from django.utils import timezone
from .models_customer import Customer, CustomerInteraction, CustomerNote
from .models import SupportTicket
import re
import logging

logger = logging.getLogger(__name__)

class CustomerService:
    """Service for managing customers"""
    
    @staticmethod
    def create_or_get_customer(email, first_name=None, last_name=None, phone=None, **kwargs):
        """
        Create a new customer or get existing one by email
        """
        try:
            # Try to get existing customer
            customer = Customer.objects.get(email=email)
            logger.info(f"Found existing customer: {customer.customer_id}")
            return customer, False
            
        except Customer.DoesNotExist:
            # Create new customer
            customer_data = {
                'email': email,
                'first_name': first_name or CustomerService.extract_name_from_email(email),
                'last_name': last_name or '',
                'phone': phone or '',
                'source': kwargs.get('source', 'chat'),
                'preferred_language': kwargs.get('language', 'en'),
                'status': 'prospect',
            }
            
            # Add any additional data
            for key, value in kwargs.items():
                if hasattr(Customer, key) and value:
                    customer_data[key] = value
            
            customer = Customer.objects.create(**customer_data)
            logger.info(f"Created new customer: {customer.customer_id}")
            
            # Create initial interaction record
            CustomerService.log_interaction(
                customer=customer,
                interaction_type='chat',
                subject='Initial Contact',
                description=f'Customer first contacted us via {customer.source}'
            )
            
            return customer, True
            
        except Exception as e:
            logger.error(f"Error creating customer: {e}")
            raise
    
    @staticmethod
    def extract_name_from_email(email):
        """Extract a reasonable first name from email"""
        try:
            username = email.split('@')[0]
            # Remove numbers and special characters
            name = re.sub(r'[^a-zA-Z]', ' ', username)
            # Capitalize first letter of each word
            name = ' '.join(word.capitalize() for word in name.split() if word)
            return name[:50] if name else 'Customer'
        except:
            return 'Customer'
    
    @staticmethod
    def create_customer_from_chat(message_data):
        """Create customer from chat interaction"""
        email = message_data.get('customer_email')
        name = message_data.get('customer_name', 'Anonymous')
        phone = message_data.get('customer_phone')
        language = message_data.get('language', 'en')
        
        if not email:
            # Generate a temporary email for anonymous users
            session_id = message_data.get('session_id', 'unknown')
            email = f"anonymous_{session_id}@temp.local"
            name = name or 'Anonymous User'
        
        # Split name into first and last
        name_parts = name.split(' ', 1) if name else ['Customer']
        first_name = name_parts[0]
        last_name = name_parts[1] if len(name_parts) > 1 else ''
        
        customer, created = CustomerService.create_or_get_customer(
            email=email,
            first_name=first_name,
            last_name=last_name,
            phone=phone,
            source='chat_widget',
            preferred_language=language,
            preferred_contact_method='chat'
        )
        
        return customer, created
    
    @staticmethod
    def create_customer_from_ticket(ticket_data):
        """Create customer from support ticket"""
        email = ticket_data.get('customer_email')
        if not email:
            return None, False
        
        # Extract name from ticket data or email
        customer_name = ticket_data.get('customer_name', '')
        if customer_name:
            name_parts = customer_name.split(' ', 1)
            first_name = name_parts[0]
            last_name = name_parts[1] if len(name_parts) > 1 else ''
        else:
            first_name = CustomerService.extract_name_from_email(email)
            last_name = ''
        
        customer, created = CustomerService.create_or_get_customer(
            email=email,
            first_name=first_name,
            last_name=last_name,
            phone=ticket_data.get('customer_phone', ''),
            source='support_ticket',
            preferred_contact_method='email'
        )
        
        return customer, created
    
    @staticmethod
    def log_interaction(customer, interaction_type, subject, description, **kwargs):
        """Log a customer interaction"""
        try:
            interaction = CustomerInteraction.objects.create(
                customer=customer,
                interaction_type=interaction_type,
                subject=subject,
                description=description,
                handled_by=kwargs.get('handled_by'),
                duration_minutes=kwargs.get('duration_minutes'),
                outcome=kwargs.get('outcome', ''),
                customer_satisfaction=kwargs.get('satisfaction'),
                metadata=kwargs.get('metadata', {})
            )
            
            # Update customer's last contact date
            customer.update_last_contact()
            
            logger.info(f"Logged interaction for customer {customer.customer_id}: {subject}")
            return interaction
            
        except Exception as e:
            logger.error(f"Error logging interaction: {e}")
            return None
    
    @staticmethod
    def update_customer_from_user(user):
        """Update or create customer record from Django User"""
        try:
            # Check if customer already exists
            try:
                customer = Customer.objects.get(user=user)
                return customer, False
            except Customer.DoesNotExist:
                pass
            
            # Try to find by email
            try:
                customer = Customer.objects.get(email=user.email)
                # Link the user to existing customer
                customer.user = user
                customer.save()
                return customer, False
            except Customer.DoesNotExist:
                pass
            
            # Create new customer from user
            customer = Customer.objects.create(
                user=user,
                email=user.email,
                first_name=user.first_name or CustomerService.extract_name_from_email(user.email),
                last_name=user.last_name or '',
                source='user_registration',
                status='active'
            )
            
            logger.info(f"Created customer from user: {customer.customer_id}")
            return customer, True
            
        except Exception as e:
            logger.error(f"Error updating customer from user: {e}")
            return None, False
    
    @staticmethod
    def link_ticket_to_customer(ticket):
        """Link a support ticket to a customer"""
        try:
            if ticket.customer:
                return ticket.customer
            
            # Try to find customer by email
            if ticket.customer_email:
                customer, created = CustomerService.create_customer_from_ticket({
                    'customer_email': ticket.customer_email,
                    'customer_name': getattr(ticket, 'customer_name', ''),
                    'customer_phone': ticket.customer_phone,
                })
                
                if customer:
                    ticket.customer = customer
                    ticket.save()
                    
                    # Update customer statistics
                    customer.total_tickets += 1
                    customer.save(update_fields=['total_tickets'])
                    
                    # Log the interaction
                    CustomerService.log_interaction(
                        customer=customer,
                        interaction_type='ticket',
                        subject=ticket.title,
                        description=ticket.description,
                        metadata={
                            'ticket_id': ticket.ticket_id,
                            'priority': ticket.priority,
                            'status': ticket.status
                        }
                    )
                    
                    return customer
            
            return None
            
        except Exception as e:
            logger.error(f"Error linking ticket to customer: {e}")
            return None
    
    @staticmethod
    def get_customer_stats(customer):
        """Get customer statistics"""
        try:
            stats = {
                'total_tickets': customer.total_tickets,
                'total_interactions': customer.interactions.count(),
                'recent_interactions': customer.interactions.order_by('-created_at')[:5],
                'satisfaction_scores': list(
                    customer.interactions.filter(customer_satisfaction__isnull=False)
                    .values_list('customer_satisfaction', flat=True)
                ),
                'preferred_contact': customer.preferred_contact_method,
                'last_contact': customer.last_contact_date,
                'customer_since': customer.created_at,
                'tags': customer.get_tags_list(),
            }
            
            # Calculate average satisfaction
            if stats['satisfaction_scores']:
                stats['avg_satisfaction'] = sum(stats['satisfaction_scores']) / len(stats['satisfaction_scores'])
            else:
                stats['avg_satisfaction'] = 0
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting customer stats: {e}")
            return {}
    
    @staticmethod
    def search_customers(query):
        """Search customers by various fields"""
        try:
            customers = Customer.objects.filter(
                models.Q(customer_id__icontains=query) |
                models.Q(first_name__icontains=query) |
                models.Q(last_name__icontains=query) |
                models.Q(email__icontains=query) |
                models.Q(company_name__icontains=query) |
                models.Q(phone__icontains=query)
            ).order_by('-created_at')
            
            return customers
            
        except Exception as e:
            logger.error(f"Error searching customers: {e}")
            return Customer.objects.none()

# Signal handlers to automatically create customers
from django.db.models.signals import post_save
from django.dispatch import receiver

@receiver(post_save, sender=User)
def create_customer_for_user(sender, instance, created, **kwargs):
    """Automatically create customer when user is created"""
    if created and instance.email:
        try:
            CustomerService.update_customer_from_user(instance)
        except Exception as e:
            logger.error(f"Error creating customer for user {instance.username}: {e}")

@receiver(post_save, sender=SupportTicket)
def link_ticket_to_customer_signal(sender, instance, created, **kwargs):
    """Automatically link tickets to customers"""
    if created:
        try:
            CustomerService.link_ticket_to_customer(instance)
        except Exception as e:
            logger.error(f"Error linking ticket {instance.ticket_id} to customer: {e}")
