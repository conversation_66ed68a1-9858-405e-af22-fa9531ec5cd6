/**
 * Layout Shift Prevention Utilities
 * Prevents Cumulative Layout Shift (CLS) issues for better Core Web Vitals
 */

import React, { useState, useEffect, useRef, useCallback } from 'react'

// Aspect ratio container to prevent layout shifts for dynamic content
export const AspectRatioContainer: React.FC<{
  ratio: number // width/height ratio (e.g., 16/9 = 1.777)
  children: React.ReactNode
  className?: string
}> = ({ ratio, children, className = '' }) => {
  return (
    <div className={`relative w-full ${className}`}>
      <div 
        className="w-full"
        style={{ paddingBottom: `${(1 / ratio) * 100}%` }}
      />
      <div className="absolute inset-0">
        {children}
      </div>
    </div>
  )
}

// Skeleton loader that matches exact content dimensions
export const DimensionPreservingSkeleton: React.FC<{
  width?: string | number
  height?: string | number
  className?: string
  children?: React.ReactNode
  loading?: boolean
}> = ({ width, height, className = '', children, loading = true }) => {
  if (!loading && children) {
    return <>{children}</>
  }

  return (
    <div
      className={`bg-gray-200 animate-pulse rounded ${className}`}
      style={{
        width: typeof width === 'number' ? `${width}px` : width,
        height: typeof height === 'number' ? `${height}px` : height,
        minHeight: height ? undefined : '1rem'
      }}
      role="status"
      aria-label="Loading content"
    >
      <span className="sr-only">Loading...</span>
    </div>
  )
}

// Image with reserved space to prevent layout shift
export const LayoutStableImage: React.FC<{
  src: string
  alt: string
  width: number
  height: number
  className?: string
  onLoad?: () => void
  onError?: () => void
  priority?: boolean
}> = ({ src, alt, width, height, className = '', onLoad, onError, priority = false }) => {
  const [loaded, setLoaded] = useState(false)
  const [error, setError] = useState(false)

  const handleLoad = useCallback(() => {
    setLoaded(true)
    onLoad?.()
  }, [onLoad])

  const handleError = useCallback(() => {
    setError(true)
    onError?.()
  }, [onError])

  return (
    <div 
      className={`relative ${className}`}
      style={{ width, height }}
    >
      {/* Placeholder that reserves exact space */}
      <div
        className={`absolute inset-0 bg-gray-200 rounded transition-opacity duration-300 ${
          loaded ? 'opacity-0' : 'opacity-100'
        }`}
        style={{ width, height }}
      />
      
      {/* Actual image */}
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={`absolute inset-0 object-cover rounded transition-opacity duration-300 ${
          loaded ? 'opacity-100' : 'opacity-0'
        }`}
        onLoad={handleLoad}
        onError={handleError}
        loading={priority ? 'eager' : 'lazy'}
        decoding="async"
      />
      
      {/* Error state */}
      {error && (
        <div 
          className="absolute inset-0 bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center rounded"
          style={{ width, height }}
        >
          <div className="text-center text-gray-500 text-sm">
            <svg className="mx-auto h-6 w-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            Failed to load
          </div>
        </div>
      )}
    </div>
  )
}

// Table with stable loading state
export const LayoutStableTable: React.FC<{
  columns: Array<{ key: string; label: string; width?: string }>
  data: any[]
  loading?: boolean
  rowHeight?: number
  skeletonRows?: number
  className?: string
  renderCell: (item: any, column: { key: string; label: string }) => React.ReactNode
}> = ({ 
  columns, 
  data, 
  loading = false, 
  rowHeight = 60, 
  skeletonRows = 5, 
  className = '',
  renderCell 
}) => {
  return (
    <div className={`overflow-x-auto ${className}`}>
      <table className="w-full">
        <thead>
          <tr className="border-b border-gray-200">
            {columns.map((column) => (
              <th
                key={column.key}
                className="px-4 py-3 text-left text-sm font-medium text-gray-700"
                style={{ width: column.width }}
              >
                {column.label}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {loading ? (
            // Skeleton rows with exact dimensions
            Array.from({ length: skeletonRows }).map((_, index) => (
              <tr 
                key={`skeleton-${index}`} 
                className="border-b border-gray-100"
                style={{ height: rowHeight }}
              >
                {columns.map((column) => (
                  <td key={column.key} className="px-4 py-3">
                    <div className="h-4 bg-gray-200 rounded animate-pulse" style={{ width: '80%' }} />
                  </td>
                ))}
              </tr>
            ))
          ) : (
            data.map((item, index) => (
              <tr 
                key={index} 
                className="border-b border-gray-100 hover:bg-gray-50"
                style={{ minHeight: rowHeight }}
              >
                {columns.map((column) => (
                  <td key={column.key} className="px-4 py-3">
                    {renderCell(item, column)}
                  </td>
                ))}
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  )
}

// Modal with stable dimensions
export const LayoutStableModal: React.FC<{
  isOpen: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
  width?: string
  height?: string
  className?: string
}> = ({ isOpen, onClose, title, children, width = '32rem', height = 'auto', className = '' }) => {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  return (
    <>
      {isOpen && (
        <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm">
          <div className="flex items-center justify-center min-h-screen p-4">
            <div
              className={`bg-white rounded-lg shadow-xl transform transition-all duration-200 ${className}`}
              style={{ 
                width, 
                height,
                maxWidth: '90vw',
                maxHeight: '90vh'
              }}
            >
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">{title}</h3>
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded p-1"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="p-6 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 120px)' }}>
                {children}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

// Hook to measure and reserve space for dynamic content
export const useLayoutStabilizer = () => {
  const ref = useRef<HTMLDivElement>(null)
  const [dimensions, setDimensions] = useState<{ width: number; height: number } | null>(null)

  const measureAndReserve = useCallback(() => {
    if (ref.current) {
      const rect = ref.current.getBoundingClientRect()
      setDimensions({ width: rect.width, height: rect.height })
    }
  }, [])

  useEffect(() => {
    const element = ref.current
    if (!element) return

    const resizeObserver = new ResizeObserver(() => {
      measureAndReserve()
    })

    resizeObserver.observe(element)
    measureAndReserve()

    return () => {
      resizeObserver.disconnect()
    }
  }, [measureAndReserve])

  return { ref, dimensions, measureAndReserve }
}

// Content with reserved space during loading
export const ReservedSpaceContent: React.FC<{
  loading: boolean
  children: React.ReactNode
  fallbackHeight?: number
  className?: string
}> = ({ loading, children, fallbackHeight = 200, className = '' }) => {
  const { ref, dimensions } = useLayoutStabilizer()

  if (loading) {
    return (
      <div
        className={`bg-gray-100 animate-pulse rounded ${className}`}
        style={{
          height: dimensions?.height || fallbackHeight,
          width: dimensions?.width || '100%'
        }}
      />
    )
  }

  return (
    <div ref={ref} className={className}>
      {children}
    </div>
  )
}


