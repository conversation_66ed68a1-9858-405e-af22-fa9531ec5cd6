"""
Advanced Customer Service Models
Automated routing, escalation, AI integration, and workflow automation
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
import json

class TicketRoutingRule(models.Model):
    """Automated ticket routing rules"""
    CONDITION_CHOICES = [
        ('keyword', 'Keyword Match'),
        ('category', 'Category'),
        ('priority', 'Priority Level'),
        ('customer_tier', 'Customer Tier'),
        ('time_of_day', 'Time of Day'),
        ('agent_workload', 'Agent Workload'),
        ('language', 'Language'),
        ('channel', 'Source Channel'),
    ]
    
    ACTION_CHOICES = [
        ('assign_agent', 'Assign to Agent'),
        ('assign_team', 'Assign to Team'),
        ('set_priority', 'Set Priority'),
        ('add_tag', 'Add Tag'),
        ('send_notification', 'Send Notification'),
        ('escalate', 'Escalate'),
    ]
    
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    priority = models.IntegerField(default=1, help_text="Higher number = higher priority")
    
    # Conditions
    condition_type = models.CharField(max_length=50, choices=CONDITION_CHOICES)
    condition_value = models.TextField(help_text="JSON formatted condition parameters")
    
    # Actions
    action_type = models.CharField(max_length=50, choices=ACTION_CHOICES)
    action_value = models.TextField(help_text="JSON formatted action parameters")
    
    # Timing
    apply_during_hours = models.CharField(max_length=100, blank=True, help_text="e.g., 09:00-17:00")
    apply_on_weekdays = models.BooleanField(default=True)
    apply_on_weekends = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-priority', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.condition_type} → {self.action_type})"
    
    def get_condition_params(self):
        try:
            return json.loads(self.condition_value)
        except:
            return {}
    
    def get_action_params(self):
        try:
            return json.loads(self.action_value)
        except:
            return {}

class EscalationRule(models.Model):
    """Automatic ticket escalation rules"""
    TRIGGER_CHOICES = [
        ('time_based', 'Time Based'),
        ('sla_breach', 'SLA Breach'),
        ('customer_response', 'Customer Response'),
        ('priority_change', 'Priority Change'),
        ('agent_unavailable', 'Agent Unavailable'),
        ('customer_satisfaction', 'Low Satisfaction'),
    ]
    
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    
    # Trigger conditions
    trigger_type = models.CharField(max_length=50, choices=TRIGGER_CHOICES)
    trigger_after_hours = models.IntegerField(null=True, blank=True, help_text="Hours before escalation")
    trigger_conditions = models.TextField(blank=True, help_text="JSON formatted conditions")
    
    # Escalation actions
    escalate_to_agent = models.ForeignKey('SupportAgent', on_delete=models.SET_NULL, null=True, blank=True)
    escalate_to_manager = models.BooleanField(default=False)
    send_email_notification = models.BooleanField(default=True)
    send_sms_notification = models.BooleanField(default=False)
    increase_priority = models.BooleanField(default=True)
    
    # Notification settings
    notification_emails = models.TextField(blank=True, help_text="Comma-separated email addresses")
    notification_template = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} ({self.trigger_type})"

class AIAssistant(models.Model):
    """AI-powered chatbot and assistant configuration"""
    ASSISTANT_TYPES = [
        ('chatbot', 'Customer Chatbot'),
        ('agent_helper', 'Agent Assistant'),
        ('sentiment_analyzer', 'Sentiment Analyzer'),
        ('auto_responder', 'Auto Responder'),
        ('knowledge_suggester', 'Knowledge Suggester'),
    ]
    
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    assistant_type = models.CharField(max_length=50, choices=ASSISTANT_TYPES)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    
    # AI Configuration
    ai_provider = models.CharField(max_length=100, default='openai', help_text="AI service provider")
    model_name = models.CharField(max_length=100, default='gpt-3.5-turbo')
    api_key_encrypted = models.TextField(blank=True, help_text="Encrypted API key")
    
    # Behavior settings
    confidence_threshold = models.FloatField(default=0.8, validators=[MinValueValidator(0.0), MaxValueValidator(1.0)])
    max_response_length = models.IntegerField(default=500)
    response_tone = models.CharField(max_length=50, default='professional', 
                                   choices=[('professional', 'Professional'), ('friendly', 'Friendly'), ('casual', 'Casual')])
    
    # Training data
    training_data = models.TextField(blank=True, help_text="Custom training prompts and examples")
    knowledge_base_integration = models.BooleanField(default=True)
    
    # Usage statistics
    total_interactions = models.IntegerField(default=0)
    successful_resolutions = models.IntegerField(default=0)
    average_satisfaction = models.FloatField(default=0.0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} ({self.assistant_type})"
    
    @property
    def success_rate(self):
        if self.total_interactions > 0:
            return (self.successful_resolutions / self.total_interactions) * 100
        return 0

class AIInteraction(models.Model):
    """Log of AI assistant interactions"""
    assistant = models.ForeignKey(AIAssistant, on_delete=models.CASCADE, related_name='interactions')
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    session_id = models.CharField(max_length=100, blank=True)
    
    # Interaction data
    user_input = models.TextField()
    ai_response = models.TextField()
    confidence_score = models.FloatField(null=True, blank=True)
    response_time_ms = models.IntegerField(null=True, blank=True)
    
    # Context
    related_ticket = models.ForeignKey('SupportTicket', on_delete=models.SET_NULL, null=True, blank=True)
    context_data = models.TextField(blank=True, help_text="JSON formatted context")
    
    # Feedback
    was_helpful = models.BooleanField(null=True, blank=True)
    user_feedback = models.TextField(blank=True)
    escalated_to_human = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.assistant.name} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"

class WorkflowAutomation(models.Model):
    """Automated workflow definitions"""
    TRIGGER_EVENTS = [
        ('ticket_created', 'Ticket Created'),
        ('ticket_updated', 'Ticket Updated'),
        ('ticket_resolved', 'Ticket Resolved'),
        ('ticket_closed', 'Ticket Closed'),
        ('comment_added', 'Comment Added'),
        ('agent_assigned', 'Agent Assigned'),
        ('sla_breach', 'SLA Breach'),
        ('customer_feedback', 'Customer Feedback'),
        ('escalation_triggered', 'Escalation Triggered'),
    ]
    
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    
    # Trigger configuration
    trigger_event = models.CharField(max_length=50, choices=TRIGGER_EVENTS)
    trigger_conditions = models.TextField(blank=True, help_text="JSON formatted conditions")
    
    # Workflow steps
    workflow_steps = models.TextField(help_text="JSON formatted workflow steps")
    
    # Execution settings
    delay_minutes = models.IntegerField(default=0, help_text="Delay before execution")
    max_retries = models.IntegerField(default=3)
    retry_delay_minutes = models.IntegerField(default=5)
    
    # Statistics
    total_executions = models.IntegerField(default=0)
    successful_executions = models.IntegerField(default=0)
    failed_executions = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} ({self.trigger_event})"
    
    @property
    def success_rate(self):
        if self.total_executions > 0:
            return (self.successful_executions / self.total_executions) * 100
        return 0

class WorkflowExecution(models.Model):
    """Log of workflow executions"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    
    workflow = models.ForeignKey(WorkflowAutomation, on_delete=models.CASCADE, related_name='executions')
    related_ticket = models.ForeignKey('SupportTicket', on_delete=models.CASCADE, null=True, blank=True)
    
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    execution_data = models.TextField(blank=True, help_text="JSON formatted execution context")
    
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True)
    
    # Results
    steps_completed = models.IntegerField(default=0)
    total_steps = models.IntegerField(default=0)
    execution_log = models.TextField(blank=True)
    
    def __str__(self):
        return f"{self.workflow.name} - {self.status} ({self.started_at.strftime('%Y-%m-%d %H:%M')})"

class IntegrationConnector(models.Model):
    """External system integrations"""
    INTEGRATION_TYPES = [
        ('email', 'Email System'),
        ('crm', 'CRM System'),
        ('slack', 'Slack'),
        ('teams', 'Microsoft Teams'),
        ('jira', 'Jira'),
        ('salesforce', 'Salesforce'),
        ('hubspot', 'HubSpot'),
        ('webhook', 'Custom Webhook'),
        ('api', 'REST API'),
        ('sms', 'SMS Gateway'),
    ]
    
    name = models.CharField(max_length=200)
    integration_type = models.CharField(max_length=50, choices=INTEGRATION_TYPES)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    
    # Connection settings
    endpoint_url = models.URLField(blank=True)
    api_key_encrypted = models.TextField(blank=True)
    username = models.CharField(max_length=200, blank=True)
    password_encrypted = models.TextField(blank=True)
    
    # Configuration
    configuration = models.TextField(blank=True, help_text="JSON formatted configuration")
    sync_frequency_minutes = models.IntegerField(default=60)
    
    # Status
    last_sync_at = models.DateTimeField(null=True, blank=True)
    last_sync_status = models.CharField(max_length=50, blank=True)
    total_syncs = models.IntegerField(default=0)
    failed_syncs = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} ({self.integration_type})"
    
    @property
    def sync_success_rate(self):
        if self.total_syncs > 0:
            successful_syncs = self.total_syncs - self.failed_syncs
            return (successful_syncs / self.total_syncs) * 100
        return 0

class CustomerTier(models.Model):
    """Customer tier/segment management for prioritization"""
    TIER_LEVELS = [
        ('platinum', 'Platinum'),
        ('gold', 'Gold'),
        ('silver', 'Silver'),
        ('bronze', 'Bronze'),
        ('standard', 'Standard'),
    ]
    
    name = models.CharField(max_length=100)
    name_ar = models.CharField(max_length=100, blank=True)
    tier_level = models.CharField(max_length=20, choices=TIER_LEVELS)
    description = models.TextField(blank=True)
    
    # SLA settings
    response_time_hours = models.IntegerField(default=24)
    resolution_time_hours = models.IntegerField(default=72)
    escalation_time_hours = models.IntegerField(default=48)
    
    # Priority settings
    default_priority = models.CharField(max_length=20, default='medium')
    auto_escalate = models.BooleanField(default=False)
    dedicated_agent = models.BooleanField(default=False)
    
    # Benefits
    benefits = models.TextField(blank=True, help_text="JSON formatted benefits list")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} ({self.tier_level})"

class CustomerProfile(models.Model):
    """Extended customer profile for better service"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='customer_profile')
    customer_tier = models.ForeignKey(CustomerTier, on_delete=models.SET_NULL, null=True, blank=True)
    
    # Contact preferences
    preferred_contact_method = models.CharField(max_length=50, default='email',
                                              choices=[('email', 'Email'), ('phone', 'Phone'), ('chat', 'Live Chat')])
    preferred_language = models.CharField(max_length=10, default='en', choices=[('en', 'English'), ('ar', 'Arabic')])
    timezone = models.CharField(max_length=50, default='UTC')
    
    # Service history
    total_tickets = models.IntegerField(default=0)
    resolved_tickets = models.IntegerField(default=0)
    average_satisfaction = models.FloatField(default=0.0)
    last_contact_date = models.DateTimeField(null=True, blank=True)
    
    # Preferences
    receive_notifications = models.BooleanField(default=True)
    receive_surveys = models.BooleanField(default=True)
    allow_ai_assistance = models.BooleanField(default=True)
    
    # Notes
    internal_notes = models.TextField(blank=True, help_text="Internal notes for agents")
    tags = models.CharField(max_length=500, blank=True, help_text="Comma-separated tags")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username} Profile"
