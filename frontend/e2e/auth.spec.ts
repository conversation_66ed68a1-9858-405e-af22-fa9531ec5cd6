import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
  });

  test('should display login page for unauthenticated users', async ({ page }) => {
    // Should redirect to login or show login form
    await expect(page).toHaveURL(/.*login.*/);
    
    // Check for login form elements
    await expect(page.locator('input[type="text"], input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")')).toBeVisible();
  });

  test('should show validation errors for empty login form', async ({ page }) => {
    // Try to submit empty form
    await page.click('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")');
    
    // Should show validation errors
    await expect(page.locator('text=/required|مطلوب|field|حقل/i')).toBeVisible();
  });

  test('should show error for invalid credentials', async ({ page }) => {
    // Fill in invalid credentials
    await page.fill('input[type="text"], input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'wrongpassword');
    
    // Submit form
    await page.click('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")');
    
    // Should show error message
    await expect(page.locator('text=/invalid|خطأ|error|غير صحيح/i')).toBeVisible();
  });

  test('should successfully login with valid credentials', async ({ page }) => {
    // Fill in valid credentials (using the superuser we created)
    await page.fill('input[type="text"], input[type="email"]', 'admin');
    await page.fill('input[type="password"]', 'admin123');
    
    // Submit form
    await page.click('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")');
    
    // Should redirect to dashboard or main app
    await expect(page).toHaveURL(/.*(?:dashboard|home|main).*/);
    
    // Should show authenticated user interface
    await expect(page.locator('text=/dashboard|لوحة التحكم|welcome|مرحبا/i')).toBeVisible();
  });

  test('should maintain session after page refresh', async ({ page }) => {
    // Login first
    await page.fill('input[type="text"], input[type="email"]', 'admin');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")');
    
    // Wait for successful login
    await expect(page).toHaveURL(/.*(?:dashboard|home|main).*/);
    
    // Refresh the page
    await page.reload();
    
    // Should still be authenticated
    await expect(page).toHaveURL(/.*(?:dashboard|home|main).*/);
    await expect(page.locator('text=/dashboard|لوحة التحكم|welcome|مرحبا/i')).toBeVisible();
  });

  test('should logout successfully', async ({ page }) => {
    // Login first
    await page.fill('input[type="text"], input[type="email"]', 'admin');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")');
    
    // Wait for successful login
    await expect(page).toHaveURL(/.*(?:dashboard|home|main).*/);
    
    // Find and click logout button
    await page.click('button:has-text("Logout"), button:has-text("تسجيل الخروج"), [data-testid="logout-button"]');
    
    // Should redirect to login page
    await expect(page).toHaveURL(/.*login.*/);
    await expect(page.locator('input[type="text"], input[type="email"]')).toBeVisible();
  });
});
