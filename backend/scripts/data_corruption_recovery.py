#!/usr/bin/env python3
"""
CRITICAL FIX: Data Corruption Detection and Recovery
Advanced data integrity validation and automated recovery procedures
"""

import os
import sys
import hashlib
import json
from pathlib import Path
from datetime import datetime
from collections import defaultdict

# Add Django to path
sys.path.append(str(Path(__file__).parent.parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

import django
django.setup()

from django.db import connections, transaction
from django.apps import apps
from django.core.serializers import serialize, deserialize

class DataCorruptionDetector:
    def __init__(self):
        self.corruption_report = {
            "scan_timestamp": datetime.now().isoformat(),
            "databases_scanned": [],
            "corruption_issues": [],
            "integrity_violations": [],
            "recovery_actions": [],
            "summary": {}
        }
        
    def run_comprehensive_scan(self):
        """Run comprehensive data corruption scan"""
        print("🔍 Starting Comprehensive Data Corruption Scan...")
        print("=" * 60)
        
        try:
            # Scan all configured databases
            for db_alias in connections:
                self.scan_database(db_alias)
            
            # Cross-database integrity checks
            self.cross_database_integrity_check()
            
            # Generate corruption report
            self.generate_corruption_report()
            
            # Suggest recovery actions
            self.suggest_recovery_actions()
            
            return len(self.corruption_report["corruption_issues"]) == 0
            
        except Exception as e:
            print(f"💥 Corruption scan failed: {e}")
            return False
    
    def scan_database(self, db_alias):
        """Scan individual database for corruption"""
        print(f"\n📊 Scanning database: {db_alias}")
        
        self.corruption_report["databases_scanned"].append(db_alias)
        
        try:
            # Test database connectivity
            self.test_database_connectivity(db_alias)
            
            # Check table integrity
            self.check_table_integrity(db_alias)
            
            # Validate foreign key constraints
            self.validate_foreign_key_constraints(db_alias)
            
            # Check for duplicate records
            self.check_duplicate_records(db_alias)
            
            # Validate data consistency
            self.validate_data_consistency(db_alias)
            
            # Check for orphaned records
            self.check_orphaned_records(db_alias)
            
        except Exception as e:
            self.add_corruption_issue(
                "database_scan_error",
                f"Failed to scan database {db_alias}: {e}",
                "critical",
                {"database": db_alias}
            )
    
    def test_database_connectivity(self, db_alias):
        """Test database connectivity and basic operations"""
        print(f"  🔌 Testing connectivity for {db_alias}...")
        
        try:
            conn = connections[db_alias]
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                
                if result[0] != 1:
                    self.add_corruption_issue(
                        "connectivity_test_failed",
                        f"Basic connectivity test failed for {db_alias}",
                        "critical",
                        {"database": db_alias}
                    )
                else:
                    print(f"    ✅ Connectivity test passed")
                    
        except Exception as e:
            self.add_corruption_issue(
                "connectivity_error",
                f"Cannot connect to database {db_alias}: {e}",
                "critical",
                {"database": db_alias}
            )
    
    def check_table_integrity(self, db_alias):
        """Check table-level integrity"""
        print(f"  📋 Checking table integrity for {db_alias}...")
        
        models = apps.get_models()
        
        for model in models:
            try:
                table_name = model._meta.db_table
                
                # Check if table exists and is accessible
                record_count = model.objects.using(db_alias).count()
                
                # Check for negative IDs (should not exist)
                negative_ids = model.objects.using(db_alias).filter(id__lt=0).count()
                if negative_ids > 0:
                    self.add_corruption_issue(
                        "negative_primary_keys",
                        f"Found {negative_ids} records with negative IDs in {table_name}",
                        "high",
                        {"database": db_alias, "table": table_name, "count": negative_ids}
                    )
                
                # Check for extremely large IDs (potential overflow)
                max_id = model.objects.using(db_alias).aggregate(max_id=models.Max('id'))['max_id']
                if max_id and max_id > 2147483647:  # 32-bit integer limit
                    self.add_corruption_issue(
                        "id_overflow_risk",
                        f"Primary key approaching overflow in {table_name}: {max_id}",
                        "medium",
                        {"database": db_alias, "table": table_name, "max_id": max_id}
                    )
                
                print(f"    ✅ {table_name}: {record_count:,} records")
                
            except Exception as e:
                self.add_corruption_issue(
                    "table_integrity_error",
                    f"Cannot access table {table_name}: {e}",
                    "high",
                    {"database": db_alias, "table": table_name}
                )
    
    def validate_foreign_key_constraints(self, db_alias):
        """Validate foreign key constraints"""
        print(f"  🔗 Validating foreign key constraints for {db_alias}...")
        
        models = apps.get_models()
        
        for model in models:
            try:
                for field in model._meta.fields:
                    if hasattr(field, 'related_model') and field.related_model:
                        # Check for orphaned foreign key references
                        orphaned_query = model.objects.using(db_alias).exclude(
                            **{f"{field.name}__isnull": True}
                        ).exclude(
                            **{f"{field.name}__in": field.related_model.objects.using(db_alias).values('id')}
                        )
                        
                        orphaned_count = orphaned_query.count()
                        
                        if orphaned_count > 0:
                            self.add_corruption_issue(
                                "orphaned_foreign_keys",
                                f"Found {orphaned_count} orphaned foreign key references in {model.__name__}.{field.name}",
                                "high",
                                {
                                    "database": db_alias,
                                    "model": model.__name__,
                                    "field": field.name,
                                    "count": orphaned_count
                                }
                            )
                        else:
                            print(f"    ✅ {model.__name__}.{field.name}: No orphaned references")
                            
            except Exception as e:
                print(f"    ⚠️ Could not validate FK for {model.__name__}: {e}")
    
    def check_duplicate_records(self, db_alias):
        """Check for duplicate records in unique fields"""
        print(f"  🔍 Checking for duplicate records in {db_alias}...")
        
        models = apps.get_models()
        
        for model in models:
            try:
                # Check unique fields
                for field in model._meta.fields:
                    if field.unique and field.name != 'id':
                        # Find duplicates
                        duplicates = model.objects.using(db_alias).values(field.name).annotate(
                            count=models.Count(field.name)
                        ).filter(count__gt=1)
                        
                        duplicate_count = duplicates.count()
                        
                        if duplicate_count > 0:
                            self.add_corruption_issue(
                                "duplicate_unique_values",
                                f"Found {duplicate_count} duplicate values in unique field {model.__name__}.{field.name}",
                                "high",
                                {
                                    "database": db_alias,
                                    "model": model.__name__,
                                    "field": field.name,
                                    "duplicates": list(duplicates)
                                }
                            )
                        else:
                            print(f"    ✅ {model.__name__}.{field.name}: No duplicates")
                
                # Check unique_together constraints
                if hasattr(model._meta, 'unique_together') and model._meta.unique_together:
                    for unique_fields in model._meta.unique_together:
                        duplicates = model.objects.using(db_alias).values(*unique_fields).annotate(
                            count=models.Count('id')
                        ).filter(count__gt=1)
                        
                        duplicate_count = duplicates.count()
                        
                        if duplicate_count > 0:
                            self.add_corruption_issue(
                                "duplicate_unique_together",
                                f"Found {duplicate_count} duplicate combinations in {model.__name__} unique_together: {unique_fields}",
                                "high",
                                {
                                    "database": db_alias,
                                    "model": model.__name__,
                                    "fields": unique_fields,
                                    "duplicates": list(duplicates)
                                }
                            )
                            
            except Exception as e:
                print(f"    ⚠️ Could not check duplicates for {model.__name__}: {e}")
    
    def validate_data_consistency(self, db_alias):
        """Validate data consistency and business rules"""
        print(f"  📊 Validating data consistency for {db_alias}...")
        
        # Custom business rule validations
        self.validate_employee_data_consistency(db_alias)
        self.validate_financial_data_consistency(db_alias)
        self.validate_date_consistency(db_alias)
    
    def validate_employee_data_consistency(self, db_alias):
        """Validate employee-specific data consistency"""
        
        try:
            from ems.models import Employee, Department
            
            # Check for employees without departments (if required)
            employees_without_dept = Employee.objects.using(db_alias).filter(department__isnull=True).count()
            if employees_without_dept > 0:
                self.add_corruption_issue(
                    "employees_without_department",
                    f"Found {employees_without_dept} employees without departments",
                    "medium",
                    {"database": db_alias, "count": employees_without_dept}
                )
            
            # Check for future hire dates
            from django.utils import timezone
            future_hires = Employee.objects.using(db_alias).filter(hire_date__gt=timezone.now().date()).count()
            if future_hires > 0:
                self.add_corruption_issue(
                    "future_hire_dates",
                    f"Found {future_hires} employees with future hire dates",
                    "medium",
                    {"database": db_alias, "count": future_hires}
                )
            
            print(f"    ✅ Employee data consistency validated")
            
        except Exception as e:
            print(f"    ⚠️ Could not validate employee data: {e}")
    
    def validate_financial_data_consistency(self, db_alias):
        """Validate financial data consistency"""
        
        try:
            from ems.models import Expense, Budget
            
            # Check for negative amounts
            negative_expenses = Expense.objects.using(db_alias).filter(amount__lt=0).count()
            if negative_expenses > 0:
                self.add_corruption_issue(
                    "negative_expense_amounts",
                    f"Found {negative_expenses} expenses with negative amounts",
                    "high",
                    {"database": db_alias, "count": negative_expenses}
                )
            
            # Check for extremely large amounts (potential data entry errors)
            large_expenses = Expense.objects.using(db_alias).filter(amount__gt=1000000).count()
            if large_expenses > 0:
                self.add_corruption_issue(
                    "unusually_large_expenses",
                    f"Found {large_expenses} expenses over $1M (potential data entry errors)",
                    "medium",
                    {"database": db_alias, "count": large_expenses}
                )
            
            print(f"    ✅ Financial data consistency validated")
            
        except Exception as e:
            print(f"    ⚠️ Could not validate financial data: {e}")
    
    def validate_date_consistency(self, db_alias):
        """Validate date field consistency"""
        
        models = apps.get_models()
        
        for model in models:
            try:
                date_fields = [f for f in model._meta.fields if f.__class__.__name__ in ['DateField', 'DateTimeField']]
                
                for field in date_fields:
                    # Check for dates in the far future (likely errors)
                    from django.utils import timezone
                    future_cutoff = timezone.now().date().replace(year=timezone.now().year + 10)
                    
                    future_dates = model.objects.using(db_alias).filter(
                        **{f"{field.name}__gt": future_cutoff}
                    ).count()
                    
                    if future_dates > 0:
                        self.add_corruption_issue(
                            "far_future_dates",
                            f"Found {future_dates} records with dates far in the future in {model.__name__}.{field.name}",
                            "medium",
                            {
                                "database": db_alias,
                                "model": model.__name__,
                                "field": field.name,
                                "count": future_dates
                            }
                        )
                
            except Exception as e:
                print(f"    ⚠️ Could not validate dates for {model.__name__}: {e}")
    
    def check_orphaned_records(self, db_alias):
        """Check for orphaned records"""
        print(f"  🔍 Checking for orphaned records in {db_alias}...")
        
        # This is a simplified check - in practice, you'd want more sophisticated orphan detection
        try:
            from ems.models import Employee, Department, LeaveRequest
            
            # Check for leave requests without employees
            orphaned_leaves = LeaveRequest.objects.using(db_alias).filter(employee__isnull=True).count()
            if orphaned_leaves > 0:
                self.add_corruption_issue(
                    "orphaned_leave_requests",
                    f"Found {orphaned_leaves} leave requests without employees",
                    "high",
                    {"database": db_alias, "count": orphaned_leaves}
                )
            
            print(f"    ✅ Orphaned records check completed")
            
        except Exception as e:
            print(f"    ⚠️ Could not check orphaned records: {e}")
    
    def cross_database_integrity_check(self):
        """Check integrity across multiple databases"""
        print(f"\n🔄 Cross-database integrity check...")
        
        if len(self.corruption_report["databases_scanned"]) < 2:
            print(f"  ℹ️ Skipping cross-database check (only one database)")
            return
        
        # Compare record counts between databases
        self.compare_record_counts_across_databases()
        
        # Check for data synchronization issues
        self.check_data_synchronization()
    
    def compare_record_counts_across_databases(self):
        """Compare record counts across databases"""
        
        models = apps.get_models()
        
        for model in models:
            try:
                counts = {}
                for db_alias in self.corruption_report["databases_scanned"]:
                    counts[db_alias] = model.objects.using(db_alias).count()
                
                # Check if all counts are the same
                count_values = list(counts.values())
                if len(set(count_values)) > 1:
                    self.add_corruption_issue(
                        "cross_database_count_mismatch",
                        f"Record count mismatch for {model.__name__}: {counts}",
                        "high",
                        {"model": model.__name__, "counts": counts}
                    )
                else:
                    print(f"    ✅ {model.__name__}: Consistent counts across databases")
                    
            except Exception as e:
                print(f"    ⚠️ Could not compare counts for {model.__name__}: {e}")
    
    def check_data_synchronization(self):
        """Check for data synchronization issues"""
        
        # This would involve more sophisticated checks like comparing checksums
        # of critical data between databases
        print(f"    ℹ️ Advanced synchronization checks would be implemented here")
    
    def add_corruption_issue(self, issue_type, description, severity, metadata):
        """Add a corruption issue to the report"""
        
        issue = {
            "type": issue_type,
            "description": description,
            "severity": severity,
            "metadata": metadata,
            "timestamp": datetime.now().isoformat()
        }
        
        self.corruption_report["corruption_issues"].append(issue)
        
        # Print issue immediately
        severity_icon = {"critical": "🔴", "high": "🟠", "medium": "🟡", "low": "🟢"}
        print(f"    {severity_icon.get(severity, '⚪')} {severity.upper()}: {description}")
    
    def generate_corruption_report(self):
        """Generate comprehensive corruption report"""
        
        # Summarize findings
        issues_by_severity = defaultdict(int)
        for issue in self.corruption_report["corruption_issues"]:
            issues_by_severity[issue["severity"]] += 1
        
        self.corruption_report["summary"] = {
            "total_issues": len(self.corruption_report["corruption_issues"]),
            "issues_by_severity": dict(issues_by_severity),
            "databases_scanned": len(self.corruption_report["databases_scanned"]),
            "scan_duration": "calculated_in_main_function"
        }
        
        # Save report to file
        report_file = f"data_corruption_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.corruption_report, f, indent=2)
        
        print(f"\n📄 Corruption report saved to: {report_file}")
    
    def suggest_recovery_actions(self):
        """Suggest recovery actions based on found issues"""
        
        print(f"\n💡 Recovery Action Suggestions:")
        
        recovery_actions = []
        
        for issue in self.corruption_report["corruption_issues"]:
            action = self.get_recovery_action_for_issue(issue)
            if action:
                recovery_actions.append(action)
                print(f"  • {action}")
        
        self.corruption_report["recovery_actions"] = recovery_actions
        
        if not recovery_actions:
            print(f"  ✅ No recovery actions needed")
    
    def get_recovery_action_for_issue(self, issue):
        """Get specific recovery action for an issue type"""
        
        action_map = {
            "orphaned_foreign_keys": "Run foreign key cleanup script to remove orphaned references",
            "duplicate_unique_values": "Identify and merge or remove duplicate records",
            "negative_expense_amounts": "Review and correct negative financial amounts",
            "future_hire_dates": "Correct employee hire dates that are in the future",
            "cross_database_count_mismatch": "Investigate and resolve data synchronization issues",
            "employees_without_department": "Assign departments to employees or mark as inactive"
        }
        
        return action_map.get(issue["type"])
    
    def print_scan_summary(self):
        """Print scan summary"""
        
        print("\n" + "=" * 60)
        print("📋 DATA CORRUPTION SCAN SUMMARY")
        print("=" * 60)
        
        summary = self.corruption_report["summary"]
        
        print(f"\n📊 Scan Results:")
        print(f"  • Databases scanned: {summary['databases_scanned']}")
        print(f"  • Total issues found: {summary['total_issues']}")
        
        if summary["issues_by_severity"]:
            print(f"\n🎯 Issues by Severity:")
            for severity, count in summary["issues_by_severity"].items():
                severity_icon = {"critical": "🔴", "high": "🟠", "medium": "🟡", "low": "🟢"}
                print(f"  • {severity_icon.get(severity, '⚪')} {severity.title()}: {count}")
        
        if summary["total_issues"] == 0:
            print(f"\n🎉 No data corruption issues found!")
        else:
            print(f"\n⚠️ {summary['total_issues']} issues require attention")

if __name__ == '__main__':
    detector = DataCorruptionDetector()
    start_time = datetime.now()
    
    success = detector.run_comprehensive_scan()
    
    # Update scan duration
    scan_duration = (datetime.now() - start_time).total_seconds()
    detector.corruption_report["summary"]["scan_duration"] = f"{scan_duration:.2f} seconds"
    
    detector.print_scan_summary()
    
    sys.exit(0 if success else 1)
