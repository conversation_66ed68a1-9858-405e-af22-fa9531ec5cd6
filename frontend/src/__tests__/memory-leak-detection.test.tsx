/**
 * CRITICAL MEMORY LEAK DETECTION TESTS
 * Automated tests to verify useEffect cleanup and resource management improvements
 */

import { render, screen, waitFor, act } from '@testing-library/react'
import { Provider } from 'react-redux'
import { store } from '../store'
import { MemoryLeakPrevention, SafeDOM } from '../utils/performanceBugFixes'
import { comprehensiveBugMonitor } from '../utils/comprehensiveBugMonitor'

// Mock performance.memory for testing
const mockMemory = {
  usedJSHeapSize: 50000000, // 50MB baseline
  totalJSHeapSize: 100000000,
  jsHeapSizeLimit: **********
}

Object.defineProperty(performance, 'memory', {
  value: mockMemory,
  writable: true
})

// Memory leak detector utility
class MemoryLeakDetector {
  private initialMemory: number
  private initialResources: any
  private memorySnapshots: number[] = []
  private resourceSnapshots: any[] = []

  constructor() {
    this.initialMemory = this.getCurrentMemory()
    this.initialResources = this.getCurrentResources()
  }

  takeSnapshot(): void {
    this.memorySnapshots.push(this.getCurrentMemory())
    this.resourceSnapshots.push(this.getCurrentResources())
  }

  getCurrentMemory(): number {
    return performance.memory?.usedJSHeapSize || mockMemory.usedJSHeapSize
  }

  getCurrentResources(): any {
    try {
      return MemoryLeakPrevention.getResourceCounts()
    } catch {
      return { intervals: 0, timeouts: 0, observers: 0, eventListeners: 0 }
    }
  }

  detectMemoryLeak(): { hasLeak: boolean; increase: number; percentage: number } {
    const currentMemory = this.getCurrentMemory()
    const increase = currentMemory - this.initialMemory
    const percentage = (increase / this.initialMemory) * 100

    return {
      hasLeak: increase > 50 * 1024 * 1024, // 50MB threshold
      increase,
      percentage
    }
  }

  detectResourceLeak(): { hasLeak: boolean; leakedResources: any } {
    const current = this.getCurrentResources()
    const initial = this.initialResources

    const leakedResources = {
      intervals: current.intervals - initial.intervals,
      timeouts: current.timeouts - initial.timeouts,
      observers: current.observers - initial.observers,
      eventListeners: current.eventListeners - initial.eventListeners
    }

    const totalLeaked = Object.values(leakedResources).reduce((sum: number, count: number) => sum + count, 0)

    return {
      hasLeak: totalLeaked > 10, // More than 10 leaked resources
      leakedResources
    }
  }

  generateReport(): string {
    const memoryLeak = this.detectMemoryLeak()
    const resourceLeak = this.detectResourceLeak()

    return `
🧠 MEMORY LEAK DETECTION REPORT
==============================
Memory Status: ${memoryLeak.hasLeak ? '❌ LEAK DETECTED' : '✅ NO LEAK'}
Memory Increase: ${Math.round(memoryLeak.increase / 1024 / 1024)}MB (${memoryLeak.percentage.toFixed(1)}%)

Resource Status: ${resourceLeak.hasLeak ? '❌ LEAK DETECTED' : '✅ NO LEAK'}
Leaked Resources:
- Intervals: ${resourceLeak.leakedResources.intervals}
- Timeouts: ${resourceLeak.leakedResources.timeouts}
- Observers: ${resourceLeak.leakedResources.observers}
- Event Listeners: ${resourceLeak.leakedResources.eventListeners}

Snapshots Taken: ${this.memorySnapshots.length}
    `
  }
}

describe('🧠 CRITICAL MEMORY LEAK DETECTION TESTS', () => {
  
  let memoryDetector: MemoryLeakDetector

  beforeEach(() => {
    memoryDetector = new MemoryLeakDetector()
    jest.useFakeTimers()
    
    // Reset mock memory
    mockMemory.usedJSHeapSize = 50000000
    
    // Clear any existing resources
    MemoryLeakPrevention.cleanup()
  })

  afterEach(() => {
    jest.useRealTimers()
    MemoryLeakPrevention.cleanup()
  })

  describe('🔄 useEffect Cleanup Detection', () => {
    
    test('CRITICAL: Components with intervals should clean up properly', async () => {
      const ComponentWithInterval = ({ active }: { active: boolean }) => {
        React.useEffect(() => {
          if (!active) return

          const interval = setInterval(() => {
            // Simulate work that could cause memory leaks
            mockMemory.usedJSHeapSize += 1000 // 1KB per tick
          }, 100)

          return () => {
            clearInterval(interval)
          }
        }, [active])

        return <div>Component with interval</div>
      }

      // Mount component with interval
      const { rerender, unmount } = render(<ComponentWithInterval active={true} />)
      
      memoryDetector.takeSnapshot()
      
      // Let interval run
      jest.advanceTimersByTime(1000)
      
      // Deactivate interval
      rerender(<ComponentWithInterval active={false} />)
      
      // Unmount component
      unmount()
      
      // Check for memory leaks
      const memoryLeak = memoryDetector.detectMemoryLeak()
      const resourceLeak = memoryDetector.detectResourceLeak()
      
      expect(memoryLeak.hasLeak).toBe(false)
      expect(resourceLeak.hasLeak).toBe(false)
      
      console.log('✅ MEMORY LEAK TEST PASSED: useEffect interval cleanup')
      console.log(memoryDetector.generateReport())
    })

    test('CRITICAL: Components with event listeners should clean up properly', async () => {
      const ComponentWithEventListener = () => {
        React.useEffect(() => {
          const handleClick = () => {
            mockMemory.usedJSHeapSize += 5000 // 5KB per click
          }

          document.addEventListener('click', handleClick)

          return () => {
            document.removeEventListener('click', handleClick)
          }
        }, [])

        return <div>Component with event listener</div>
      }

      // Mount multiple components
      const components = []
      for (let i = 0; i < 10; i++) {
        components.push(render(<ComponentWithEventListener />))
      }

      memoryDetector.takeSnapshot()

      // Simulate clicks
      for (let i = 0; i < 20; i++) {
        document.dispatchEvent(new Event('click'))
      }

      // Unmount all components
      components.forEach(({ unmount }) => unmount())

      // Check for leaks
      const resourceLeak = memoryDetector.detectResourceLeak()
      
      expect(resourceLeak.hasLeak).toBe(false)
      
      console.log('✅ MEMORY LEAK TEST PASSED: Event listener cleanup')
    })

    test('CRITICAL: Components with observers should clean up properly', async () => {
      let observerCount = 0
      
      // Mock ResizeObserver
      global.ResizeObserver = jest.fn().mockImplementation(() => {
        observerCount++
        return {
          observe: jest.fn(),
          disconnect: jest.fn(() => {
            observerCount--
          }),
          unobserve: jest.fn()
        }
      })

      const ComponentWithObserver = () => {
        const ref = React.useRef<HTMLDivElement>(null)

        React.useEffect(() => {
          if (!ref.current) return

          const observer = new ResizeObserver(() => {
            mockMemory.usedJSHeapSize += 2000 // 2KB per observation
          })

          observer.observe(ref.current)

          return () => {
            observer.disconnect()
          }
        }, [])

        return <div ref={ref}>Component with observer</div>
      }

      // Mount multiple components
      const components = []
      for (let i = 0; i < 15; i++) {
        components.push(render(<ComponentWithObserver />))
      }

      memoryDetector.takeSnapshot()

      // Unmount all components
      components.forEach(({ unmount }) => unmount())

      // Check observer cleanup
      expect(observerCount).toBe(0)
      
      const resourceLeak = memoryDetector.detectResourceLeak()
      expect(resourceLeak.hasLeak).toBe(false)
      
      console.log('✅ MEMORY LEAK TEST PASSED: Observer cleanup')
    })

    test('CRITICAL: Components with async operations should clean up properly', async () => {
      const ComponentWithAsync = () => {
        const [data, setData] = React.useState(null)
        const isMountedRef = React.useRef(true)

        React.useEffect(() => {
          const fetchData = async () => {
            // Simulate async operation
            await new Promise(resolve => setTimeout(resolve, 100))
            
            if (isMountedRef.current) {
              setData('loaded')
              mockMemory.usedJSHeapSize += 10000 // 10KB of data
            }
          }

          fetchData()

          return () => {
            isMountedRef.current = false
          }
        }, [])

        return <div>{data || 'loading'}</div>
      }

      // Mount and quickly unmount components
      for (let i = 0; i < 20; i++) {
        const { unmount } = render(<ComponentWithAsync />)
        
        // Unmount before async operation completes
        setTimeout(unmount, 50)
        jest.advanceTimersByTime(50)
      }

/**
 * AUTOMATED MEMORY LEAK MONITORING
 * Continuous monitoring system for production memory leak detection
 */
export class AutomatedMemoryLeakMonitor {
  private isMonitoring = false
  private monitoringInterval: NodeJS.Timeout | null = null
  private memoryHistory: Array<{ timestamp: number; memory: number; resources: any }> = []
  private leakAlerts: Array<{ timestamp: number; type: string; severity: string; message: string }> = []

  // Thresholds for leak detection
  private thresholds = {
    memoryGrowthRate: 10 * 1024 * 1024, // 10MB per minute
    maxMemoryIncrease: 100 * 1024 * 1024, // 100MB total
    maxResourceCount: 50,
    resourceGrowthRate: 5 // 5 resources per minute
  }

  startMonitoring(intervalMs: number = 30000): void {
    if (this.isMonitoring) return

    this.isMonitoring = true
    console.log('🧠 Automated memory leak monitoring started')

    this.monitoringInterval = setInterval(() => {
      this.collectMemoryData()
      this.analyzeMemoryPatterns()
      this.checkForLeaks()
    }, intervalMs)
  }

  stopMonitoring(): void {
    if (!this.isMonitoring) return

    this.isMonitoring = false
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }

    console.log('🛑 Automated memory leak monitoring stopped')
  }

  private collectMemoryData(): void {
    const timestamp = Date.now()
    const memory = performance.memory?.usedJSHeapSize || 0
    const resources = MemoryLeakPrevention.getResourceCounts()

    this.memoryHistory.push({ timestamp, memory, resources })

    // Keep only last 100 data points
    if (this.memoryHistory.length > 100) {
      this.memoryHistory.shift()
    }
  }

  private analyzeMemoryPatterns(): void {
    if (this.memoryHistory.length < 5) return

    const recent = this.memoryHistory.slice(-5)
    const oldest = recent[0]
    const newest = recent[recent.length - 1]

    // Calculate growth rates
    const timeSpan = newest.timestamp - oldest.timestamp
    const memoryGrowth = newest.memory - oldest.memory
    const memoryGrowthRate = (memoryGrowth / timeSpan) * 60000 // Per minute

    const resourceGrowth = Object.values(newest.resources).reduce((sum: number, count: number) => sum + count, 0) -
                          Object.values(oldest.resources).reduce((sum: number, count: number) => sum + count, 0)
    const resourceGrowthRate = (resourceGrowth / timeSpan) * 60000 // Per minute

    // Store analysis results
    this.lastAnalysis = {
      memoryGrowthRate,
      resourceGrowthRate,
      totalMemoryIncrease: newest.memory - this.memoryHistory[0].memory,
      totalResourceCount: Object.values(newest.resources).reduce((sum: number, count: number) => sum + count, 0)
    }
  }

  private lastAnalysis: any = {}

  private checkForLeaks(): void {
    if (!this.lastAnalysis) return

    const { memoryGrowthRate, resourceGrowthRate, totalMemoryIncrease, totalResourceCount } = this.lastAnalysis

    // Check memory growth rate
    if (memoryGrowthRate > this.thresholds.memoryGrowthRate) {
      this.addAlert({
        type: 'memory-growth-rate',
        severity: 'high',
        message: `High memory growth rate: ${Math.round(memoryGrowthRate / 1024 / 1024)}MB/min`
      })
    }

    // Check total memory increase
    if (totalMemoryIncrease > this.thresholds.maxMemoryIncrease) {
      this.addAlert({
        type: 'memory-total-increase',
        severity: 'critical',
        message: `Excessive memory increase: ${Math.round(totalMemoryIncrease / 1024 / 1024)}MB`
      })
    }

    // Check resource count
    if (totalResourceCount > this.thresholds.maxResourceCount) {
      this.addAlert({
        type: 'resource-count',
        severity: 'medium',
        message: `High resource count: ${totalResourceCount} active resources`
      })
    }

    // Check resource growth rate
    if (resourceGrowthRate > this.thresholds.resourceGrowthRate) {
      this.addAlert({
        type: 'resource-growth-rate',
        severity: 'high',
        message: `High resource growth rate: ${resourceGrowthRate} resources/min`
      })
    }
  }

  private addAlert(alert: { type: string; severity: string; message: string }): void {
    const fullAlert = {
      timestamp: Date.now(),
      ...alert
    }

    this.leakAlerts.push(fullAlert)

    // Keep only last 50 alerts
    if (this.leakAlerts.length > 50) {
      this.leakAlerts.shift()
    }

    console.warn(`🚨 MEMORY LEAK ALERT [${alert.severity.toUpperCase()}]: ${alert.message}`)
  }

  getMemoryReport(): string {
    if (this.memoryHistory.length === 0) {
      return 'No memory data collected yet'
    }

    const latest = this.memoryHistory[this.memoryHistory.length - 1]
    const oldest = this.memoryHistory[0]
    const memoryIncrease = latest.memory - oldest.memory
    const timeSpan = latest.timestamp - oldest.timestamp

    return `
🧠 AUTOMATED MEMORY LEAK MONITORING REPORT
==========================================
Monitoring Duration: ${Math.round(timeSpan / 1000 / 60)} minutes
Data Points Collected: ${this.memoryHistory.length}

Current Memory: ${Math.round(latest.memory / 1024 / 1024)}MB
Memory Increase: ${Math.round(memoryIncrease / 1024 / 1024)}MB
Growth Rate: ${this.lastAnalysis.memoryGrowthRate ? Math.round(this.lastAnalysis.memoryGrowthRate / 1024 / 1024) : 0}MB/min

Current Resources: ${Object.values(latest.resources).reduce((sum: number, count: number) => sum + count, 0)}
- Intervals: ${latest.resources.intervals}
- Timeouts: ${latest.resources.timeouts}
- Observers: ${latest.resources.observers}
- Event Listeners: ${latest.resources.eventListeners}

🚨 ALERTS (${this.leakAlerts.length} total):
${this.leakAlerts.slice(-5).map(alert =>
  `${alert.severity.toUpperCase()}: ${alert.message}`
).join('\n') || 'No alerts'}

STATUS: ${this.getOverallStatus()}
    `
  }

  private getOverallStatus(): string {
    const criticalAlerts = this.leakAlerts.filter(alert => alert.severity === 'critical').length
    const highAlerts = this.leakAlerts.filter(alert => alert.severity === 'high').length

    if (criticalAlerts > 0) return '🚨 CRITICAL LEAKS DETECTED'
    if (highAlerts > 0) return '⚠️ POTENTIAL LEAKS DETECTED'
    return '✅ NO LEAKS DETECTED'
  }

  getCriticalAlerts(): any[] {
    return this.leakAlerts.filter(alert => alert.severity === 'critical')
  }

  getMemoryHistory(): any[] {
    return [...this.memoryHistory]
  }

  clearAlerts(): void {
    this.leakAlerts = []
  }

  // Export data for analysis
  exportData(): any {
    return {
      memoryHistory: this.memoryHistory,
      alerts: this.leakAlerts,
      thresholds: this.thresholds,
      lastAnalysis: this.lastAnalysis
    }
  }
}

// Export singleton instance
export const automatedMemoryLeakMonitor = new AutomatedMemoryLeakMonitor()

// Auto-start in development
if (import.meta.env.DEV) {
  automatedMemoryLeakMonitor.startMonitoring(30000) // Every 30 seconds

  // Log report every 5 minutes
  setInterval(() => {
    console.log(automatedMemoryLeakMonitor.getMemoryReport())
  }, 300000)
}

      // Wait for all async operations to complete
      jest.advanceTimersByTime(200)

      const memoryLeak = memoryDetector.detectMemoryLeak()
      
      // Should not have significant memory increase from cancelled operations
      expect(memoryLeak.increase).toBeLessThan(100 * 1024) // Less than 100KB
      
      console.log('✅ MEMORY LEAK TEST PASSED: Async operation cleanup')
    })
  })

  describe('🔧 Resource Management Detection', () => {
    
    test('CRITICAL: MemoryLeakPrevention utilities should track resources', () => {
      const initialCounts = MemoryLeakPrevention.getResourceCounts()

      // Create resources using safe utilities
      const intervals = []
      const timeouts = []

      for (let i = 0; i < 5; i++) {
        intervals.push(MemoryLeakPrevention.setInterval(() => {}, 1000))
        timeouts.push(MemoryLeakPrevention.setTimeout(() => {}, 5000))
      }

      const afterCreation = MemoryLeakPrevention.getResourceCounts()
      
      expect(afterCreation.intervals).toBe(initialCounts.intervals + 5)
      expect(afterCreation.timeouts).toBe(initialCounts.timeouts + 5)

      // Cleanup
      MemoryLeakPrevention.cleanup()

      const afterCleanup = MemoryLeakPrevention.getResourceCounts()
      
      expect(afterCleanup.intervals).toBe(0)
      expect(afterCleanup.timeouts).toBe(0)

      console.log('✅ MEMORY LEAK TEST PASSED: Resource tracking')
    })

    test('CRITICAL: SafeDOM utilities should prevent memory leaks', () => {
      const element = document.createElement('div')
      document.body.appendChild(element)

      // Use SafeDOM for event listeners
      const cleanup1 = SafeDOM.addEventListenerSafe(element, 'click', () => {})
      const cleanup2 = SafeDOM.addEventListenerSafe(element, 'mouseover', () => {})

      memoryDetector.takeSnapshot()

      // Cleanup
      cleanup1()
      cleanup2()

      document.body.removeChild(element)

      const resourceLeak = memoryDetector.detectResourceLeak()
      expect(resourceLeak.hasLeak).toBe(false)

      console.log('✅ MEMORY LEAK TEST PASSED: SafeDOM utilities')
    })
  })

  describe('📊 Memory Growth Pattern Detection', () => {
    
    test('CRITICAL: Repeated operations should not cause linear memory growth', async () => {
      const ComponentWithPotentialLeak = ({ iteration }: { iteration: number }) => {
        const [data, setData] = React.useState<any[]>([])

        React.useEffect(() => {
          // Simulate operation that could cause memory growth
          const newData = Array(1000).fill(null).map((_, i) => ({
            id: i + iteration * 1000,
            value: Math.random(),
            timestamp: Date.now()
          }))

          setData(newData)
          mockMemory.usedJSHeapSize += newData.length * 100 // Simulate memory usage

          return () => {
            // Proper cleanup
            setData([])
            mockMemory.usedJSHeapSize -= newData.length * 100
          }
        }, [iteration])

        return <div>Data items: {data.length}</div>
      }

      const memorySnapshots = []

      // Perform repeated operations
      for (let i = 0; i < 10; i++) {
        const { unmount } = render(<ComponentWithPotentialLeak iteration={i} />)
        
        memorySnapshots.push(memoryDetector.getCurrentMemory())
        
        unmount()
        
        // Force cleanup
        jest.advanceTimersByTime(100)
      }

      // Check for linear growth pattern
      const growthRates = []
      for (let i = 1; i < memorySnapshots.length; i++) {
        const growth = memorySnapshots[i] - memorySnapshots[i - 1]
        growthRates.push(growth)
      }

      // Memory should not continuously grow
      const averageGrowth = growthRates.reduce((sum, rate) => sum + rate, 0) / growthRates.length
      
      expect(Math.abs(averageGrowth)).toBeLessThan(1024 * 1024) // Less than 1MB average growth

      console.log('✅ MEMORY LEAK TEST PASSED: No linear memory growth')
      console.log(`Average memory growth per operation: ${Math.round(averageGrowth / 1024)}KB`)
    })

    test('CRITICAL: Memory should be reclaimed after component unmount', async () => {
      const HeavyComponent = () => {
        const [heavyData, setHeavyData] = React.useState<number[]>([])

        React.useEffect(() => {
          // Create heavy data structure
          const data = Array(10000).fill(0).map(() => Math.random())
          setHeavyData(data)
          
          // Simulate memory allocation
          mockMemory.usedJSHeapSize += data.length * 8 // 8 bytes per number

          return () => {
            // Cleanup heavy data
            setHeavyData([])
            mockMemory.usedJSHeapSize -= data.length * 8
          }
        }, [])

        return <div>Heavy component with {heavyData.length} items</div>
      }

      const initialMemory = memoryDetector.getCurrentMemory()

      // Mount heavy component
      const { unmount } = render(<HeavyComponent />)
      
      const peakMemory = memoryDetector.getCurrentMemory()
      
      // Unmount component
      unmount()
      
      // Allow cleanup
      jest.advanceTimersByTime(100)
      
      const finalMemory = memoryDetector.getCurrentMemory()

      // Memory should be reclaimed
      const memoryReclaimed = peakMemory - finalMemory
      const reclaimPercentage = (memoryReclaimed / (peakMemory - initialMemory)) * 100

      expect(reclaimPercentage).toBeGreaterThan(80) // At least 80% reclaimed

      console.log('✅ MEMORY LEAK TEST PASSED: Memory reclamation')
      console.log(`Memory reclaimed: ${reclaimPercentage.toFixed(1)}%`)
    })
  })
})

/**
 * MEMORY LEAK DETECTION UTILITIES
 */
export const MemoryLeakDetectionUtils = {
  
  // Create memory leak detector
  createDetector: () => new MemoryLeakDetector(),

  // Simulate memory pressure
  simulateMemoryPressure: (sizeInMB: number = 10) => {
    const bytes = sizeInMB * 1024 * 1024
    mockMemory.usedJSHeapSize += bytes
    return bytes
  },

  // Simulate memory cleanup
  simulateMemoryCleanup: (sizeInMB: number = 10) => {
    const bytes = sizeInMB * 1024 * 1024
    mockMemory.usedJSHeapSize = Math.max(0, mockMemory.usedJSHeapSize - bytes)
    return bytes
  },

  // Check for memory leaks in component
  checkComponentForLeaks: async (Component: React.ComponentType, iterations: number = 10) => {
    const detector = new MemoryLeakDetector()
    
    for (let i = 0; i < iterations; i++) {
      const { unmount } = render(React.createElement(Component))
      detector.takeSnapshot()
      unmount()
    }
    
    return {
      memoryLeak: detector.detectMemoryLeak(),
      resourceLeak: detector.detectResourceLeak(),
      report: detector.generateReport()
    }
  },

  // Monitor memory over time
  monitorMemoryOverTime: (durationMs: number = 10000, intervalMs: number = 1000) => {
    const snapshots: { timestamp: number; memory: number; resources: any }[] = []
    
    const interval = setInterval(() => {
      snapshots.push({
        timestamp: Date.now(),
        memory: performance.memory?.usedJSHeapSize || 0,
        resources: MemoryLeakPrevention.getResourceCounts()
      })
    }, intervalMs)
    
    setTimeout(() => {
      clearInterval(interval)
    }, durationMs)
    
    return snapshots
  }
}
