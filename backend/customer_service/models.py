from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
import uuid

class SupportTicketCategory(models.Model):
    """Categories for support tickets"""
    name = models.CharField(max_length=100)
    name_ar = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    color = models.CharField(max_length=7, default='#3B82F6')  # Hex color
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Support Ticket Categories"

    def __str__(self):
        return self.name

class SupportAgent(models.Model):
    """Support agents who handle tickets"""
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    agent_id = models.CharField(max_length=20, unique=True)
    specializations = models.JSONField(default=list)  # List of specialization areas
    max_concurrent_tickets = models.IntegerField(default=10)
    is_available = models.BooleanField(default=True)
    performance_rating = models.FloatField(
        default=5.0,
        validators=[MinValueValidator(1.0), MaxValueValidator(5.0)]
    )
    total_tickets_resolved = models.IntegerField(default=0)
    average_resolution_time = models.DurationField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.get_full_name()} ({self.agent_id})"

class ServiceLevelAgreement(models.Model):
    """SLA definitions for different ticket types"""
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    name = models.CharField(max_length=100)
    name_ar = models.CharField(max_length=100)
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES)
    response_time_hours = models.IntegerField()  # Hours to first response
    resolution_time_hours = models.IntegerField()  # Hours to resolution
    escalation_time_hours = models.IntegerField()  # Hours before escalation
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.priority})"

class SupportTicket(models.Model):
    """Main support ticket model"""
    STATUS_CHOICES = [
        ('open', 'Open'),
        ('in_progress', 'In Progress'),
        ('pending_customer', 'Pending Customer'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
        ('cancelled', 'Cancelled'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    SOURCE_CHOICES = [
        ('email', 'Email'),
        ('phone', 'Phone'),
        ('chat', 'Live Chat'),
        ('web', 'Web Form'),
        ('mobile', 'Mobile App'),
        ('social', 'Social Media'),
    ]

    # Basic Information
    ticket_id = models.CharField(max_length=20, unique=True)
    title = models.CharField(max_length=200)
    description = models.TextField()

    # Customer Information
    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='support_tickets')
    customer_email = models.EmailField()
    customer_phone = models.CharField(max_length=20, blank=True)

    # Categorization
    category = models.ForeignKey(SupportTicketCategory, on_delete=models.SET_NULL, null=True)
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='medium')
    source = models.CharField(max_length=20, choices=SOURCE_CHOICES, default='web')

    # Assignment
    assigned_agent = models.ForeignKey(SupportAgent, on_delete=models.SET_NULL, null=True, blank=True)
    sla = models.ForeignKey(ServiceLevelAgreement, on_delete=models.SET_NULL, null=True)

    # Status and Tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='open')
    tags = models.JSONField(default=list)  # List of tags

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    first_response_at = models.DateTimeField(null=True, blank=True)
    resolved_at = models.DateTimeField(null=True, blank=True)
    closed_at = models.DateTimeField(null=True, blank=True)

    # SLA Tracking
    sla_response_due = models.DateTimeField(null=True, blank=True)
    sla_resolution_due = models.DateTimeField(null=True, blank=True)
    sla_escalation_due = models.DateTimeField(null=True, blank=True)
    is_sla_breached = models.BooleanField(default=False)

    # Metrics
    customer_satisfaction_rating = models.IntegerField(
        null=True, blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    resolution_time = models.DurationField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.ticket_id} - {self.title}"

    def save(self, *args, **kwargs):
        if not self.ticket_id:
            self.ticket_id = f"TKT-{timezone.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"

        # Set SLA due dates if SLA is assigned
        if self.sla and not self.sla_response_due:
            self.sla_response_due = self.created_at + timezone.timedelta(hours=self.sla.response_time_hours)
            self.sla_resolution_due = self.created_at + timezone.timedelta(hours=self.sla.resolution_time_hours)
            self.sla_escalation_due = self.created_at + timezone.timedelta(hours=self.sla.escalation_time_hours)

        super().save(*args, **kwargs)

class TicketComment(models.Model):
    """Comments/replies on support tickets"""
    COMMENT_TYPE_CHOICES = [
        ('customer', 'Customer Reply'),
        ('agent', 'Agent Reply'),
        ('internal', 'Internal Note'),
        ('system', 'System Update'),
    ]

    ticket = models.ForeignKey(SupportTicket, on_delete=models.CASCADE, related_name='comments')
    author = models.ForeignKey(User, on_delete=models.CASCADE)
    comment_type = models.CharField(max_length=20, choices=COMMENT_TYPE_CHOICES)
    content = models.TextField()
    is_public = models.BooleanField(default=True)  # Visible to customer
    attachments = models.JSONField(default=list)  # List of file URLs
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        return f"Comment on {self.ticket.ticket_id} by {self.author.username}"

class KnowledgeBaseCategory(models.Model):
    """Categories for knowledge base articles"""
    name = models.CharField(max_length=100)
    name_ar = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    icon = models.CharField(max_length=50, default='HelpCircle')
    color = models.CharField(max_length=7, default='#3B82F6')
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)
    sort_order = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Knowledge Base Categories"
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name

class KnowledgeBaseArticle(models.Model):
    """Knowledge base articles and FAQs"""
    ARTICLE_TYPE_CHOICES = [
        ('article', 'Article'),
        ('faq', 'FAQ'),
        ('tutorial', 'Tutorial'),
        ('troubleshooting', 'Troubleshooting'),
        ('policy', 'Policy'),
    ]

    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200)
    content = models.TextField()
    content_ar = models.TextField()
    summary = models.TextField(blank=True)
    summary_ar = models.TextField(blank=True)

    category = models.ForeignKey(KnowledgeBaseCategory, on_delete=models.CASCADE)
    article_type = models.CharField(max_length=20, choices=ARTICLE_TYPE_CHOICES, default='article')
    tags = models.JSONField(default=list)

    author = models.ForeignKey(User, on_delete=models.CASCADE)
    is_published = models.BooleanField(default=False)
    is_featured = models.BooleanField(default=False)

    # SEO and Search
    slug = models.SlugField(max_length=250, unique=True)
    meta_description = models.CharField(max_length=160, blank=True)

    # Analytics
    view_count = models.IntegerField(default=0)
    helpful_votes = models.IntegerField(default=0)
    not_helpful_votes = models.IntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    published_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    @property
    def helpfulness_ratio(self):
        total_votes = self.helpful_votes + self.not_helpful_votes
        if total_votes == 0:
            return 0
        return (self.helpful_votes / total_votes) * 100

class CustomerFeedback(models.Model):
    """Customer feedback and satisfaction surveys"""
    FEEDBACK_TYPE_CHOICES = [
        ('ticket_rating', 'Ticket Rating'),
        ('service_survey', 'Service Survey'),
        ('product_feedback', 'Product Feedback'),
        ('general_feedback', 'General Feedback'),
    ]

    customer = models.ForeignKey(User, on_delete=models.CASCADE)
    feedback_type = models.CharField(max_length=20, choices=FEEDBACK_TYPE_CHOICES)
    related_ticket = models.ForeignKey(SupportTicket, on_delete=models.CASCADE, null=True, blank=True)

    # Rating (1-5 stars)
    overall_rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    response_time_rating = models.IntegerField(
        null=True, blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    solution_quality_rating = models.IntegerField(
        null=True, blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    agent_professionalism_rating = models.IntegerField(
        null=True, blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )

    # Text feedback
    comments = models.TextField(blank=True)
    suggestions = models.TextField(blank=True)

    # Metadata
    is_anonymous = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Feedback from {self.customer.username} - {self.overall_rating} stars"

class LiveChatSession(models.Model):
    """Live chat sessions between customers and agents"""
    STATUS_CHOICES = [
        ('waiting', 'Waiting for Agent'),
        ('active', 'Active Chat'),
        ('ended', 'Chat Ended'),
        ('transferred', 'Transferred'),
    ]

    session_id = models.CharField(max_length=50, unique=True)
    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='chat_sessions')
    agent = models.ForeignKey(SupportAgent, on_delete=models.SET_NULL, null=True, blank=True)

    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='waiting')
    subject = models.CharField(max_length=200, blank=True)

    # Session metadata
    customer_ip = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    referrer_url = models.URLField(blank=True)

    # Timestamps
    started_at = models.DateTimeField(auto_now_add=True)
    agent_joined_at = models.DateTimeField(null=True, blank=True)
    ended_at = models.DateTimeField(null=True, blank=True)

    # Metrics
    wait_time = models.DurationField(null=True, blank=True)
    chat_duration = models.DurationField(null=True, blank=True)
    customer_satisfaction = models.IntegerField(
        null=True, blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )

    def __str__(self):
        return f"Chat {self.session_id} - {self.customer.username}"

    def save(self, *args, **kwargs):
        if not self.session_id:
            self.session_id = f"CHAT-{timezone.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
        super().save(*args, **kwargs)

class ChatMessage(models.Model):
    """Messages in live chat sessions"""
    MESSAGE_TYPE_CHOICES = [
        ('customer', 'Customer Message'),
        ('agent', 'Agent Message'),
        ('system', 'System Message'),
        ('file', 'File Attachment'),
    ]

    session = models.ForeignKey(LiveChatSession, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPE_CHOICES)
    content = models.TextField()
    attachments = models.JSONField(default=list)  # List of file URLs
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        return f"Message in {self.session.session_id} by {self.sender.username}"

# Import advanced models
from .models_advanced import *
