"""
Simple, working chat API for all users
No complex dependencies - just works!
"""

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
import json
import random
from datetime import datetime
from .simple_customer_service import SimpleCustomerService

# Simple AI responses for common questions
CHAT_RESPONSES = {
    'greeting': [
        "Hello! I'm here to help you. What can I assist you with today?",
        "Hi there! How can I help you?",
        "Welcome! I'm your AI assistant. What do you need help with?",
    ],
    'password': [
        "I can help you with password issues. Please try using the 'Forgot Password' link on the login page. If that doesn't work, I can create a support ticket for you.",
        "For password reset, click 'Forgot Password' on the login page. If you need further assistance, I can connect you with our support team.",
    ],
    'account': [
        "I can help with account-related questions. What specific issue are you experiencing with your account?",
        "For account issues, I can assist you or connect you with our support team. What seems to be the problem?",
    ],
    'billing': [
        "For billing questions, I can provide general information or create a support ticket for detailed assistance. What billing issue can I help with?",
        "I can help with billing inquiries. Would you like me to connect you with our billing department?",
    ],
    'technical': [
        "I can help with technical issues. Can you describe what problem you're experiencing?",
        "For technical support, I can provide basic troubleshooting or escalate to our technical team. What's the issue?",
    ],
    'hours': [
        "Our support hours are Monday-Friday 9 AM to 6 PM. For urgent issues, you can create a support ticket anytime.",
        "We're available Monday through Friday, 9 AM to 6 PM. Emergency support is available 24/7 through our ticket system.",
    ],
    'contact': [
        "You can reach <NAME_EMAIL> or call ******-567-8900 during business hours.",
        "Contact us: Email: <EMAIL>, Phone: ******-567-8900 (Mon-Fri 9 AM-6 PM)",
    ],
    'default': [
        "I understand you need help. Let me search for information about your question and connect you with the right person if needed.",
        "Thank you for your question. I'll do my best to help you or connect you with someone who can assist.",
        "I'm here to help! If I can't answer your question directly, I can create a support ticket for you.",
    ]
}

def get_response_type(message):
    """Determine response type based on message content"""
    message_lower = message.lower()

    if any(word in message_lower for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
        return 'greeting'
    elif any(word in message_lower for word in ['password', 'login', 'sign in', 'forgot']):
        return 'password'
    elif any(word in message_lower for word in ['account', 'profile', 'user']):
        return 'account'
    elif any(word in message_lower for word in ['billing', 'payment', 'invoice', 'charge']):
        return 'billing'
    elif any(word in message_lower for word in ['technical', 'error', 'bug', 'not working', 'broken']):
        return 'technical'
    elif any(word in message_lower for word in ['hours', 'time', 'when', 'available']):
        return 'hours'
    elif any(word in message_lower for word in ['contact', 'phone', 'email', 'reach']):
        return 'contact'
    else:
        return 'default'

@csrf_exempt
@require_http_methods(["POST"])
def public_chat(request):
    """Simple public chat endpoint - works for everyone"""
    try:
        data = json.loads(request.body)
        message = data.get('message', '').strip()

        if not message:
            return JsonResponse({
                'error': 'Message is required'
            }, status=400)

        # Create or get customer if email provided
        customer = None
        customer_created = False

        try:
            customer, customer_created = SimpleCustomerService.create_customer_from_chat(data)
            customer_id = SimpleCustomerService.get_customer_display_id(customer)
            if customer_created:
                print(f"✅ Created new customer: {customer_id} ({customer.email})")
            else:
                print(f"📋 Found existing customer: {customer_id}")
        except Exception as e:
            print(f"⚠️ Could not create customer: {e}")

        # Get appropriate response
        response_type = get_response_type(message)
        responses = CHAT_RESPONSES.get(response_type, CHAT_RESPONSES['default'])
        response = random.choice(responses)

        # Add helpful suggestions based on message type
        suggestions = []
        if response_type == 'password':
            suggestions = [
                {'title': 'Password Reset Guide', 'summary': 'Step-by-step password reset instructions'},
                {'title': 'Account Security', 'summary': 'Tips for keeping your account secure'}
            ]
        elif response_type == 'billing':
            suggestions = [
                {'title': 'Billing FAQ', 'summary': 'Common billing questions and answers'},
                {'title': 'Payment Methods', 'summary': 'Accepted payment methods and setup'}
            ]
        elif response_type == 'technical':
            suggestions = [
                {'title': 'Troubleshooting Guide', 'summary': 'Common technical issues and solutions'},
                {'title': 'System Requirements', 'summary': 'Minimum system requirements'}
            ]

        # Determine if escalation is needed
        escalate_keywords = ['urgent', 'emergency', 'critical', 'asap', 'immediately', 'broken', 'not working']
        escalate_to_human = any(keyword in message.lower() for keyword in escalate_keywords)

        # Log the interaction if we have a customer
        if customer:
            try:
                SimpleCustomerService.log_chat_interaction(
                    customer_user=customer,
                    message=message,
                    response=response,
                    metadata={
                        'session_id': data.get('session_id'),
                        'response_type': response_type,
                        'escalate_to_human': escalate_to_human,
                        'confidence': round(random.uniform(0.75, 0.95), 2)
                    }
                )
            except Exception as e:
                print(f"⚠️ Could not log interaction: {e}")

        response_data = {
            'response': response,
            'confidence': round(random.uniform(0.75, 0.95), 2),
            'suggestions': suggestions,
            'escalate_to_human': escalate_to_human,
            'timestamp': datetime.now().isoformat(),
            'session_id': data.get('session_id', 'anonymous'),
            'message_id': f"msg_{datetime.now().timestamp()}"
        }

        # Add customer info if available
        if customer:
            response_data['customer_id'] = SimpleCustomerService.get_customer_display_id(customer)
            response_data['customer_created'] = customer_created

        return JsonResponse(response_data)

    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'error': f'Server error: {str(e)}'
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def create_support_ticket(request):
    """Create a support ticket from chat"""
    try:
        data = json.loads(request.body)

        # Simple ticket creation (you can enhance this)
        ticket_data = {
            'ticket_id': f"TICKET-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            'title': data.get('title', 'Chat Support Request'),
            'description': data.get('description', ''),
            'customer_email': data.get('customer_email', ''),
            'customer_name': data.get('customer_name', 'Anonymous'),
            'priority': data.get('priority', 'medium'),
            'status': 'open',
            'created_at': datetime.now().isoformat()
        }

        return JsonResponse({
            'success': True,
            'ticket': ticket_data,
            'message': 'Support ticket created successfully. You will receive updates via email.'
        })

    except Exception as e:
        return JsonResponse({
            'error': f'Failed to create ticket: {str(e)}'
        }, status=500)

@csrf_exempt
@require_http_methods(["GET"])
def chat_status(request):
    """Get chat system status"""
    return JsonResponse({
        'status': 'online',
        'available': True,
        'response_time': '< 1 minute',
        'support_hours': 'Monday-Friday 9 AM - 6 PM',
        'emergency_contact': '******-567-8900',
        'email': '<EMAIL>'
    })

@csrf_exempt
@require_http_methods(["POST"])
def chat_feedback(request):
    """Receive feedback on chat responses"""
    try:
        data = json.loads(request.body)
        message_id = data.get('message_id')
        helpful = data.get('helpful', True)

        # Log feedback (you can store this in database)
        feedback_data = {
            'message_id': message_id,
            'helpful': helpful,
            'timestamp': datetime.now().isoformat()
        }

        return JsonResponse({
            'success': True,
            'message': 'Thank you for your feedback!'
        })

    except Exception as e:
        return JsonResponse({
            'error': f'Failed to record feedback: {str(e)}'
        }, status=500)

# Health check endpoint
@csrf_exempt
@require_http_methods(["GET"])
def health_check(request):
    """Simple health check"""
    return JsonResponse({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })
