"""
Advanced Customer Service Serializers
For automation, AI, and integration features
"""

from rest_framework import serializers
from django.contrib.auth.models import User
from .models_advanced import (
    TicketRoutingRule, EscalationRule, AIAssistant, AIInteraction,
    WorkflowAutomation, WorkflowExecution, IntegrationConnector,
    CustomerTier, CustomerProfile
)
from .serializers import UserSerializer

class TicketRoutingRuleSerializer(serializers.ModelSerializer):
    condition_params = serializers.SerializerMethodField()
    action_params = serializers.SerializerMethodField()
    
    class Meta:
        model = TicketRoutingRule
        fields = '__all__'
    
    def get_condition_params(self, obj):
        return obj.get_condition_params()
    
    def get_action_params(self, obj):
        return obj.get_action_params()

class EscalationRuleSerializer(serializers.ModelSerializer):
    escalate_to_agent_details = serializers.SerializerMethodField()
    
    class Meta:
        model = EscalationRule
        fields = '__all__'
    
    def get_escalate_to_agent_details(self, obj):
        if obj.escalate_to_agent:
            return {
                'id': obj.escalate_to_agent.id,
                'name': f"{obj.escalate_to_agent.user.first_name} {obj.escalate_to_agent.user.last_name}",
                'agent_id': obj.escalate_to_agent.agent_id
            }
        return None

class AIAssistantSerializer(serializers.ModelSerializer):
    success_rate = serializers.ReadOnlyField()
    
    class Meta:
        model = AIAssistant
        fields = '__all__'
        extra_kwargs = {
            'api_key_encrypted': {'write_only': True}
        }

class AIInteractionSerializer(serializers.ModelSerializer):
    assistant_name = serializers.CharField(source='assistant.name', read_only=True)
    user_details = UserSerializer(source='user', read_only=True)
    
    class Meta:
        model = AIInteraction
        fields = '__all__'

class WorkflowAutomationSerializer(serializers.ModelSerializer):
    success_rate = serializers.ReadOnlyField()
    workflow_steps_parsed = serializers.SerializerMethodField()
    trigger_conditions_parsed = serializers.SerializerMethodField()
    
    class Meta:
        model = WorkflowAutomation
        fields = '__all__'
    
    def get_workflow_steps_parsed(self, obj):
        try:
            import json
            return json.loads(obj.workflow_steps)
        except:
            return []
    
    def get_trigger_conditions_parsed(self, obj):
        try:
            import json
            return json.loads(obj.trigger_conditions) if obj.trigger_conditions else {}
        except:
            return {}

class WorkflowExecutionSerializer(serializers.ModelSerializer):
    workflow_name = serializers.CharField(source='workflow.name', read_only=True)
    ticket_details = serializers.SerializerMethodField()
    execution_data_parsed = serializers.SerializerMethodField()
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = WorkflowExecution
        fields = '__all__'
    
    def get_ticket_details(self, obj):
        if obj.related_ticket:
            return {
                'id': obj.related_ticket.id,
                'ticket_id': obj.related_ticket.ticket_id,
                'title': obj.related_ticket.title,
                'status': obj.related_ticket.status
            }
        return None
    
    def get_execution_data_parsed(self, obj):
        try:
            import json
            return json.loads(obj.execution_data) if obj.execution_data else {}
        except:
            return {}
    
    def get_duration(self, obj):
        if obj.completed_at and obj.started_at:
            duration = obj.completed_at - obj.started_at
            return duration.total_seconds()
        return None

class IntegrationConnectorSerializer(serializers.ModelSerializer):
    sync_success_rate = serializers.ReadOnlyField()
    connection_status = serializers.SerializerMethodField()
    
    class Meta:
        model = IntegrationConnector
        fields = '__all__'
        extra_kwargs = {
            'api_key_encrypted': {'write_only': True},
            'password_encrypted': {'write_only': True}
        }
    
    def get_connection_status(self, obj):
        if obj.last_sync_at:
            from django.utils import timezone
            from datetime import timedelta
            
            # Consider connection healthy if synced within last 2 hours
            if obj.last_sync_at > timezone.now() - timedelta(hours=2):
                return 'healthy'
            else:
                return 'stale'
        return 'unknown'

class CustomerTierSerializer(serializers.ModelSerializer):
    benefits_parsed = serializers.SerializerMethodField()
    customer_count = serializers.SerializerMethodField()
    
    class Meta:
        model = CustomerTier
        fields = '__all__'
    
    def get_benefits_parsed(self, obj):
        try:
            import json
            return json.loads(obj.benefits) if obj.benefits else []
        except:
            return []
    
    def get_customer_count(self, obj):
        return CustomerProfile.objects.filter(customer_tier=obj).count()

class CustomerProfileSerializer(serializers.ModelSerializer):
    user_details = UserSerializer(source='user', read_only=True)
    tier_details = CustomerTierSerializer(source='customer_tier', read_only=True)
    satisfaction_level = serializers.SerializerMethodField()
    tags_list = serializers.SerializerMethodField()
    
    class Meta:
        model = CustomerProfile
        fields = '__all__'
    
    def get_satisfaction_level(self, obj):
        if obj.average_satisfaction >= 4.5:
            return 'excellent'
        elif obj.average_satisfaction >= 4.0:
            return 'good'
        elif obj.average_satisfaction >= 3.0:
            return 'average'
        elif obj.average_satisfaction > 0:
            return 'poor'
        return 'no_data'
    
    def get_tags_list(self, obj):
        if obj.tags:
            return [tag.strip() for tag in obj.tags.split(',') if tag.strip()]
        return []

# Analytics Serializers
class AutomationMetricsSerializer(serializers.Serializer):
    routing_rules = serializers.DictField()
    ai_assistants = serializers.DictField()
    workflows = serializers.DictField()
    integrations = serializers.DictField()

class CustomerInsightsSerializer(serializers.Serializer):
    customer_tiers = serializers.DictField()
    satisfaction_trends = serializers.ListField()
    contact_preferences = serializers.DictField()
    language_preferences = serializers.DictField()

class AIResponseSerializer(serializers.Serializer):
    response = serializers.CharField()
    confidence = serializers.FloatField()
    suggestions = serializers.ListField()
    escalate_to_human = serializers.BooleanField()

class SentimentAnalysisSerializer(serializers.Serializer):
    sentiment = serializers.CharField()
    score = serializers.FloatField()
    confidence = serializers.FloatField()

class WorkflowTestResultSerializer(serializers.Serializer):
    workflow_name = serializers.CharField()
    steps_tested = serializers.IntegerField()
    success = serializers.BooleanField()
    execution_time_ms = serializers.IntegerField()

class IntegrationTestResultSerializer(serializers.Serializer):
    connector_name = serializers.CharField()
    connection_status = serializers.CharField()
    response_time_ms = serializers.IntegerField()
    last_tested = serializers.DateTimeField()

class CustomerSupportHistorySerializer(serializers.Serializer):
    total_tickets = serializers.IntegerField()
    resolved_tickets = serializers.IntegerField()
    average_satisfaction = serializers.FloatField()
    recent_tickets = serializers.ListField()
    recent_feedback = serializers.ListField()
