export interface Asset {
  id: string
  name: string
  name_ar: string
  description?: string
  description_ar?: string
  category: string
  status: 'ACTIVE' | 'INACTIVE' | 'MAINTENANCE' | 'DISPOSED'
  condition: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR'
  purchase_date: string
  purchase_price: number
  current_value: number
  depreciation_rate: number
  location: string
  assigned_to?: string
  maintenance_schedule?: string
  warranty_expiry?: string
  serial_number?: string
  model?: string
  manufacturer?: string
  created_at: string
  updated_at: string
}

export interface CreateAssetData {
  name: string
  name_ar: string
  description?: string
  description_ar?: string
  category: string
  status: Asset['status']
  condition: Asset['condition']
  purchase_date: string
  purchase_price: number
  current_value: number
  depreciation_rate: number
  location: string
  assigned_to?: string
  maintenance_schedule?: string
  warranty_expiry?: string
  serial_number?: string
  model?: string
  manufacturer?: string
}

export interface AssetFilters {
  search?: string
  category?: string
  status?: Asset['status']
  condition?: Asset['condition']
  assigned_to?: string
  location?: string
  page?: number
  page_size?: number
}

export interface AssetStats {
  total_assets: number
  by_status: Record<string, number>
  by_condition: Record<string, number>
  total_value: number
  maintenance_due: number
}
