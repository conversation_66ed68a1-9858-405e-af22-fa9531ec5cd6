#!/usr/bin/env python3
"""
CRITICAL FIX: Database Performance Testing Script
Tests query performance before and after optimizations
"""

import os
import sys
import time
from pathlib import Path

# Add Django to path
sys.path.append(str(Path(__file__).parent.parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

import django
django.setup()

from django.db import connection, reset_queries
from django.test.utils import override_settings
from ems.models import Employee, Department, LeaveRequest, Attendance, Project, Task

class PerformanceTester:
    def __init__(self):
        self.results = {}
        
    def run_all_tests(self):
        """Run comprehensive performance tests"""
        print("🚀 Running Database Performance Tests...")
        print("=" * 60)
        
        # Enable query logging
        with override_settings(DEBUG=True):
            self.test_employee_queries()
            self.test_department_queries()
            self.test_leave_request_queries()
            self.test_attendance_queries()
            self.test_project_queries()
            self.test_complex_joins()
            
        self.print_summary()
        
    def test_employee_queries(self):
        """Test employee-related queries"""
        print("👥 Testing Employee Queries...")
        
        # Test 1: Employee list with department (should use select_related)
        reset_queries()
        start_time = time.time()
        
        employees = list(Employee.objects.select_related('user', 'department')[:100])
        
        end_time = time.time()
        query_count = len(connection.queries)
        
        self.results['employee_list'] = {
            'time': end_time - start_time,
            'queries': query_count,
            'records': len(employees)
        }
        
        print(f"  📊 Employee list: {query_count} queries, {(end_time - start_time)*1000:.2f}ms")
        
        # Test 2: Employee search by department
        reset_queries()
        start_time = time.time()
        
        dept_employees = list(Employee.objects.filter(
            department__name__icontains='Engineering'
        ).select_related('user', 'department'))
        
        end_time = time.time()
        query_count = len(connection.queries)
        
        self.results['employee_search'] = {
            'time': end_time - start_time,
            'queries': query_count,
            'records': len(dept_employees)
        }
        
        print(f"  🔍 Employee search: {query_count} queries, {(end_time - start_time)*1000:.2f}ms")
        
    def test_department_queries(self):
        """Test department-related queries"""
        print("🏢 Testing Department Queries...")
        
        # Test: Department list with employee count
        reset_queries()
        start_time = time.time()
        
        departments = list(Department.objects.prefetch_related('employee_set')[:50])
        for dept in departments:
            employee_count = dept.employee_set.count()
        
        end_time = time.time()
        query_count = len(connection.queries)
        
        self.results['department_list'] = {
            'time': end_time - start_time,
            'queries': query_count,
            'records': len(departments)
        }
        
        print(f"  📊 Department list: {query_count} queries, {(end_time - start_time)*1000:.2f}ms")
        
    def test_leave_request_queries(self):
        """Test leave request queries"""
        print("🏖️ Testing Leave Request Queries...")
        
        # Test: Leave requests with employee and approver info
        reset_queries()
        start_time = time.time()
        
        leave_requests = list(LeaveRequest.objects.select_related(
            'employee__user', 'leave_type', 'approved_by__user'
        )[:100])
        
        end_time = time.time()
        query_count = len(connection.queries)
        
        self.results['leave_requests'] = {
            'time': end_time - start_time,
            'queries': query_count,
            'records': len(leave_requests)
        }
        
        print(f"  📊 Leave requests: {query_count} queries, {(end_time - start_time)*1000:.2f}ms")
        
    def test_attendance_queries(self):
        """Test attendance queries"""
        print("⏰ Testing Attendance Queries...")
        
        # Test: Recent attendance records
        reset_queries()
        start_time = time.time()
        
        attendance = list(Attendance.objects.select_related('employee__user')
                         .order_by('-date')[:200])
        
        end_time = time.time()
        query_count = len(connection.queries)
        
        self.results['attendance'] = {
            'time': end_time - start_time,
            'queries': query_count,
            'records': len(attendance)
        }
        
        print(f"  📊 Attendance records: {query_count} queries, {(end_time - start_time)*1000:.2f}ms")
        
    def test_project_queries(self):
        """Test project and task queries"""
        print("📋 Testing Project Queries...")
        
        # Test: Projects with tasks and team members
        reset_queries()
        start_time = time.time()
        
        projects = list(Project.objects.select_related('project_manager__user', 'department')
                       .prefetch_related('team_members', 'tasks')[:50])
        
        for project in projects:
            task_count = project.tasks.count()
            member_count = project.team_members.count()
        
        end_time = time.time()
        query_count = len(connection.queries)
        
        self.results['projects'] = {
            'time': end_time - start_time,
            'queries': query_count,
            'records': len(projects)
        }
        
        print(f"  📊 Projects with tasks: {query_count} queries, {(end_time - start_time)*1000:.2f}ms")
        
    def test_complex_joins(self):
        """Test complex join queries"""
        print("🔗 Testing Complex Join Queries...")
        
        # Test: Complex query with multiple joins
        reset_queries()
        start_time = time.time()
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    e.id,
                    u.username,
                    d.name as department_name,
                    COUNT(lr.id) as leave_count,
                    COUNT(a.id) as attendance_count
                FROM ems_employee e
                JOIN auth_user u ON e.user_id = u.id
                LEFT JOIN ems_department d ON e.department_id = d.id
                LEFT JOIN ems_leaverequest lr ON e.id = lr.employee_id
                LEFT JOIN ems_attendance a ON e.id = a.employee_id
                WHERE e.is_active = true
                GROUP BY e.id, u.username, d.name
                LIMIT 100
            """)
            
            results = cursor.fetchall()
        
        end_time = time.time()
        query_count = len(connection.queries)
        
        self.results['complex_join'] = {
            'time': end_time - start_time,
            'queries': query_count,
            'records': len(results)
        }
        
        print(f"  📊 Complex join: {query_count} queries, {(end_time - start_time)*1000:.2f}ms")
        
    def print_summary(self):
        """Print performance test summary"""
        print("\n" + "=" * 60)
        print("📈 PERFORMANCE TEST SUMMARY")
        print("=" * 60)
        
        total_time = sum(result['time'] for result in self.results.values())
        total_queries = sum(result['queries'] for result in self.results.values())
        
        print(f"\n🎯 Overall Performance:")
        print(f"  • Total execution time: {total_time*1000:.2f}ms")
        print(f"  • Total queries executed: {total_queries}")
        print(f"  • Average time per query: {(total_time/total_queries)*1000:.2f}ms")
        
        print(f"\n📊 Detailed Results:")
        for test_name, result in self.results.items():
            time_ms = result['time'] * 1000
            queries_per_record = result['queries'] / max(result['records'], 1)
            
            # Performance rating
            if time_ms < 50:
                rating = "🟢 EXCELLENT"
            elif time_ms < 200:
                rating = "🟡 GOOD"
            elif time_ms < 500:
                rating = "🟠 FAIR"
            else:
                rating = "🔴 POOR"
            
            print(f"  • {test_name}: {time_ms:.2f}ms, {result['queries']} queries, "
                  f"{queries_per_record:.1f} queries/record {rating}")
        
        # Performance recommendations
        print(f"\n💡 Recommendations:")
        slow_tests = [name for name, result in self.results.items() 
                     if result['time'] * 1000 > 200]
        
        if slow_tests:
            print(f"  • Optimize slow queries: {', '.join(slow_tests)}")
        
        high_query_tests = [name for name, result in self.results.items() 
                           if result['queries'] > result['records']]
        
        if high_query_tests:
            print(f"  • Check for N+1 queries: {', '.join(high_query_tests)}")
        
        if not slow_tests and not high_query_tests:
            print(f"  • 🎉 All queries are well optimized!")

if __name__ == '__main__':
    tester = PerformanceTester()
    tester.run_all_tests()
