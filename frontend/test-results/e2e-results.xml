<testsuites id="" name="" tests="285" failures="9" skipped="276" errors="0" time="71.586601">
<testsuite name="advanced-features.e2e.test.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="chromium" tests="18" failures="9" skipped="9" time="256.145" errors="0">
<testcase name="Advanced Features E2E Tests › Global Search › should open search with keyboard shortcut" classname="advanced-features.e2e.test.ts" time="10.061">
<failure message="advanced-features.e2e.test.ts:13:5 should open search with keyboard shortcut" type="FAILURE">
<![CDATA[  [chromium] › advanced-features.e2e.test.ts:13:5 › Advanced Features E2E Tests › Global Search › should open search with keyboard shortcut 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="global-search"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - expect.toBeVisible with timeout 5000ms
      - waiting for locator('[data-testid="global-search"]')


      16 |       
      17 |       // Verify search dialog is open
    > 18 |       await expect(page.locator('[data-testid="global-search"]')).toBeVisible()
         |                                                                   ^
      19 |       
      20 |       // Verify search input is focused
      21 |       await expect(page.locator('[data-testid="search-input"]')).toBeFocused()
        at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:18:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-3bbb1-arch-with-keyboard-shortcut-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-3bbb1-arch-with-keyboard-shortcut-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/advanced-features.e2e-Adva-3bbb1-arch-with-keyboard-shortcut-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|advanced-features.e2e-Adva-3bbb1-arch-with-keyboard-shortcut-chromium/test-failed-1.png]]

[[ATTACHMENT|advanced-features.e2e-Adva-3bbb1-arch-with-keyboard-shortcut-chromium/video.webm]]

[[ATTACHMENT|advanced-features.e2e-Adva-3bbb1-arch-with-keyboard-shortcut-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Advanced Features E2E Tests › Global Search › should perform search and show results" classname="advanced-features.e2e.test.ts" time="31.648">
<failure message="advanced-features.e2e.test.ts:24:5 should perform search and show results" type="FAILURE">
<![CDATA[  [chromium] › advanced-features.e2e.test.ts:24:5 › Advanced Features E2E Tests › Global Search › should perform search and show results 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="search-input"]')


      27 |       
      28 |       // Type search query
    > 29 |       await page.fill('[data-testid="search-input"]', 'employee')
         |                  ^
      30 |       
      31 |       // Wait for search results
      32 |       await page.waitForSelector('[data-testid="search-results"]')
        at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:29:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-d42e3-orm-search-and-show-results-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-d42e3-orm-search-and-show-results-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/advanced-features.e2e-Adva-d42e3-orm-search-and-show-results-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|advanced-features.e2e-Adva-d42e3-orm-search-and-show-results-chromium/test-failed-1.png]]

[[ATTACHMENT|advanced-features.e2e-Adva-d42e3-orm-search-and-show-results-chromium/video.webm]]

[[ATTACHMENT|advanced-features.e2e-Adva-d42e3-orm-search-and-show-results-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Advanced Features E2E Tests › Global Search › should show search suggestions" classname="advanced-features.e2e.test.ts" time="31.707">
<failure message="advanced-features.e2e.test.ts:38:5 should show search suggestions" type="FAILURE">
<![CDATA[  [chromium] › advanced-features.e2e.test.ts:38:5 › Advanced Features E2E Tests › Global Search › should show search suggestions 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="search-input"]')


      41 |       
      42 |       // Type partial query
    > 43 |       await page.fill('[data-testid="search-input"]', 'emp')
         |                  ^
      44 |       
      45 |       // Wait for suggestions
      46 |       await page.waitForSelector('[data-testid="search-suggestions"]')
        at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:43:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-813f7-uld-show-search-suggestions-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-813f7-uld-show-search-suggestions-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/advanced-features.e2e-Adva-813f7-uld-show-search-suggestions-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|advanced-features.e2e-Adva-813f7-uld-show-search-suggestions-chromium/test-failed-1.png]]

[[ATTACHMENT|advanced-features.e2e-Adva-813f7-uld-show-search-suggestions-chromium/video.webm]]

[[ATTACHMENT|advanced-features.e2e-Adva-813f7-uld-show-search-suggestions-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Advanced Features E2E Tests › Global Search › should close search with Escape key" classname="advanced-features.e2e.test.ts" time="10.054">
<failure message="advanced-features.e2e.test.ts:52:5 should close search with Escape key" type="FAILURE">
<![CDATA[  [chromium] › advanced-features.e2e.test.ts:52:5 › Advanced Features E2E Tests › Global Search › should close search with Escape key 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="global-search"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - expect.toBeVisible with timeout 5000ms
      - waiting for locator('[data-testid="global-search"]')


      55 |       
      56 |       // Verify search is open
    > 57 |       await expect(page.locator('[data-testid="global-search"]')).toBeVisible()
         |                                                                   ^
      58 |       
      59 |       // Press Escape to close
      60 |       await page.keyboard.press('Escape')
        at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:57:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-da298-lose-search-with-Escape-key-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-da298-lose-search-with-Escape-key-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/advanced-features.e2e-Adva-da298-lose-search-with-Escape-key-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|advanced-features.e2e-Adva-da298-lose-search-with-Escape-key-chromium/test-failed-1.png]]

[[ATTACHMENT|advanced-features.e2e-Adva-da298-lose-search-with-Escape-key-chromium/video.webm]]

[[ATTACHMENT|advanced-features.e2e-Adva-da298-lose-search-with-Escape-key-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should open export dialog with keyboard shortcut" classname="advanced-features.e2e.test.ts" time="7.461">
<failure message="advanced-features.e2e.test.ts:68:5 should open export dialog with keyboard shortcut" type="FAILURE">
<![CDATA[  [chromium] › advanced-features.e2e.test.ts:68:5 › Advanced Features E2E Tests › Export Functionality › should open export dialog with keyboard shortcut 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="export-dialog"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - expect.toBeVisible with timeout 5000ms
      - waiting for locator('[data-testid="export-dialog"]')


      71 |       
      72 |       // Verify export dialog is open
    > 73 |       await expect(page.locator('[data-testid="export-dialog"]')).toBeVisible()
         |                                                                   ^
      74 |     })
      75 |
      76 |     test('should select export format', async ({ page }) => {
        at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:73:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-7273d-alog-with-keyboard-shortcut-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-7273d-alog-with-keyboard-shortcut-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/advanced-features.e2e-Adva-7273d-alog-with-keyboard-shortcut-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|advanced-features.e2e-Adva-7273d-alog-with-keyboard-shortcut-chromium/test-failed-1.png]]

[[ATTACHMENT|advanced-features.e2e-Adva-7273d-alog-with-keyboard-shortcut-chromium/video.webm]]

[[ATTACHMENT|advanced-features.e2e-Adva-7273d-alog-with-keyboard-shortcut-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should select export format" classname="advanced-features.e2e.test.ts" time="31.538">
<failure message="advanced-features.e2e.test.ts:76:5 should select export format" type="FAILURE">
<![CDATA[  [chromium] › advanced-features.e2e.test.ts:76:5 › Advanced Features E2E Tests › Export Functionality › should select export format 

    Test timeout of 30000ms exceeded.

    Error: page.click: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="format-pdf"]')


      79 |       
      80 |       // Select PDF format
    > 81 |       await page.click('[data-testid="format-pdf"]')
         |                  ^
      82 |       
      83 |       // Verify PDF format is selected
      84 |       await expect(page.locator('[data-testid="format-pdf"]')).toHaveClass(/selected|active/)
        at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:81:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-68485-should-select-export-format-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-68485-should-select-export-format-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/advanced-features.e2e-Adva-68485-should-select-export-format-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|advanced-features.e2e-Adva-68485-should-select-export-format-chromium/test-failed-1.png]]

[[ATTACHMENT|advanced-features.e2e-Adva-68485-should-select-export-format-chromium/video.webm]]

[[ATTACHMENT|advanced-features.e2e-Adva-68485-should-select-export-format-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should configure export columns" classname="advanced-features.e2e.test.ts" time="31.454">
<failure message="advanced-features.e2e.test.ts:87:5 should configure export columns" type="FAILURE">
<![CDATA[  [chromium] › advanced-features.e2e.test.ts:87:5 › Advanced Features E2E Tests › Export Functionality › should configure export columns 

    Test timeout of 30000ms exceeded.

    Error: page.uncheck: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="column-checkbox"]:first-child')


      90 |       
      91 |       // Uncheck a column
    > 92 |       await page.uncheck('[data-testid="column-checkbox"]:first-child')
         |                  ^
      93 |       
      94 |       // Verify column is unchecked
      95 |       await expect(page.locator('[data-testid="column-checkbox"]:first-child')).not.toBeChecked()
        at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:92:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-8238e-ld-configure-export-columns-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-8238e-ld-configure-export-columns-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/advanced-features.e2e-Adva-8238e-ld-configure-export-columns-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|advanced-features.e2e-Adva-8238e-ld-configure-export-columns-chromium/test-failed-1.png]]

[[ATTACHMENT|advanced-features.e2e-Adva-8238e-ld-configure-export-columns-chromium/video.webm]]

[[ATTACHMENT|advanced-features.e2e-Adva-8238e-ld-configure-export-columns-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should start export process" classname="advanced-features.e2e.test.ts" time="31.48">
<failure message="advanced-features.e2e.test.ts:98:5 should start export process" type="FAILURE">
<![CDATA[  [chromium] › advanced-features.e2e.test.ts:98:5 › Advanced Features E2E Tests › Export Functionality › should start export process 

    Test timeout of 30000ms exceeded.

    Error: page.click: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="export-button"]')


      101 |       
      102 |       // Click export button
    > 103 |       await page.click('[data-testid="export-button"]')
          |                  ^
      104 |       
      105 |       // Verify export progress is shown
      106 |       await expect(page.locator('[data-testid="export-progress"]')).toBeVisible()
        at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:103:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-eda9e-should-start-export-process-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-eda9e-should-start-export-process-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/advanced-features.e2e-Adva-eda9e-should-start-export-process-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|advanced-features.e2e-Adva-eda9e-should-start-export-process-chromium/test-failed-1.png]]

[[ATTACHMENT|advanced-features.e2e-Adva-eda9e-should-start-export-process-chromium/video.webm]]

[[ATTACHMENT|advanced-features.e2e-Adva-eda9e-should-start-export-process-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should open notifications with keyboard shortcut" classname="advanced-features.e2e.test.ts" time="7.342">
<failure message="advanced-features.e2e.test.ts:111:5 should open notifications with keyboard shortcut" type="FAILURE">
<![CDATA[  [chromium] › advanced-features.e2e.test.ts:111:5 › Advanced Features E2E Tests › Notification Center › should open notifications with keyboard shortcut 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="notification-center"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - expect.toBeVisible with timeout 5000ms
      - waiting for locator('[data-testid="notification-center"]')


      114 |       
      115 |       // Verify notification center is open
    > 116 |       await expect(page.locator('[data-testid="notification-center"]')).toBeVisible()
          |                                                                         ^
      117 |     })
      118 |
      119 |     test('should filter notifications', async ({ page }) => {
        at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:116:73

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-fd2d4-ions-with-keyboard-shortcut-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/advanced-features.e2e-Adva-fd2d4-ions-with-keyboard-shortcut-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/advanced-features.e2e-Adva-fd2d4-ions-with-keyboard-shortcut-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|advanced-features.e2e-Adva-fd2d4-ions-with-keyboard-shortcut-chromium/test-failed-1.png]]

[[ATTACHMENT|advanced-features.e2e-Adva-fd2d4-ions-with-keyboard-shortcut-chromium/video.webm]]

[[ATTACHMENT|advanced-features.e2e-Adva-fd2d4-ions-with-keyboard-shortcut-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should filter notifications" classname="advanced-features.e2e.test.ts" time="26.579">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should mark notification as read" classname="advanced-features.e2e.test.ts" time="22.527">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should search notifications" classname="advanced-features.e2e.test.ts" time="13.294">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Responsive Design › should work on mobile devices" classname="advanced-features.e2e.test.ts" time="1">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Responsive Design › should work on tablet devices" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Accessibility › should be keyboard navigable" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Accessibility › should have proper ARIA labels" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Performance › should load within acceptable time" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Performance › should handle large datasets efficiently" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="api.spec.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="chromium" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="API Integration Tests › should authenticate and get user profile" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should get dashboard stats" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should get departments list" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should get employees list" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should handle unauthorized requests" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should handle invalid endpoints" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should validate CORS headers" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should refresh authentication token" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should handle API rate limiting gracefully" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should validate API response structure" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth.spec.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="chromium" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Authentication Flow › should display login page for unauthenticated users" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show validation errors for empty login form" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show error for invalid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should successfully login with valid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should maintain session after page refresh" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should logout successfully" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="dashboard.spec.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="chromium" tests="7" failures="0" skipped="7" time="0" errors="0">
<testcase name="Dashboard Functionality › should display dashboard with key metrics" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should display employee statistics" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should have working navigation menu" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should display charts and visualizations" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should be responsive on mobile viewport" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should handle Arabic/English language toggle" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should load without JavaScript errors" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="visual-regression.e2e.test.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="chromium" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Visual Regression Tests › Component Screenshots › should match global search component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Component Screenshots › should match export dialog component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Component Screenshots › should match notification center component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › State Screenshots › should match search with results" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › State Screenshots › should match search with suggestions" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › State Screenshots › should match export with progress" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Responsive Screenshots › should match mobile layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Responsive Screenshots › should match tablet layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Responsive Screenshots › should match desktop layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Theme Screenshots › should match light theme" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Theme Screenshots › should match dark theme" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › RTL Screenshots › should match Arabic RTL layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › RTL Screenshots › should match RTL search component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Error State Screenshots › should match search error state" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Error State Screenshots › should match export error state" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Loading State Screenshots › should match search loading state" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="advanced-features.e2e.test.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="firefox" tests="18" failures="0" skipped="18" time="0" errors="0">
<testcase name="Advanced Features E2E Tests › Global Search › should open search with keyboard shortcut" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Global Search › should perform search and show results" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Global Search › should show search suggestions" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Global Search › should close search with Escape key" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should open export dialog with keyboard shortcut" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should select export format" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should configure export columns" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should start export process" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should open notifications with keyboard shortcut" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should filter notifications" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should mark notification as read" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should search notifications" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Responsive Design › should work on mobile devices" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Responsive Design › should work on tablet devices" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Accessibility › should be keyboard navigable" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Accessibility › should have proper ARIA labels" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Performance › should load within acceptable time" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Performance › should handle large datasets efficiently" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="api.spec.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="firefox" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="API Integration Tests › should authenticate and get user profile" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should get dashboard stats" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should get departments list" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should get employees list" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should handle unauthorized requests" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should handle invalid endpoints" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should validate CORS headers" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should refresh authentication token" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should handle API rate limiting gracefully" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should validate API response structure" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth.spec.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="firefox" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Authentication Flow › should display login page for unauthenticated users" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show validation errors for empty login form" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show error for invalid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should successfully login with valid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should maintain session after page refresh" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should logout successfully" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="dashboard.spec.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="firefox" tests="7" failures="0" skipped="7" time="0" errors="0">
<testcase name="Dashboard Functionality › should display dashboard with key metrics" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should display employee statistics" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should have working navigation menu" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should display charts and visualizations" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should be responsive on mobile viewport" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should handle Arabic/English language toggle" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should load without JavaScript errors" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="visual-regression.e2e.test.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="firefox" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Visual Regression Tests › Component Screenshots › should match global search component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Component Screenshots › should match export dialog component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Component Screenshots › should match notification center component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › State Screenshots › should match search with results" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › State Screenshots › should match search with suggestions" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › State Screenshots › should match export with progress" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Responsive Screenshots › should match mobile layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Responsive Screenshots › should match tablet layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Responsive Screenshots › should match desktop layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Theme Screenshots › should match light theme" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Theme Screenshots › should match dark theme" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › RTL Screenshots › should match Arabic RTL layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › RTL Screenshots › should match RTL search component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Error State Screenshots › should match search error state" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Error State Screenshots › should match export error state" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Loading State Screenshots › should match search loading state" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="advanced-features.e2e.test.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="webkit" tests="18" failures="0" skipped="18" time="0" errors="0">
<testcase name="Advanced Features E2E Tests › Global Search › should open search with keyboard shortcut" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Global Search › should perform search and show results" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Global Search › should show search suggestions" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Global Search › should close search with Escape key" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should open export dialog with keyboard shortcut" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should select export format" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should configure export columns" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should start export process" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should open notifications with keyboard shortcut" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should filter notifications" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should mark notification as read" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should search notifications" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Responsive Design › should work on mobile devices" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Responsive Design › should work on tablet devices" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Accessibility › should be keyboard navigable" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Accessibility › should have proper ARIA labels" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Performance › should load within acceptable time" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Performance › should handle large datasets efficiently" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="api.spec.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="webkit" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="API Integration Tests › should authenticate and get user profile" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should get dashboard stats" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should get departments list" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should get employees list" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should handle unauthorized requests" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should handle invalid endpoints" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should validate CORS headers" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should refresh authentication token" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should handle API rate limiting gracefully" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should validate API response structure" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth.spec.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="webkit" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Authentication Flow › should display login page for unauthenticated users" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show validation errors for empty login form" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show error for invalid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should successfully login with valid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should maintain session after page refresh" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should logout successfully" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="dashboard.spec.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="webkit" tests="7" failures="0" skipped="7" time="0" errors="0">
<testcase name="Dashboard Functionality › should display dashboard with key metrics" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should display employee statistics" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should have working navigation menu" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should display charts and visualizations" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should be responsive on mobile viewport" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should handle Arabic/English language toggle" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should load without JavaScript errors" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="visual-regression.e2e.test.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="webkit" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Visual Regression Tests › Component Screenshots › should match global search component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Component Screenshots › should match export dialog component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Component Screenshots › should match notification center component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › State Screenshots › should match search with results" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › State Screenshots › should match search with suggestions" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › State Screenshots › should match export with progress" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Responsive Screenshots › should match mobile layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Responsive Screenshots › should match tablet layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Responsive Screenshots › should match desktop layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Theme Screenshots › should match light theme" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Theme Screenshots › should match dark theme" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › RTL Screenshots › should match Arabic RTL layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › RTL Screenshots › should match RTL search component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Error State Screenshots › should match search error state" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Error State Screenshots › should match export error state" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Loading State Screenshots › should match search loading state" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="advanced-features.e2e.test.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="Mobile Chrome" tests="18" failures="0" skipped="18" time="0" errors="0">
<testcase name="Advanced Features E2E Tests › Global Search › should open search with keyboard shortcut" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Global Search › should perform search and show results" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Global Search › should show search suggestions" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Global Search › should close search with Escape key" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should open export dialog with keyboard shortcut" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should select export format" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should configure export columns" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should start export process" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should open notifications with keyboard shortcut" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should filter notifications" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should mark notification as read" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should search notifications" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Responsive Design › should work on mobile devices" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Responsive Design › should work on tablet devices" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Accessibility › should be keyboard navigable" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Accessibility › should have proper ARIA labels" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Performance › should load within acceptable time" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Performance › should handle large datasets efficiently" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="api.spec.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="Mobile Chrome" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="API Integration Tests › should authenticate and get user profile" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should get dashboard stats" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should get departments list" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should get employees list" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should handle unauthorized requests" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should handle invalid endpoints" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should validate CORS headers" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should refresh authentication token" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should handle API rate limiting gracefully" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should validate API response structure" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth.spec.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="Mobile Chrome" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Authentication Flow › should display login page for unauthenticated users" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show validation errors for empty login form" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show error for invalid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should successfully login with valid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should maintain session after page refresh" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should logout successfully" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="dashboard.spec.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="Mobile Chrome" tests="7" failures="0" skipped="7" time="0" errors="0">
<testcase name="Dashboard Functionality › should display dashboard with key metrics" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should display employee statistics" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should have working navigation menu" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should display charts and visualizations" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should be responsive on mobile viewport" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should handle Arabic/English language toggle" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should load without JavaScript errors" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="visual-regression.e2e.test.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="Mobile Chrome" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Visual Regression Tests › Component Screenshots › should match global search component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Component Screenshots › should match export dialog component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Component Screenshots › should match notification center component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › State Screenshots › should match search with results" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › State Screenshots › should match search with suggestions" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › State Screenshots › should match export with progress" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Responsive Screenshots › should match mobile layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Responsive Screenshots › should match tablet layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Responsive Screenshots › should match desktop layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Theme Screenshots › should match light theme" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Theme Screenshots › should match dark theme" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › RTL Screenshots › should match Arabic RTL layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › RTL Screenshots › should match RTL search component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Error State Screenshots › should match search error state" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Error State Screenshots › should match export error state" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Loading State Screenshots › should match search loading state" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="advanced-features.e2e.test.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="Mobile Safari" tests="18" failures="0" skipped="18" time="0" errors="0">
<testcase name="Advanced Features E2E Tests › Global Search › should open search with keyboard shortcut" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Global Search › should perform search and show results" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Global Search › should show search suggestions" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Global Search › should close search with Escape key" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should open export dialog with keyboard shortcut" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should select export format" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should configure export columns" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Export Functionality › should start export process" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should open notifications with keyboard shortcut" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should filter notifications" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should mark notification as read" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Notification Center › should search notifications" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Responsive Design › should work on mobile devices" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Responsive Design › should work on tablet devices" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Accessibility › should be keyboard navigable" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Accessibility › should have proper ARIA labels" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Performance › should load within acceptable time" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Advanced Features E2E Tests › Performance › should handle large datasets efficiently" classname="advanced-features.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="api.spec.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="Mobile Safari" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="API Integration Tests › should authenticate and get user profile" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should get dashboard stats" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should get departments list" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should get employees list" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should handle unauthorized requests" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should handle invalid endpoints" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should validate CORS headers" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should refresh authentication token" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should handle API rate limiting gracefully" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="API Integration Tests › should validate API response structure" classname="api.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth.spec.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="Mobile Safari" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Authentication Flow › should display login page for unauthenticated users" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show validation errors for empty login form" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should show error for invalid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should successfully login with valid credentials" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should maintain session after page refresh" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Authentication Flow › should logout successfully" classname="auth.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="dashboard.spec.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="Mobile Safari" tests="7" failures="0" skipped="7" time="0" errors="0">
<testcase name="Dashboard Functionality › should display dashboard with key metrics" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should display employee statistics" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should have working navigation menu" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should display charts and visualizations" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should be responsive on mobile viewport" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should handle Arabic/English language toggle" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Dashboard Functionality › should load without JavaScript errors" classname="dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="visual-regression.e2e.test.ts" timestamp="2025-07-18T11:27:40.673Z" hostname="Mobile Safari" tests="16" failures="0" skipped="16" time="0" errors="0">
<testcase name="Visual Regression Tests › Component Screenshots › should match global search component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Component Screenshots › should match export dialog component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Component Screenshots › should match notification center component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › State Screenshots › should match search with results" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › State Screenshots › should match search with suggestions" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › State Screenshots › should match export with progress" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Responsive Screenshots › should match mobile layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Responsive Screenshots › should match tablet layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Responsive Screenshots › should match desktop layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Theme Screenshots › should match light theme" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Theme Screenshots › should match dark theme" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › RTL Screenshots › should match Arabic RTL layout" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › RTL Screenshots › should match RTL search component" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Error State Screenshots › should match search error state" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Error State Screenshots › should match export error state" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Visual Regression Tests › Loading State Screenshots › should match search loading state" classname="visual-regression.e2e.test.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>