/**
 * MOBILE FIX: Mobile-Responsive UI Components
 * Provides components optimized for mobile devices
 */

import React, { useState, useEffect, useRef } from 'react'
import { cn } from '@/lib/utils'
import { useMobileOptimization } from '@/utils/mobileOptimization'
import { ChevronDown, ChevronUp, X, Menu } from 'lucide-react'

// MOBILE FIX: Responsive container with mobile optimizations
export interface ResponsiveContainerProps {
  children: React.ReactNode
  className?: string
  mobileClassName?: string
  tabletClassName?: string
  desktopClassName?: string
  padding?: 'none' | 'sm' | 'md' | 'lg'
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
}

export function ResponsiveContainer({
  children,
  className = '',
  mobileClassName = '',
  tabletClassName = '',
  desktopClassName = '',
  padding = 'md',
  maxWidth = 'full'
}: ResponsiveContainerProps) {
  const { isMobile, isTablet, isDesktop } = useMobileOptimization()

  const paddingClasses = {
    none: '',
    sm: 'p-2 sm:p-3',
    md: 'p-4 sm:p-6',
    lg: 'p-6 sm:p-8'
  }

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full'
  }

  const deviceSpecificClass = isMobile ? mobileClassName : isTablet ? tabletClassName : desktopClassName

  return (
    <div className={cn(
      'w-full mx-auto',
      maxWidthClasses[maxWidth],
      paddingClasses[padding],
      className,
      deviceSpecificClass
    )}>
      {children}
    </div>
  )
}

// MOBILE FIX: Mobile-optimized modal/drawer
export interface MobileDrawerProps {
  isOpen: boolean
  onClose: () => void
  children: React.ReactNode
  title?: string
  position?: 'bottom' | 'top' | 'left' | 'right'
  className?: string
}

export function MobileDrawer({
  isOpen,
  onClose,
  children,
  title,
  position = 'bottom',
  className = ''
}: MobileDrawerProps) {
  const { isMobile } = useMobileOptimization()
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (isOpen) {
      setIsAnimating(true)
      // Prevent body scroll when drawer is open
      document.body.style.overflow = 'hidden'
    } else {
      // Re-enable body scroll when drawer is closed
      document.body.style.overflow = ''
    }

    return () => {
      document.body.style.overflow = ''
    }
  }, [isOpen])

  const positionClasses = {
    bottom: 'bottom-0 left-0 right-0 rounded-t-xl translate-y-full',
    top: 'top-0 left-0 right-0 rounded-b-xl -translate-y-full',
    left: 'left-0 top-0 bottom-0 rounded-r-xl -translate-x-full',
    right: 'right-0 top-0 bottom-0 rounded-l-xl translate-x-full'
  }

  const openClasses = {
    bottom: 'translate-y-0',
    top: 'translate-y-0',
    left: 'translate-x-0',
    right: 'translate-x-0'
  }

  if (!isOpen && !isAnimating) return null

  return (
    <>
      {/* Backdrop */}
      <div
        className={cn(
          'fixed inset-0 bg-black/50 backdrop-blur-sm z-40 transition-opacity duration-300',
          isOpen ? 'opacity-100' : 'opacity-0'
        )}
        onClick={onClose}
      />

      {/* Drawer */}
      <div
        className={cn(
          'fixed z-50 bg-white dark:bg-gray-900 shadow-xl transition-transform duration-300 ease-out',
          positionClasses[position],
          isOpen ? openClasses[position] : '',
          isMobile ? 'max-h-[90vh]' : 'max-h-[80vh]',
          className
        )}
        onTransitionEnd={() => {
          if (!isOpen) setIsAnimating(false)
        }}
      >
        {/* Header */}
        {title && (
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              {title}
            </h2>
            <button
              onClick={onClose}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        )}

        {/* Content */}
        <div className="overflow-y-auto flex-1">
          {children}
        </div>
      </div>
    </>
  )
}

// MOBILE FIX: Touch-optimized button
export interface TouchButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  children: React.ReactNode
}

export function TouchButton({
  variant = 'primary',
  size = 'md',
  className = '',
  children,
  ...props
}: TouchButtonProps) {
  const { getTouchButtonSize, isTouchDevice } = useMobileOptimization()
  const touchSize = getTouchButtonSize()

  const variantClasses = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white',
    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-900 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-white',
    ghost: 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300'
  }

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-6 py-4 text-lg'
  }

  return (
    <button
      className={cn(
        'rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
        'active:scale-95 transition-transform duration-100', // Touch feedback
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      style={{
        minWidth: isTouchDevice ? touchSize.minWidth : undefined,
        minHeight: isTouchDevice ? touchSize.minHeight : undefined
      }}
      {...props}
    >
      {children}
    </button>
  )
}

// MOBILE FIX: Collapsible section for mobile
export interface CollapsibleSectionProps {
  title: string
  children: React.ReactNode
  defaultOpen?: boolean
  className?: string
}

export function CollapsibleSection({
  title,
  children,
  defaultOpen = false,
  className = ''
}: CollapsibleSectionProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen)
  const { isMobile } = useMobileOptimization()

  return (
    <div className={cn('border border-gray-200 dark:border-gray-700 rounded-lg', className)}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors',
          isMobile ? 'min-h-[44px]' : ''
        )}
      >
        <span className="font-medium text-gray-900 dark:text-white">{title}</span>
        {isOpen ? (
          <ChevronUp className="h-5 w-5 text-gray-500" />
        ) : (
          <ChevronDown className="h-5 w-5 text-gray-500" />
        )}
      </button>
      
      {isOpen && (
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          {children}
        </div>
      )}
    </div>
  )
}

// MOBILE FIX: Mobile-optimized table
export interface MobileTableProps {
  headers: string[]
  data: Array<Record<string, any>>
  renderCell: (value: any, key: string, row: Record<string, any>) => React.ReactNode
  className?: string
}

export function MobileTable({
  headers,
  data,
  renderCell,
  className = ''
}: MobileTableProps) {
  const { isMobile } = useMobileOptimization()

  if (isMobile) {
    // Mobile card layout
    return (
      <div className={cn('space-y-4', className)}>
        {data.map((row, index) => (
          <div key={index} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
            {headers.map((header, headerIndex) => {
              const key = Object.keys(row)[headerIndex]
              const value = row[key]
              return (
                <div key={header} className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                  <span className="font-medium text-gray-600 dark:text-gray-400 text-sm">
                    {header}
                  </span>
                  <span className="text-gray-900 dark:text-white">
                    {renderCell(value, key, row)}
                  </span>
                </div>
              )
            })}
          </div>
        ))}
      </div>
    )
  }

  // Desktop table layout
  return (
    <div className={cn('overflow-x-auto', className)}>
      <table className="min-w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
        <thead className="bg-gray-50 dark:bg-gray-700">
          <tr>
            {headers.map((header, index) => (
              <th key={index} className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                {header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
          {data.map((row, index) => (
            <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
              {headers.map((header, headerIndex) => {
                const key = Object.keys(row)[headerIndex]
                const value = row[key]
                return (
                  <td key={header} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {renderCell(value, key, row)}
                  </td>
                )
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

// MOBILE FIX: Swipe gesture handler
export interface SwipeGestureProps {
  children: React.ReactNode
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  threshold?: number
  className?: string
}

export function SwipeGesture({
  children,
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  threshold = 50,
  className = ''
}: SwipeGestureProps) {
  const { isTouchDevice } = useMobileOptimization()
  const [touchStart, setTouchStart] = useState<{ x: number; y: number } | null>(null)
  const [touchEnd, setTouchEnd] = useState<{ x: number; y: number } | null>(null)

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null)
    setTouchStart({
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY
    })
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd({
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY
    })
  }

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distanceX = touchStart.x - touchEnd.x
    const distanceY = touchStart.y - touchEnd.y
    const isLeftSwipe = distanceX > threshold
    const isRightSwipe = distanceX < -threshold
    const isUpSwipe = distanceY > threshold
    const isDownSwipe = distanceY < -threshold

    if (Math.abs(distanceX) > Math.abs(distanceY)) {
      // Horizontal swipe
      if (isLeftSwipe && onSwipeLeft) {
        onSwipeLeft()
      } else if (isRightSwipe && onSwipeRight) {
        onSwipeRight()
      }
    } else {
      // Vertical swipe
      if (isUpSwipe && onSwipeUp) {
        onSwipeUp()
      } else if (isDownSwipe && onSwipeDown) {
        onSwipeDown()
      }
    }
  }

  if (!isTouchDevice) {
    return <div className={className}>{children}</div>
  }

  return (
    <div
      className={className}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {children}
    </div>
  )
}

export default {
  ResponsiveContainer,
  MobileDrawer,
  TouchButton,
  CollapsibleSection,
  MobileTable,
  SwipeGesture
}
