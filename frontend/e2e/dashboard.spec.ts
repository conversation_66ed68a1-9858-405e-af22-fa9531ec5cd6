import { test, expect } from '@playwright/test';

test.describe('Dashboard Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/');
    
    // Handle potential redirect to login
    const currentUrl = page.url();
    if (currentUrl.includes('login') || !currentUrl.includes('dashboard')) {
      await page.fill('input[type="text"], input[type="email"]', 'admin');
      await page.fill('input[type="password"]', 'admin123');
      await page.click('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")');
      
      // Wait for successful login
      await expect(page).toHaveURL(/.*(?:dashboard|home|main).*/);
    }
  });

  test('should display dashboard with key metrics', async ({ page }) => {
    // Check for dashboard elements
    await expect(page.locator('text=/dashboard|لوحة التحكم/i')).toBeVisible();
    
    // Look for common dashboard widgets/cards
    await expect(page.locator('[data-testid*="card"], .card, [class*="card"]')).toHaveCount({ min: 1 });
    
    // Check for navigation menu
    await expect(page.locator('nav, [role="navigation"], .navigation, .sidebar')).toBeVisible();
  });

  test('should display employee statistics', async ({ page }) => {
    // Look for employee-related metrics
    await expect(page.locator('text=/employee|موظف|staff|عدد الموظفين/i')).toBeVisible();
    
    // Check for numerical data
    await expect(page.locator('text=/\\d+/')).toHaveCount({ min: 1 });
  });

  test('should have working navigation menu', async ({ page }) => {
    // Test navigation to different sections
    const navigationItems = [
      { text: /employee|موظف/i, url: /employee/ },
      { text: /department|قسم/i, url: /department/ },
      { text: /report|تقرير/i, url: /report/ },
      { text: /setting|إعداد/i, url: /setting/ }
    ];

    for (const item of navigationItems) {
      const navLink = page.locator(`a:has-text("${item.text.source}"), button:has-text("${item.text.source}")`).first();
      
      if (await navLink.isVisible()) {
        await navLink.click();
        await page.waitForTimeout(1000); // Wait for navigation
        
        // Check if URL changed appropriately
        const currentUrl = page.url();
        console.log(`Navigated to: ${currentUrl}`);
        
        // Go back to dashboard for next test
        await page.goto('/dashboard');
        await page.waitForTimeout(500);
      }
    }
  });

  test('should display charts and visualizations', async ({ page }) => {
    // Look for chart containers
    const chartSelectors = [
      'canvas',
      '[data-testid*="chart"]',
      '.recharts-wrapper',
      '.chart-container',
      'svg'
    ];

    let chartsFound = false;
    for (const selector of chartSelectors) {
      const charts = page.locator(selector);
      if (await charts.count() > 0) {
        chartsFound = true;
        break;
      }
    }

    if (chartsFound) {
      console.log('Charts found on dashboard');
    } else {
      console.log('No charts found - this might be expected for a fresh installation');
    }
  });

  test('should be responsive on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Dashboard should still be accessible
    await expect(page.locator('text=/dashboard|لوحة التحكم/i')).toBeVisible();
    
    // Check for mobile navigation (hamburger menu, etc.)
    const mobileNavSelectors = [
      '[data-testid="mobile-menu"]',
      '.hamburger',
      'button[aria-label*="menu"]',
      'button:has-text("☰")'
    ];

    let mobileNavFound = false;
    for (const selector of mobileNavSelectors) {
      if (await page.locator(selector).isVisible()) {
        mobileNavFound = true;
        break;
      }
    }

    console.log(`Mobile navigation found: ${mobileNavFound}`);
  });

  test('should handle Arabic/English language toggle', async ({ page }) => {
    // Look for language toggle button
    const languageToggleSelectors = [
      'button:has-text("EN")',
      'button:has-text("AR")',
      'button:has-text("عربي")',
      'button:has-text("English")',
      '[data-testid="language-toggle"]'
    ];

    let toggleFound = false;
    for (const selector of languageToggleSelectors) {
      const toggle = page.locator(selector);
      if (await toggle.isVisible()) {
        await toggle.click();
        await page.waitForTimeout(1000);
        toggleFound = true;
        break;
      }
    }

    if (toggleFound) {
      console.log('Language toggle functionality tested');
    } else {
      console.log('Language toggle not found - checking for RTL/LTR support');
      
      // Check if the page supports RTL
      const htmlDir = await page.getAttribute('html', 'dir');
      console.log(`HTML direction: ${htmlDir}`);
    }
  });

  test('should load without JavaScript errors', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });

    page.on('pageerror', error => {
      errors.push(error.message);
    });

    // Reload the page to catch any errors
    await page.reload();
    await page.waitForTimeout(3000);

    // Log errors but don't fail the test unless they're critical
    if (errors.length > 0) {
      console.log('JavaScript errors found:', errors);
      
      // Only fail for critical errors
      const criticalErrors = errors.filter(error => 
        error.includes('TypeError') || 
        error.includes('ReferenceError') ||
        error.includes('SyntaxError')
      );
      
      expect(criticalErrors).toHaveLength(0);
    }
  });
});
