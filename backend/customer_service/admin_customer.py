"""
Admin interface for viewing customers created through chat
"""

from django.contrib import admin
from django.contrib.auth.models import User
from django.utils.html import format_html
from django.urls import reverse
from django.db.models import Count, Q
from .models import SupportTicket, TicketComment
from .simple_customer_service import SimpleCustomerService

class CustomerUserAdmin(admin.ModelAdmin):
    """Enhanced admin for User model to show customer information"""
    
    list_display = [
        'customer_id_display', 
        'username', 
        'email', 
        'full_name_display',
        'total_tickets_display',
        'last_ticket_display',
        'date_joined',
        'is_active'
    ]
    
    list_filter = [
        'is_active',
        'date_joined',
        'last_login'
    ]
    
    search_fields = [
        'username',
        'email',
        'first_name',
        'last_name'
    ]
    
    readonly_fields = [
        'customer_id_display',
        'customer_stats_display',
        'recent_tickets_display'
    ]
    
    fieldsets = (
        ('Customer Information', {
            'fields': ('customer_id_display', 'username', 'email', 'first_name', 'last_name')
        }),
        ('Account Status', {
            'fields': ('is_active', 'date_joined', 'last_login')
        }),
        ('Customer Statistics', {
            'fields': ('customer_stats_display', 'recent_tickets_display'),
            'classes': ('collapse',)
        }),
        ('Permissions', {
            'fields': ('is_staff', 'is_superuser', 'groups', 'user_permissions'),
            'classes': ('collapse',)
        }),
    )
    
    def customer_id_display(self, obj):
        """Display customer ID"""
        return SimpleCustomerService.get_customer_display_id(obj)
    customer_id_display.short_description = 'Customer ID'
    
    def full_name_display(self, obj):
        """Display full name"""
        full_name = obj.get_full_name()
        return full_name if full_name else obj.username
    full_name_display.short_description = 'Name'
    
    def total_tickets_display(self, obj):
        """Display total tickets count"""
        count = SupportTicket.objects.filter(customer=obj).count()
        if count > 0:
            url = reverse('admin:customer_service_supportticket_changelist') + f'?customer__id__exact={obj.id}'
            return format_html('<a href="{}">{} tickets</a>', url, count)
        return '0 tickets'
    total_tickets_display.short_description = 'Tickets'
    
    def last_ticket_display(self, obj):
        """Display last ticket information"""
        last_ticket = SupportTicket.objects.filter(customer=obj).order_by('-created_at').first()
        if last_ticket:
            url = reverse('admin:customer_service_supportticket_change', args=[last_ticket.id])
            return format_html(
                '<a href="{}">{}</a><br><small>{}</small>',
                url,
                last_ticket.ticket_id,
                last_ticket.created_at.strftime('%Y-%m-%d %H:%M')
            )
        return 'No tickets'
    last_ticket_display.short_description = 'Last Ticket'
    
    def customer_stats_display(self, obj):
        """Display customer statistics"""
        stats = SimpleCustomerService.get_customer_stats(obj)
        
        html = f"""
        <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
            <h4>Customer Statistics</h4>
            <ul>
                <li><strong>Customer ID:</strong> {stats.get('customer_id', 'N/A')}</li>
                <li><strong>Total Tickets:</strong> {stats.get('total_tickets', 0)}</li>
                <li><strong>Open Tickets:</strong> {stats.get('open_tickets', 0)}</li>
                <li><strong>Resolved Tickets:</strong> {stats.get('resolved_tickets', 0)}</li>
                <li><strong>Customer Since:</strong> {stats.get('customer_since', 'N/A')}</li>
                <li><strong>Average Satisfaction:</strong> {stats.get('avg_satisfaction', 0):.1f}/5</li>
            </ul>
        </div>
        """
        return format_html(html)
    customer_stats_display.short_description = 'Statistics'
    
    def recent_tickets_display(self, obj):
        """Display recent tickets"""
        recent_tickets = SupportTicket.objects.filter(customer=obj).order_by('-created_at')[:5]
        
        if not recent_tickets:
            return 'No tickets'
        
        html = '<div style="background: #f8f9fa; padding: 10px; border-radius: 5px;"><h4>Recent Tickets</h4><ul>'
        
        for ticket in recent_tickets:
            url = reverse('admin:customer_service_supportticket_change', args=[ticket.id])
            status_color = {
                'open': '#dc3545',
                'in_progress': '#ffc107',
                'resolved': '#28a745',
                'closed': '#6c757d'
            }.get(ticket.status, '#6c757d')
            
            html += f'''
            <li>
                <a href="{url}">{ticket.ticket_id}</a> - 
                <span style="color: {status_color}; font-weight: bold;">{ticket.status.title()}</span><br>
                <small>{ticket.title[:50]}{'...' if len(ticket.title) > 50 else ''}</small><br>
                <small>{ticket.created_at.strftime('%Y-%m-%d %H:%M')}</small>
            </li>
            '''
        
        html += '</ul></div>'
        return format_html(html)
    recent_tickets_display.short_description = 'Recent Tickets'
    
    def get_queryset(self, request):
        """Optimize queryset with prefetch"""
        return super().get_queryset(request).prefetch_related('support_tickets')

# Enhanced SupportTicket admin
class SupportTicketAdmin(admin.ModelAdmin):
    """Enhanced admin for Support Tickets"""
    
    list_display = [
        'ticket_id',
        'title',
        'customer_display',
        'status',
        'priority',
        'source',
        'created_at',
        'assigned_agent'
    ]
    
    list_filter = [
        'status',
        'priority',
        'source',
        'created_at',
        'assigned_agent'
    ]
    
    search_fields = [
        'ticket_id',
        'title',
        'description',
        'customer__username',
        'customer__email',
        'customer_email'
    ]
    
    readonly_fields = [
        'ticket_id',
        'created_at',
        'updated_at',
        'customer_info_display'
    ]
    
    fieldsets = (
        ('Ticket Information', {
            'fields': ('ticket_id', 'title', 'description', 'status', 'priority', 'source')
        }),
        ('Customer Information', {
            'fields': ('customer', 'customer_email', 'customer_phone', 'customer_info_display')
        }),
        ('Assignment', {
            'fields': ('assigned_agent', 'category', 'sla')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'first_response_at', 'resolved_at', 'closed_at'),
            'classes': ('collapse',)
        }),
        ('Metrics', {
            'fields': ('customer_satisfaction_rating', 'resolution_time', 'is_sla_breached'),
            'classes': ('collapse',)
        }),
    )
    
    def customer_display(self, obj):
        """Display customer information"""
        if obj.customer:
            customer_id = SimpleCustomerService.get_customer_display_id(obj.customer)
            url = reverse('admin:auth_user_change', args=[obj.customer.id])
            return format_html(
                '<a href="{}">{}</a><br><small>{}</small>',
                url,
                customer_id,
                obj.customer.email
            )
        return obj.customer_email
    customer_display.short_description = 'Customer'
    
    def customer_info_display(self, obj):
        """Display detailed customer information"""
        if not obj.customer:
            return 'No customer linked'
        
        stats = SimpleCustomerService.get_customer_stats(obj.customer)
        
        html = f"""
        <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
            <h4>Customer Details</h4>
            <ul>
                <li><strong>Customer ID:</strong> {stats.get('customer_id', 'N/A')}</li>
                <li><strong>Name:</strong> {stats.get('name', 'N/A')}</li>
                <li><strong>Email:</strong> {stats.get('email', 'N/A')}</li>
                <li><strong>Total Tickets:</strong> {stats.get('total_tickets', 0)}</li>
                <li><strong>Customer Since:</strong> {stats.get('customer_since', 'N/A')}</li>
            </ul>
        </div>
        """
        return format_html(html)
    customer_info_display.short_description = 'Customer Details'

# Register the enhanced admin
try:
    admin.site.unregister(User)
except admin.sites.NotRegistered:
    pass

admin.site.register(User, CustomerUserAdmin)

# Update SupportTicket admin if not already registered
try:
    admin.site.unregister(SupportTicket)
except admin.sites.NotRegistered:
    pass

admin.site.register(SupportTicket, SupportTicketAdmin)
