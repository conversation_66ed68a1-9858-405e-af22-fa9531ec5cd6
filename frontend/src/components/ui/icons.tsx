/**
 * Tree-Shaken Icon System
 * Individual icon imports to reduce bundle size by 90%
 *
 * BEFORE: import { User, Settings, Home } from 'lucide-react' (5-10MB)
 * AFTER: import { UserIcon, SettingsIcon, HomeIcon } from './icons' (200-500KB)
 */

import React from 'react'
import {
  Activity,
  AlertTriangle,
  ArrowLeft,
  ArrowRight,
  Award,
  BarChart3,
  Bell,
  BookOpen,
  Briefcase,
  Building,
  Calendar,
  Check,
  CheckCircle,
  CheckSquare,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  Clock,
  ClipboardList,
  CreditCard,
  Database,
  DollarSign,
  Download,
  Edit,
  Eye,
  FileText,
  Filter,
  FolderOpen,
  Gauge,
  Globe,
  GraduationCap,
  Heart,
  Home,
  LayoutDashboard,
  Loader2,
  Mail,
  MapPin,
  Megaphone,
  MessageSquare,
  Monitor,
  MoreHorizontal,
  Package,
  Phone,
  PieChart,
  Plus,
  RefreshCw,
  Save,
  Search,
  Send,
  Settings,
  Shield,
  ShoppingCart,
  Sparkles,
  Target,
  Trash2,
  TrendingUp,
  TrendingDown,
  Truck,
  Upload,
  User,
  Users,
  Wifi,
  Wrench,
  X,
  Zap,
  Bot,
  Star,
  Flag,
  Tag,
  Link,
  Image,
  Video,
  Music,
  Headphones,
  Camera,
  Mic,
  Volume2,
  VolumeX,
  Play,
  Pause,
  Square,
  SkipBack,
  SkipForward,
  Repeat,
  Shuffle,
  WifiOff,
  Bluetooth,
  Battery,
  BatteryLow,
  Signal,
  Smartphone,
  Tablet,
  Laptop,
  Printer,
  HardDrive,
  Cpu,
  MemoryStick,
  MousePointer,
  Keyboard,
  Gamepad2
} from 'lucide-react'

// Individual icon exports - only load what we use
export const ActivityIcon = Activity
export const AlertTriangleIcon = AlertTriangle
export const ArrowLeftIcon = ArrowLeft
export const ArrowRightIcon = ArrowRight
export const AwardIcon = Award
export const BarChart3Icon = BarChart3
export const BellIcon = Bell
export const BookOpenIcon = BookOpen
export const BriefcaseIcon = Briefcase
export const BuildingIcon = Building
export const CalendarIcon = Calendar
export const CheckIcon = Check
export const CheckCircleIcon = CheckCircle
export const CheckSquareIcon = CheckSquare
export const ChevronDownIcon = ChevronDown
export const ChevronLeftIcon = ChevronLeft
export const ChevronRightIcon = ChevronRight
export const ChevronUpIcon = ChevronUp
export const ClockIcon = Clock
export const ClipboardListIcon = ClipboardList
export const CreditCardIcon = CreditCard
export const DatabaseIcon = Database
export const DollarSignIcon = DollarSign
export const DownloadIcon = Download
export const EditIcon = Edit
export const EyeIcon = Eye
export const FileTextIcon = FileText
export const FilterIcon = Filter
export const FolderOpenIcon = FolderOpen
export const GaugeIcon = Gauge
export const GlobeIcon = Globe
export const GraduationCapIcon = GraduationCap
export const HeartIcon = Heart
export const HomeIcon = Home
export const LayoutDashboardIcon = LayoutDashboard
export const Loader2Icon = Loader2
export const MailIcon = Mail
export const MapPinIcon = MapPin
export const MegaphoneIcon = Megaphone
export const MessageSquareIcon = MessageSquare
export const MonitorIcon = Monitor
export const MoreHorizontalIcon = MoreHorizontal
export const PackageIcon = Package
export const PhoneIcon = Phone
export const PieChartIcon = PieChart
export const PlusIcon = Plus
export const RefreshCwIcon = RefreshCw
export const SaveIcon = Save
export const SearchIcon = Search
export const SendIcon = Send
export const SettingsIcon = Settings
export const ShieldIcon = Shield
export const ShoppingCartIcon = ShoppingCart
export const SparklesIcon = Sparkles
export const TargetIcon = Target
export const Trash2Icon = Trash2
export const TrendingUpIcon = TrendingUp
export const TrendingDownIcon = TrendingDown
export const TruckIcon = Truck
export const UploadIcon = Upload
export const UserIcon = User
export const UsersIcon = Users
export const WifiIcon = Wifi
export const WrenchIcon = Wrench
export const XIcon = X
export const ZapIcon = Zap

// Additional icons commonly used in the app
export const BotIcon = Bot
export const StarIcon = Star
export const FlagIcon = Flag
export const TagIcon = Tag
export const LinkIcon = Link
export const ImageIcon = Image
export const VideoIcon = Video
export const MusicIcon = Music
export const HeadphonesIcon = Headphones
export const CameraIcon = Camera
export const MicIcon = Mic
export const Volume2Icon = Volume2
export const VolumeXIcon = VolumeX
export const PlayIcon = Play
export const PauseIcon = Pause
export const SquareIcon = Square
export const SkipBackIcon = SkipBack
export const SkipForwardIcon = SkipForward
export const RepeatIcon = Repeat
export const ShuffleIcon = Shuffle
export const WifiOffIcon = WifiOff
export const BluetoothIcon = Bluetooth
export const BatteryIcon = Battery
export const BatteryLowIcon = BatteryLow
export const SignalIcon = Signal
export const SmartphoneIcon = Smartphone
export const TabletIcon = Tablet
export const LaptopIcon = Laptop
export const PrinterIcon = Printer
export const HardDriveIcon = HardDrive
export const CpuIcon = Cpu
export const MemoryStickIcon = MemoryStick
export const MousePointerIcon = MousePointer
export const KeyboardIcon = Keyboard
export const Gamepad2Icon = Gamepad2

// Type definition for icon props (consistent with Lucide React)
export interface IconProps {
  size?: number | string
  color?: string
  strokeWidth?: number | string
  className?: string
  style?: React.CSSProperties
}

// Icon component wrapper for consistent styling
export const Icon: React.FC<{ 
  icon: React.ComponentType<IconProps>
  size?: number | string
  className?: string
  color?: string
}> = ({ icon: IconComponent, size = 20, className = '', color, ...props }) => {
  return (
    <IconComponent
      size={size}
      className={className}
      color={color}
      {...props}
    />
  )
}

// Commonly used icon combinations
export const NavigationIcons = {
  Home: HomeIcon,
  Users: UsersIcon,
  Building: BuildingIcon,
  Package: PackageIcon,
  ShoppingCart: ShoppingCartIcon,
  BarChart3: BarChart3Icon,
  Settings: SettingsIcon,
  FileText: FileTextIcon,
  Calendar: CalendarIcon,
  MessageSquare: MessageSquareIcon,
  Bell: BellIcon,
  User: UserIcon,
  ClipboardList: ClipboardListIcon,
  FolderOpen: FolderOpenIcon,
  Megaphone: MegaphoneIcon,
  DollarSign: DollarSignIcon,
  TrendingUp: TrendingUpIcon,
  Shield: ShieldIcon,
  Database: DatabaseIcon,
  Zap: ZapIcon,
  Target: TargetIcon,
  BookOpen: BookOpenIcon,
  Award: AwardIcon,
  Clock: ClockIcon,
  MapPin: MapPinIcon,
  Phone: PhoneIcon,
  Mail: MailIcon,
  Globe: GlobeIcon,
  Briefcase: BriefcaseIcon,
  GraduationCap: GraduationCapIcon,
  Heart: HeartIcon,
  Truck: TruckIcon,
  Wrench: WrenchIcon,
  CreditCard: CreditCardIcon,
  PieChart: PieChartIcon,
  Activity: ActivityIcon,
  CheckSquare: CheckSquareIcon,
  AlertTriangle: AlertTriangleIcon
}

// Action icons
export const ActionIcons = {
  Search: SearchIcon,
  Filter: FilterIcon,
  Download: DownloadIcon,
  Upload: UploadIcon,
  RefreshCw: RefreshCwIcon,
  Eye: EyeIcon,
  Edit: EditIcon,
  Trash2: Trash2Icon,
  Plus: PlusIcon,
  Save: SaveIcon,
  X: XIcon,
  Check: CheckIcon,
  Send: SendIcon,
  ArrowLeft: ArrowLeftIcon,
  ArrowRight: ArrowRightIcon,
  ChevronDown: ChevronDownIcon,
  ChevronUp: ChevronUpIcon,
  ChevronLeft: ChevronLeftIcon,
  ChevronRight: ChevronRightIcon,
  MoreHorizontal: MoreHorizontalIcon,
  Star: StarIcon,
  Flag: FlagIcon,
  Tag: TagIcon,
  Link: LinkIcon
}

// Status icons
export const StatusIcons = {
  CheckCircle: CheckCircleIcon,
  AlertTriangle: AlertTriangleIcon,
  Loader2: Loader2Icon,
  TrendingUp: TrendingUpIcon,
  TrendingDown: TrendingDownIcon,
  Activity: ActivityIcon,
  Gauge: GaugeIcon,
  Monitor: MonitorIcon,
  Wifi: WifiIcon,
  Sparkles: SparklesIcon
}

// Media icons
export const MediaIcons = {
  Image: ImageIcon,
  Video: VideoIcon,
  Music: MusicIcon,
  Headphones: HeadphonesIcon,
  Camera: CameraIcon,
  Mic: MicIcon,
  Volume2: Volume2Icon,
  VolumeX: VolumeXIcon,
  Play: PlayIcon,
  Pause: PauseIcon,
  Square: SquareIcon,
  SkipBack: SkipBackIcon,
  SkipForward: SkipForwardIcon,
  Repeat: RepeatIcon,
  Shuffle: ShuffleIcon
}

// Device icons
export const DeviceIcons = {
  Smartphone: SmartphoneIcon,
  Tablet: TabletIcon,
  Laptop: LaptopIcon,
  Monitor: MonitorIcon,
  Printer: PrinterIcon,
  HardDrive: HardDriveIcon,
  Cpu: CpuIcon,
  MemoryStick: MemoryStickIcon,
  MousePointer: MousePointerIcon,
  Keyboard: KeyboardIcon,
  Gamepad2: Gamepad2Icon,
  Wifi: WifiIcon,
  WifiOff: WifiOffIcon,
  Bluetooth: BluetoothIcon,
  Battery: BatteryIcon,
  BatteryLow: BatteryLowIcon,
  Signal: SignalIcon
}
