/**
 * VALIDATION FIX: Comprehensive Validation Testing
 * Tests all validation functions and schemas to ensure they work correctly
 */

import { 
  validateEmail, 
  validatePhone, 
  validateSalary, 
  validatePastDate,
  validateForm,
  employeeValidationSchema,
  departmentValidationSchema
} from './validation'

import { formValidationManager } from './formValidation'

export interface ValidationTestResult {
  testName: string
  passed: boolean
  error?: string
  details?: any
}

export class ValidationTester {
  private results: ValidationTestResult[] = []

  // Run all validation tests
  async runAllTests(): Promise<ValidationTestResult[]> {
    this.results = []
    
    console.log('🧪 Starting comprehensive validation tests...')
    
    // Test individual validators
    this.testEmailValidation()
    this.testPhoneValidation()
    this.testSalaryValidation()
    this.testDateValidation()
    
    // Test form schemas
    this.testEmployeeValidationSchema()
    this.testDepartmentValidationSchema()
    
    // Test form validation manager
    this.testFormValidationManager()
    
    // Test edge cases
    this.testEdgeCases()
    
    this.displayResults()
    return this.results
  }

  private addResult(testName: string, passed: boolean, error?: string, details?: any) {
    this.results.push({ testName, passed, error, details })
  }

  // Test email validation
  private testEmailValidation() {
    const validEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ]
    
    const invalidEmails = [
      'invalid-email',
      '@domain.com',
      'user@',
      'user@domain',
      ''
    ]

    try {
      // Test valid emails
      validEmails.forEach(email => {
        const result = validateEmail(email)
        if (result !== null) {
          throw new Error(`Valid email "${email}" failed validation: ${result}`)
        }
      })

      // Test invalid emails
      invalidEmails.forEach(email => {
        const result = validateEmail(email)
        if (result === null) {
          throw new Error(`Invalid email "${email}" passed validation`)
        }
      })

      this.addResult('Email Validation', true)
    } catch (error) {
      this.addResult('Email Validation', false, error instanceof Error ? error.message : String(error))
    }
  }

  // Test phone validation
  private testPhoneValidation() {
    const validPhones = [
      '+1234567890',
      '1234567890',
      '+966501234567',
      '0501234567'
    ]
    
    const invalidPhones = [
      '123',
      'abc123',
      '+',
      '12345678901234567890', // too long
      ''
    ]

    try {
      // Test valid phones
      validPhones.forEach(phone => {
        const result = validatePhone(phone)
        if (result !== null) {
          throw new Error(`Valid phone "${phone}" failed validation: ${result}`)
        }
      })

      // Test invalid phones
      invalidPhones.forEach(phone => {
        const result = validatePhone(phone)
        if (result === null) {
          throw new Error(`Invalid phone "${phone}" passed validation`)
        }
      })

      this.addResult('Phone Validation', true)
    } catch (error) {
      this.addResult('Phone Validation', false, error instanceof Error ? error.message : String(error))
    }
  }

  // Test salary validation
  private testSalaryValidation() {
    const validSalaries = [1000, 50000, 100000, '5000']
    const invalidSalaries = [0, -1000, 'abc', '', null]

    try {
      // Test valid salaries
      validSalaries.forEach(salary => {
        const result = validateSalary(salary)
        if (result !== null) {
          throw new Error(`Valid salary "${salary}" failed validation: ${result}`)
        }
      })

      // Test invalid salaries
      invalidSalaries.forEach(salary => {
        const result = validateSalary(salary)
        if (result === null) {
          throw new Error(`Invalid salary "${salary}" passed validation`)
        }
      })

      this.addResult('Salary Validation', true)
    } catch (error) {
      this.addResult('Salary Validation', false, error instanceof Error ? error.message : String(error))
    }
  }

  // Test date validation
  private testDateValidation() {
    const validDates = ['2020-01-01', '2023-12-31']
    const invalidDates = ['2030-01-01', 'invalid-date', '']

    try {
      // Test valid dates (past dates)
      validDates.forEach(date => {
        const result = validatePastDate(date)
        if (result !== null) {
          throw new Error(`Valid date "${date}" failed validation: ${result}`)
        }
      })

      // Test invalid dates (future dates or invalid format)
      invalidDates.forEach(date => {
        const result = validatePastDate(date)
        if (result === null && date !== '') {
          throw new Error(`Invalid date "${date}" passed validation`)
        }
      })

      this.addResult('Date Validation', true)
    } catch (error) {
      this.addResult('Date Validation', false, error instanceof Error ? error.message : String(error))
    }
  }

  // Test employee validation schema
  private testEmployeeValidationSchema() {
    const validEmployee = {
      first_name: 'John',
      last_name: 'Doe',
      first_name_ar: 'جون',
      last_name_ar: 'دو',
      email: '<EMAIL>',
      phone: '+1234567890',
      address: '123 Main St, City',
      emergency_contact: '+0987654321',
      national_id: 'ID123456',
      department: 'IT',
      position: 'Developer',
      salary: 50000,
      hire_date: '2023-01-01'
    }

    const invalidEmployee = {
      first_name: '', // Required field missing
      email: 'invalid-email', // Invalid email
      phone: '123', // Invalid phone
      salary: -1000 // Invalid salary
    }

    try {
      // Test valid employee
      const validResult = validateForm(validEmployee, employeeValidationSchema)
      if (Object.keys(validResult).length > 0) {
        throw new Error(`Valid employee failed validation: ${JSON.stringify(validResult)}`)
      }

      // Test invalid employee
      const invalidResult = validateForm(invalidEmployee, employeeValidationSchema)
      if (Object.keys(invalidResult).length === 0) {
        throw new Error('Invalid employee passed validation')
      }

      this.addResult('Employee Schema Validation', true)
    } catch (error) {
      this.addResult('Employee Schema Validation', false, error instanceof Error ? error.message : String(error))
    }
  }

  // Test department validation schema
  private testDepartmentValidationSchema() {
    const validDepartment = {
      name: 'Information Technology',
      name_ar: 'تقنية المعلومات',
      description: 'IT Department',
      manager: 'emp123'
    }

    const invalidDepartment = {
      name: '', // Required field missing
      name_ar: 'ت' // Too short
    }

    try {
      // Test valid department
      const validResult = validateForm(validDepartment, departmentValidationSchema)
      if (Object.keys(validResult).length > 0) {
        throw new Error(`Valid department failed validation: ${JSON.stringify(validResult)}`)
      }

      // Test invalid department
      const invalidResult = validateForm(invalidDepartment, departmentValidationSchema)
      if (Object.keys(invalidResult).length === 0) {
        throw new Error('Invalid department passed validation')
      }

      this.addResult('Department Schema Validation', true)
    } catch (error) {
      this.addResult('Department Schema Validation', false, error instanceof Error ? error.message : String(error))
    }
  }

  // Test form validation manager
  private testFormValidationManager() {
    try {
      // Register schemas
      formValidationManager.registerSchema('employee', employeeValidationSchema)
      formValidationManager.registerSchema('department', departmentValidationSchema)

      // Test employee validation
      const employeeData = { first_name: 'John', email: 'invalid-email' }
      const employeeResult = formValidationManager.validateForm('employee', employeeData)
      
      if (employeeResult.isValid) {
        throw new Error('Invalid employee data passed validation')
      }

      if (!employeeResult.errors.email) {
        throw new Error('Email validation error not detected')
      }

      this.addResult('Form Validation Manager', true)
    } catch (error) {
      this.addResult('Form Validation Manager', false, error instanceof Error ? error.message : String(error))
    }
  }

  // Test edge cases
  private testEdgeCases() {
    try {
      // Test null/undefined values
      const nullResult = validateEmail(null as any)
      if (nullResult === null) {
        throw new Error('Null email should fail validation')
      }

      // Test empty strings
      const emptyResult = validatePhone('')
      if (emptyResult === null) {
        throw new Error('Empty phone should fail validation')
      }

      // Test very long strings
      const longString = 'a'.repeat(1000)
      const longResult = validateForm({ name: longString }, { name: { maxLength: 100 } })
      if (Object.keys(longResult).length === 0) {
        throw new Error('Long string should fail validation')
      }

      this.addResult('Edge Cases', true)
    } catch (error) {
      this.addResult('Edge Cases', false, error instanceof Error ? error.message : String(error))
    }
  }

  // Display test results
  private displayResults() {
    const passed = this.results.filter(r => r.passed).length
    const total = this.results.length
    
    console.log(`\n🧪 Validation Test Results: ${passed}/${total} passed`)
    
    this.results.forEach(result => {
      const icon = result.passed ? '✅' : '❌'
      console.log(`${icon} ${result.testName}`)
      if (!result.passed && result.error) {
        console.log(`   Error: ${result.error}`)
      }
    })

    if (passed === total) {
      console.log('🎉 All validation tests passed!')
    } else {
      console.log(`⚠️ ${total - passed} validation tests failed`)
    }
  }

  // Get test summary
  getTestSummary(): { passed: number; total: number; success: boolean } {
    const passed = this.results.filter(r => r.passed).length
    const total = this.results.length
    return { passed, total, success: passed === total }
  }
}

// Export singleton instance
export const validationTester = new ValidationTester()
export default validationTester
