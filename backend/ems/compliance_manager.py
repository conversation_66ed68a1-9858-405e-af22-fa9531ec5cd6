"""
CRITICAL FIX: GDPR Compliance & Data Protection Manager
Enterprise-grade compliance management with automated data protection
"""

import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from django.db import models, transaction
from django.contrib.auth.models import User
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from django.core.exceptions import ValidationError
import logging

logger = logging.getLogger(__name__)

class DataProtectionManager:
    """GDPR and data protection compliance manager"""
    
    # Data retention periods (in days)
    RETENTION_PERIODS = {
        'employee_data': 2555,      # 7 years
        'payroll_data': 2555,       # 7 years  
        'attendance_data': 1095,    # 3 years
        'leave_requests': 1095,     # 3 years
        'performance_reviews': 2555, # 7 years
        'audit_logs': 2555,         # 7 years
        'user_sessions': 30,        # 30 days
        'temporary_data': 90        # 90 days
    }
    
    # Sensitive data fields requiring special handling
    SENSITIVE_FIELDS = {
        'personal_identifiers': ['ssn', 'national_id', 'passport_number'],
        'financial_data': ['salary', 'bank_account', 'tax_id'],
        'health_data': ['medical_conditions', 'disabilities'],
        'biometric_data': ['fingerprint', 'facial_recognition'],
        'location_data': ['home_address', 'gps_coordinates']
    }
    
    @classmethod
    def create_data_processing_record(cls, user: User, data_type: str, 
                                    purpose: str, legal_basis: str, 
                                    retention_period: int = None):
        """Create GDPR data processing record"""
        
        from .models import DataProcessingRecord
        
        if not retention_period:
            retention_period = cls.RETENTION_PERIODS.get(data_type, 1095)
        
        record = DataProcessingRecord.objects.create(
            user=user,
            data_type=data_type,
            processing_purpose=purpose,
            legal_basis=legal_basis,
            retention_period_days=retention_period,
            created_at=timezone.now(),
            expires_at=timezone.now() + timedelta(days=retention_period)
        )
        
        logger.info(f"Created data processing record for user {user.id}: {data_type}")
        return record
    
    @classmethod
    def request_data_export(cls, user: User, requester_email: str = None):
        """Handle GDPR data export request"""
        
        from .models import DataExportRequest
        
        # Create export request
        export_request = DataExportRequest.objects.create(
            user=user,
            requester_email=requester_email or user.email,
            status='pending',
            requested_at=timezone.now()
        )
        
        # Generate export data
        export_data = cls.generate_user_data_export(user)
        
        # Save export data
        export_request.export_data = export_data
        export_request.status = 'completed'
        export_request.completed_at = timezone.now()
        export_request.save()
        
        # Send notification email
        cls.send_data_export_notification(export_request)
        
        logger.info(f"Data export completed for user {user.id}")
        return export_request
    
    @classmethod
    def generate_user_data_export(cls, user: User) -> Dict[str, Any]:
        """Generate comprehensive user data export"""
        
        export_data = {
            "export_metadata": {
                "user_id": user.id,
                "export_date": timezone.now().isoformat(),
                "export_version": "1.0",
                "data_controller": "EMS - Employee Management System"
            },
            "personal_data": {},
            "employment_data": {},
            "activity_data": {},
            "system_data": {}
        }
        
        # Personal data
        export_data["personal_data"] = {
            "username": user.username,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "date_joined": user.date_joined.isoformat(),
            "last_login": user.last_login.isoformat() if user.last_login else None,
            "is_active": user.is_active
        }
        
        # Employment data
        try:
            from .models import Employee
            employee = Employee.objects.get(user=user)
            
            export_data["employment_data"] = {
                "employee_id": employee.id,
                "hire_date": employee.hire_date.isoformat() if employee.hire_date else None,
                "department": employee.department.name if employee.department else None,
                "position": employee.position,
                "is_active": employee.is_active,
                "created_at": employee.created_at.isoformat(),
                "updated_at": employee.updated_at.isoformat()
            }
            
            # Leave requests
            leave_requests = employee.leave_requests.all()
            export_data["employment_data"]["leave_requests"] = [
                {
                    "id": lr.id,
                    "leave_type": lr.leave_type.name if lr.leave_type else None,
                    "start_date": lr.start_date.isoformat(),
                    "end_date": lr.end_date.isoformat(),
                    "days_requested": lr.days_requested,
                    "status": lr.status,
                    "reason": lr.reason,
                    "created_at": lr.created_at.isoformat()
                }
                for lr in leave_requests
            ]
            
            # Attendance records
            attendance_records = employee.attendance_records.all()[:100]  # Last 100 records
            export_data["employment_data"]["attendance_records"] = [
                {
                    "date": ar.date.isoformat(),
                    "check_in": ar.check_in.isoformat() if ar.check_in else None,
                    "check_out": ar.check_out.isoformat() if ar.check_out else None,
                    "hours_worked": str(ar.hours_worked) if ar.hours_worked else None
                }
                for ar in attendance_records
            ]
            
        except Exception as e:
            logger.warning(f"Could not export employment data for user {user.id}: {e}")
        
        # Activity data
        try:
            from .models import Activity
            activities = Activity.objects.filter(user=user)[:100]  # Last 100 activities
            
            export_data["activity_data"]["user_activities"] = [
                {
                    "action": activity.action,
                    "description": activity.description,
                    "timestamp": activity.timestamp.isoformat(),
                    "ip_address": getattr(activity, 'ip_address', None)
                }
                for activity in activities
            ]
            
        except Exception as e:
            logger.warning(f"Could not export activity data for user {user.id}: {e}")
        
        # System data
        export_data["system_data"] = {
            "login_history": cls.get_login_history(user),
            "data_processing_records": cls.get_data_processing_records(user),
            "consent_records": cls.get_consent_records(user)
        }
        
        return export_data
    
    @classmethod
    def get_login_history(cls, user: User) -> List[Dict[str, Any]]:
        """Get user login history"""
        
        try:
            from .models import LoginHistory
            login_records = LoginHistory.objects.filter(user=user).order_by('-timestamp')[:50]
            
            return [
                {
                    "timestamp": record.timestamp.isoformat(),
                    "ip_address": record.ip_address,
                    "user_agent": record.user_agent,
                    "success": record.success
                }
                for record in login_records
            ]
            
        except Exception:
            return []
    
    @classmethod
    def get_data_processing_records(cls, user: User) -> List[Dict[str, Any]]:
        """Get data processing records for user"""
        
        try:
            from .models import DataProcessingRecord
            records = DataProcessingRecord.objects.filter(user=user)
            
            return [
                {
                    "data_type": record.data_type,
                    "processing_purpose": record.processing_purpose,
                    "legal_basis": record.legal_basis,
                    "created_at": record.created_at.isoformat(),
                    "expires_at": record.expires_at.isoformat(),
                    "is_active": record.is_active
                }
                for record in records
            ]
            
        except Exception:
            return []
    
    @classmethod
    def get_consent_records(cls, user: User) -> List[Dict[str, Any]]:
        """Get consent records for user"""
        
        try:
            from .models import ConsentRecord
            consents = ConsentRecord.objects.filter(user=user)
            
            return [
                {
                    "consent_type": consent.consent_type,
                    "purpose": consent.purpose,
                    "granted": consent.granted,
                    "granted_at": consent.granted_at.isoformat() if consent.granted_at else None,
                    "withdrawn_at": consent.withdrawn_at.isoformat() if consent.withdrawn_at else None,
                    "version": consent.version
                }
                for consent in consents
            ]
            
        except Exception:
            return []
    
    @classmethod
    def send_data_export_notification(cls, export_request):
        """Send data export notification email"""
        
        try:
            subject = "Your Data Export is Ready - EMS"
            message = f"""
Dear {export_request.user.get_full_name() or export_request.user.username},

Your personal data export request has been completed. 

Request Details:
- Request ID: {export_request.id}
- Requested on: {export_request.requested_at.strftime('%Y-%m-%d %H:%M:%S')}
- Completed on: {export_request.completed_at.strftime('%Y-%m-%d %H:%M:%S')}

For security reasons, please log into your account to download the export file.
The export will be available for 30 days.

If you did not request this export, please contact our support team immediately.

Best regards,
EMS Data Protection Team
            """
            
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [export_request.requester_email],
                fail_silently=False
            )
            
        except Exception as e:
            logger.error(f"Failed to send data export notification: {e}")
    
    @classmethod
    def request_data_deletion(cls, user: User, deletion_reason: str = None):
        """Handle GDPR data deletion request (Right to be Forgotten)"""
        
        from .models import DataDeletionRequest
        
        # Create deletion request
        deletion_request = DataDeletionRequest.objects.create(
            user=user,
            reason=deletion_reason or "User requested data deletion",
            status='pending',
            requested_at=timezone.now()
        )
        
        # Check if user can be deleted (employment status, legal obligations)
        can_delete, restrictions = cls.check_deletion_restrictions(user)
        
        if can_delete:
            # Perform data anonymization/deletion
            cls.anonymize_user_data(user, deletion_request)
            deletion_request.status = 'completed'
            deletion_request.completed_at = timezone.now()
        else:
            deletion_request.status = 'restricted'
            deletion_request.restriction_reason = "; ".join(restrictions)
        
        deletion_request.save()
        
        # Send notification
        cls.send_data_deletion_notification(deletion_request, can_delete, restrictions)
        
        logger.info(f"Data deletion request processed for user {user.id}: {deletion_request.status}")
        return deletion_request
    
    @classmethod
    def check_deletion_restrictions(cls, user: User) -> tuple[bool, List[str]]:
        """Check if user data can be deleted"""
        
        restrictions = []
        
        try:
            from .models import Employee
            employee = Employee.objects.get(user=user)
            
            # Check if employee is still active
            if employee.is_active:
                restrictions.append("Employee is still active - cannot delete active employee data")
            
            # Check legal retention requirements
            if employee.hire_date:
                years_since_hire = (timezone.now().date() - employee.hire_date).days / 365
                if years_since_hire < 7:  # 7-year retention requirement
                    restrictions.append("Legal retention period not met (7 years from hire date)")
            
            # Check for pending legal matters
            pending_leaves = employee.leave_requests.filter(status='pending').count()
            if pending_leaves > 0:
                restrictions.append(f"Pending leave requests ({pending_leaves}) must be resolved")
            
        except Employee.DoesNotExist:
            pass
        
        # Check for admin/superuser status
        if user.is_superuser or user.is_staff:
            restrictions.append("Cannot delete admin/staff user data - transfer privileges first")
        
        return len(restrictions) == 0, restrictions
    
    @classmethod
    def anonymize_user_data(cls, user: User, deletion_request):
        """Anonymize user data while preserving necessary records"""
        
        anonymized_id = f"anon_{hashlib.md5(f'{user.id}_{timezone.now()}'.encode()).hexdigest()[:8]}"
        
        with transaction.atomic():
            # Anonymize user account
            user.username = anonymized_id
            user.email = f"{anonymized_id}@anonymized.local"
            user.first_name = "Anonymized"
            user.last_name = "User"
            user.is_active = False
            user.save()
            
            # Anonymize employee data
            try:
                from .models import Employee
                employee = Employee.objects.get(user=user)
                employee.is_active = False
                # Keep employment dates for legal compliance but anonymize personal data
                employee.save()
                
            except Employee.DoesNotExist:
                pass
            
            # Anonymize activity logs (keep for audit but remove personal identifiers)
            try:
                from .models import Activity
                Activity.objects.filter(user=user).update(
                    description=f"Activity by anonymized user {anonymized_id}"
                )
                
            except Exception as e:
                logger.warning(f"Could not anonymize activity logs: {e}")
            
            # Record anonymization
            deletion_request.anonymization_id = anonymized_id
            deletion_request.save()
    
    @classmethod
    def send_data_deletion_notification(cls, deletion_request, can_delete: bool, restrictions: List[str]):
        """Send data deletion notification email"""
        
        try:
            if can_delete:
                subject = "Data Deletion Completed - EMS"
                message = f"""
Dear User,

Your data deletion request has been completed.

Request Details:
- Request ID: {deletion_request.id}
- Requested on: {deletion_request.requested_at.strftime('%Y-%m-%d %H:%M:%S')}
- Completed on: {deletion_request.completed_at.strftime('%Y-%m-%d %H:%M:%S')}

Your personal data has been anonymized in accordance with GDPR requirements.
Some records may be retained for legal compliance purposes but have been anonymized.

Best regards,
EMS Data Protection Team
                """
            else:
                subject = "Data Deletion Request - Restrictions Apply"
                message = f"""
Dear {deletion_request.user.get_full_name() or deletion_request.user.username},

We have received your data deletion request, however, we cannot complete it at this time due to the following restrictions:

{chr(10).join(f"- {restriction}" for restriction in restrictions)}

Please contact our Data Protection Officer for more information about these restrictions and when your data may be eligible for deletion.

Request ID: {deletion_request.id}
Requested on: {deletion_request.requested_at.strftime('%Y-%m-%d %H:%M:%S')}

Best regards,
EMS Data Protection Team
                """
            
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [deletion_request.user.email],
                fail_silently=False
            )
            
        except Exception as e:
            logger.error(f"Failed to send data deletion notification: {e}")
    
    @classmethod
    def record_consent(cls, user: User, consent_type: str, purpose: str, 
                      granted: bool, version: str = "1.0"):
        """Record user consent for data processing"""
        
        from .models import ConsentRecord
        
        # Withdraw previous consent of same type
        ConsentRecord.objects.filter(
            user=user,
            consent_type=consent_type,
            withdrawn_at__isnull=True
        ).update(withdrawn_at=timezone.now())
        
        # Create new consent record
        consent = ConsentRecord.objects.create(
            user=user,
            consent_type=consent_type,
            purpose=purpose,
            granted=granted,
            granted_at=timezone.now() if granted else None,
            version=version
        )
        
        logger.info(f"Recorded consent for user {user.id}: {consent_type} = {granted}")
        return consent
    
    @classmethod
    def check_data_retention(cls):
        """Check and enforce data retention policies"""
        
        from .models import DataProcessingRecord, Employee, Activity
        
        retention_actions = {
            "expired_records": 0,
            "anonymized_records": 0,
            "deleted_records": 0
        }
        
        # Check expired data processing records
        expired_records = DataProcessingRecord.objects.filter(
            expires_at__lt=timezone.now(),
            is_active=True
        )
        
        for record in expired_records:
            # Determine action based on data type
            if record.data_type in ['user_sessions', 'temporary_data']:
                # Delete temporary data
                record.delete()
                retention_actions["deleted_records"] += 1
            else:
                # Anonymize other data
                record.is_active = False
                record.anonymized_at = timezone.now()
                record.save()
                retention_actions["anonymized_records"] += 1
        
        # Check old activity logs
        cutoff_date = timezone.now() - timedelta(days=cls.RETENTION_PERIODS['audit_logs'])
        old_activities = Activity.objects.filter(timestamp__lt=cutoff_date)
        
        # Anonymize old activities
        old_activities.update(
            description="Anonymized due to retention policy",
            user=None
        )
        retention_actions["anonymized_records"] += old_activities.count()
        
        logger.info(f"Data retention check completed: {retention_actions}")
        return retention_actions
    
    @classmethod
    def generate_compliance_report(cls) -> Dict[str, Any]:
        """Generate GDPR compliance report"""
        
        from .models import (DataProcessingRecord, DataExportRequest, 
                           DataDeletionRequest, ConsentRecord)
        
        report = {
            "report_date": timezone.now().isoformat(),
            "data_processing": {
                "active_records": DataProcessingRecord.objects.filter(is_active=True).count(),
                "expired_records": DataProcessingRecord.objects.filter(
                    expires_at__lt=timezone.now(), is_active=True
                ).count()
            },
            "data_subject_requests": {
                "export_requests": {
                    "total": DataExportRequest.objects.count(),
                    "pending": DataExportRequest.objects.filter(status='pending').count(),
                    "completed": DataExportRequest.objects.filter(status='completed').count()
                },
                "deletion_requests": {
                    "total": DataDeletionRequest.objects.count(),
                    "pending": DataDeletionRequest.objects.filter(status='pending').count(),
                    "completed": DataDeletionRequest.objects.filter(status='completed').count(),
                    "restricted": DataDeletionRequest.objects.filter(status='restricted').count()
                }
            },
            "consent_management": {
                "total_consents": ConsentRecord.objects.count(),
                "active_consents": ConsentRecord.objects.filter(
                    granted=True, withdrawn_at__isnull=True
                ).count(),
                "withdrawn_consents": ConsentRecord.objects.filter(
                    withdrawn_at__isnull=False
                ).count()
            },
            "retention_compliance": cls.check_data_retention()
        }
        
        return report

# GDPR API Views

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def request_data_export(request):
    """API endpoint for GDPR data export requests"""
    
    try:
        export_request = DataProtectionManager.request_data_export(request.user)
        
        return Response({
            "message": "Data export request submitted successfully",
            "request_id": export_request.id,
            "status": export_request.status
        })
        
    except Exception as e:
        logger.error(f"Data export request failed: {e}")
        return Response({
            "error": "Failed to process data export request"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def request_data_deletion(request):
    """API endpoint for GDPR data deletion requests"""
    
    try:
        reason = request.data.get('reason', 'User requested data deletion')
        deletion_request = DataProtectionManager.request_data_deletion(request.user, reason)
        
        return Response({
            "message": "Data deletion request submitted successfully",
            "request_id": deletion_request.id,
            "status": deletion_request.status,
            "can_delete": deletion_request.status != 'restricted'
        })
        
    except Exception as e:
        logger.error(f"Data deletion request failed: {e}")
        return Response({
            "error": "Failed to process data deletion request"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def record_user_consent(request):
    """API endpoint for recording user consent"""
    
    try:
        consent_type = request.data.get('consent_type')
        purpose = request.data.get('purpose')
        granted = request.data.get('granted', False)
        version = request.data.get('version', '1.0')
        
        if not consent_type or not purpose:
            return Response({
                "error": "consent_type and purpose are required"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        consent = DataProtectionManager.record_consent(
            request.user, consent_type, purpose, granted, version
        )
        
        return Response({
            "message": "Consent recorded successfully",
            "consent_id": consent.id,
            "granted": consent.granted
        })
        
    except Exception as e:
        logger.error(f"Consent recording failed: {e}")
        return Response({
            "error": "Failed to record consent"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
