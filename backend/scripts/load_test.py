#!/usr/bin/env python3
"""
CRITICAL FIX: Load Testing Script
Comprehensive load testing for production readiness validation
"""

import asyncio
import aiohttp
import time
import statistics
from concurrent.futures import ThreadPoolExecutor
import json
import sys

class LoadTester:
    def __init__(self, base_url="http://localhost:8000", max_workers=50):
        self.base_url = base_url
        self.max_workers = max_workers
        self.results = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "response_times": [],
            "status_codes": {},
            "errors": []
        }
    
    async def make_request(self, session, endpoint, method="GET", data=None):
        """Make a single HTTP request"""
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()
        
        try:
            if method == "GET":
                async with session.get(url) as response:
                    await response.text()
                    return {
                        "status": response.status,
                        "response_time": time.time() - start_time,
                        "success": True
                    }
            elif method == "POST":
                async with session.post(url, json=data) as response:
                    await response.text()
                    return {
                        "status": response.status,
                        "response_time": time.time() - start_time,
                        "success": True
                    }
        except Exception as e:
            return {
                "status": "error",
                "response_time": time.time() - start_time,
                "success": False,
                "error": str(e)
            }
    
    async def run_concurrent_requests(self, endpoint, num_requests, method="GET", data=None):
        """Run concurrent requests to an endpoint"""
        
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=50)
        timeout = aiohttp.ClientTimeout(total=30)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            tasks = []
            
            for _ in range(num_requests):
                task = self.make_request(session, endpoint, method, data)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for result in results:
                if isinstance(result, Exception):
                    self.results["failed_requests"] += 1
                    self.results["errors"].append(str(result))
                else:
                    self.results["total_requests"] += 1
                    
                    if result["success"]:
                        self.results["successful_requests"] += 1
                        self.results["response_times"].append(result["response_time"])
                    else:
                        self.results["failed_requests"] += 1
                        self.results["errors"].append(result.get("error", "Unknown error"))
                    
                    # Track status codes
                    status = result["status"]
                    self.results["status_codes"][status] = self.results["status_codes"].get(status, 0) + 1
    
    def test_health_endpoint(self):
        """Test health check endpoint under load"""
        print("🏥 Testing health endpoint...")
        
        asyncio.run(self.run_concurrent_requests("/api/health/", 100))
        
        print(f"  📊 Health endpoint: {self.results['successful_requests']}/{self.results['total_requests']} successful")
    
    def test_api_endpoints(self):
        """Test main API endpoints under load"""
        print("🌐 Testing API endpoints...")
        
        endpoints = [
            "/api/dashboard-stats/",
            "/api/employees/",
            "/api/departments/",
            "/api/projects/"
        ]
        
        for endpoint in endpoints:
            print(f"  Testing {endpoint}...")
            asyncio.run(self.run_concurrent_requests(endpoint, 50))
    
    def test_authentication_load(self):
        """Test authentication endpoints under load"""
        print("🔐 Testing authentication load...")
        
        # Test login endpoint with invalid credentials (should be rate limited)
        login_data = {"username": "testuser", "password": "wrongpassword"}
        
        asyncio.run(self.run_concurrent_requests("/api/auth/login/", 20, "POST", login_data))
        
        print(f"  📊 Auth endpoint: {self.results['successful_requests']}/{self.results['total_requests']} requests processed")
    
    def test_database_intensive_operations(self):
        """Test database-intensive operations"""
        print("🗄️ Testing database-intensive operations...")
        
        # Test endpoints that require complex database queries
        db_endpoints = [
            "/api/superadmin/system-stats/",
            "/api/user-profile/"
        ]
        
        for endpoint in db_endpoints:
            print(f"  Testing {endpoint}...")
            asyncio.run(self.run_concurrent_requests(endpoint, 30))
    
    def run_comprehensive_load_test(self):
        """Run comprehensive load test"""
        print("🚀 Starting Comprehensive Load Test...")
        print("=" * 60)
        
        start_time = time.time()
        
        # Reset results
        self.results = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "response_times": [],
            "status_codes": {},
            "errors": []
        }
        
        try:
            # Test 1: Health endpoint
            self.test_health_endpoint()
            
            # Test 2: API endpoints
            self.test_api_endpoints()
            
            # Test 3: Authentication load
            self.test_authentication_load()
            
            # Test 4: Database operations
            self.test_database_intensive_operations()
            
            # Calculate statistics
            total_time = time.time() - start_time
            self.print_load_test_summary(total_time)
            
            return self.assess_performance()
            
        except Exception as e:
            print(f"💥 Load test failed: {e}")
            return False
    
    def print_load_test_summary(self, total_time):
        """Print comprehensive load test summary"""
        print("\n" + "=" * 60)
        print("📊 LOAD TEST SUMMARY")
        print("=" * 60)
        
        # Basic statistics
        total_requests = self.results["total_requests"]
        successful_requests = self.results["successful_requests"]
        failed_requests = self.results["failed_requests"]
        success_rate = (successful_requests / total_requests * 100) if total_requests > 0 else 0
        
        print(f"\n🎯 Overall Performance:")
        print(f"  • Total requests: {total_requests}")
        print(f"  • Successful requests: {successful_requests}")
        print(f"  • Failed requests: {failed_requests}")
        print(f"  • Success rate: {success_rate:.2f}%")
        print(f"  • Total test time: {total_time:.2f} seconds")
        print(f"  • Requests per second: {total_requests/total_time:.2f}")
        
        # Response time statistics
        if self.results["response_times"]:
            response_times = self.results["response_times"]
            avg_response_time = statistics.mean(response_times) * 1000
            median_response_time = statistics.median(response_times) * 1000
            p95_response_time = sorted(response_times)[int(len(response_times) * 0.95)] * 1000
            max_response_time = max(response_times) * 1000
            
            print(f"\n⏱️ Response Time Statistics:")
            print(f"  • Average: {avg_response_time:.2f}ms")
            print(f"  • Median: {median_response_time:.2f}ms")
            print(f"  • 95th percentile: {p95_response_time:.2f}ms")
            print(f"  • Maximum: {max_response_time:.2f}ms")
        
        # Status code distribution
        if self.results["status_codes"]:
            print(f"\n📈 Status Code Distribution:")
            for status, count in sorted(self.results["status_codes"].items()):
                percentage = (count / total_requests * 100) if total_requests > 0 else 0
                print(f"  • {status}: {count} ({percentage:.1f}%)")
        
        # Error summary
        if self.results["errors"]:
            print(f"\n❌ Errors ({len(self.results['errors'])}):")
            error_counts = {}
            for error in self.results["errors"]:
                error_counts[error] = error_counts.get(error, 0) + 1
            
            for error, count in sorted(error_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"  • {error}: {count} occurrences")
    
    def assess_performance(self):
        """Assess overall performance and provide recommendations"""
        print(f"\n💡 Performance Assessment:")
        
        issues = []
        recommendations = []
        
        # Check success rate
        total_requests = self.results["total_requests"]
        successful_requests = self.results["successful_requests"]
        success_rate = (successful_requests / total_requests * 100) if total_requests > 0 else 0
        
        if success_rate < 95:
            issues.append(f"Low success rate: {success_rate:.2f}%")
            recommendations.append("Investigate failed requests and improve error handling")
        
        # Check response times
        if self.results["response_times"]:
            avg_response_time = statistics.mean(self.results["response_times"]) * 1000
            p95_response_time = sorted(self.results["response_times"])[int(len(self.results["response_times"]) * 0.95)] * 1000
            
            if avg_response_time > 500:
                issues.append(f"High average response time: {avg_response_time:.2f}ms")
                recommendations.append("Optimize database queries and add caching")
            
            if p95_response_time > 1000:
                issues.append(f"High 95th percentile response time: {p95_response_time:.2f}ms")
                recommendations.append("Investigate slow queries and optimize bottlenecks")
        
        # Check for rate limiting
        rate_limited = self.results["status_codes"].get(429, 0)
        if rate_limited > 0:
            print(f"  ✅ Rate limiting working: {rate_limited} requests rate limited")
        
        # Print assessment
        if issues:
            print(f"  ⚠️ Issues found:")
            for issue in issues:
                print(f"    • {issue}")
            
            print(f"  🔧 Recommendations:")
            for rec in recommendations:
                print(f"    • {rec}")
            
            return False
        else:
            print(f"  🎉 Performance looks good!")
            return True

if __name__ == '__main__':
    # Allow custom base URL and worker count
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8000"
    max_workers = int(sys.argv[2]) if len(sys.argv) > 2 else 50
    
    tester = LoadTester(base_url, max_workers)
    success = tester.run_comprehensive_load_test()
    
    sys.exit(0 if success else 1)
