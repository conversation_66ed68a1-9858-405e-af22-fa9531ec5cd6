{"dashboard": {"id": null, "title": "EMS Performance Dashboard", "tags": ["ems", "performance", "monitoring"], "timezone": "browser", "panels": [{"id": 1, "title": "API Response Times", "type": "graph", "targets": [{"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"ems-backend\"}[5m]))", "legendFormat": "50th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"ems-backend\"}[5m]))", "legendFormat": "95th percentile", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket{job=\"ems-backend\"}[5m]))", "legendFormat": "99th percentile", "refId": "C"}], "yAxes": [{"label": "Response Time (seconds)", "min": 0}], "alert": {"conditions": [{"query": {"queryType": "", "refId": "B"}, "reducer": {"type": "last", "params": []}, "evaluator": {"params": [1.0], "type": "gt"}}], "executionErrorState": "alerting", "for": "5m", "frequency": "10s", "handler": 1, "name": "High API Response Time", "noDataState": "no_data", "notifications": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Request Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total{job=\"ems-backend\"}[5m])", "legendFormat": "{{method}} {{endpoint}}", "refId": "A"}], "yAxes": [{"label": "Requests per second", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Error Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total{job=\"ems-backend\",status=~\"5..\"}[5m]) / rate(http_requests_total{job=\"ems-backend\"}[5m]) * 100", "legendFormat": "5xx Error Rate %", "refId": "A"}, {"expr": "rate(http_requests_total{job=\"ems-backend\",status=~\"4..\"}[5m]) / rate(http_requests_total{job=\"ems-backend\"}[5m]) * 100", "legendFormat": "4xx Error Rate %", "refId": "B"}], "yAxes": [{"label": "Error Rate (%)", "min": 0, "max": 100}], "alert": {"conditions": [{"query": {"queryType": "", "refId": "A"}, "reducer": {"type": "last", "params": []}, "evaluator": {"params": [5.0], "type": "gt"}}], "executionErrorState": "alerting", "for": "2m", "frequency": "10s", "handler": 1, "name": "High Error Rate", "noDataState": "no_data", "notifications": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Database Performance", "type": "graph", "targets": [{"expr": "rate(pg_stat_database_tup_returned[5m])", "legendFormat": "Rows returned/sec", "refId": "A"}, {"expr": "rate(pg_stat_database_tup_fetched[5m])", "legendFormat": "Rows fetched/sec", "refId": "B"}, {"expr": "pg_stat_database_numbackends", "legendFormat": "Active connections", "refId": "C"}], "yAxes": [{"label": "Operations per second", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "<PERSON><PERSON>", "type": "graph", "targets": [{"expr": "rate(redis_keyspace_hits_total[5m])", "legendFormat": "Cache hits/sec", "refId": "A"}, {"expr": "rate(redis_keyspace_misses_total[5m])", "legendFormat": "<PERSON><PERSON> misses/sec", "refId": "B"}, {"expr": "rate(redis_keyspace_hits_total[5m]) / (rate(redis_keyspace_hits_total[5m]) + rate(redis_keyspace_misses_total[5m])) * 100", "legendFormat": "Hit rate %", "refId": "C"}], "yAxes": [{"label": "Operations per second / Hit rate %", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 6, "title": "System Resources", "type": "graph", "targets": [{"expr": "100 - (avg by(instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)", "legendFormat": "CPU Usage %", "refId": "A"}, {"expr": "(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100", "legendFormat": "Memory Usage %", "refId": "B"}, {"expr": "(node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes * 100", "legendFormat": "Disk Usage %", "refId": "C"}], "yAxes": [{"label": "Usage %", "min": 0, "max": 100}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 7, "title": "Query Performance", "type": "table", "targets": [{"expr": "topk(10, rate(pg_stat_statements_total_time[5m]))", "legendFormat": "{{query}}", "refId": "A", "format": "table"}], "columns": [{"text": "Query", "value": "query"}, {"text": "Total Time (ms/sec)", "value": "Value"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}}, {"id": 8, "title": "Authentication Events", "type": "graph", "targets": [{"expr": "rate(http_requests_total{job=\"ems-backend\",endpoint=\"/api/auth/login/\",status=\"200\"}[5m])", "legendFormat": "Successful logins/sec", "refId": "A"}, {"expr": "rate(http_requests_total{job=\"ems-backend\",endpoint=\"/api/auth/login/\",status=\"401\"}[5m])", "legendFormat": "Failed logins/sec", "refId": "B"}, {"expr": "rate(http_requests_total{job=\"ems-backend\",status=\"429\"}[5m])", "legendFormat": "Rate limited requests/sec", "refId": "C"}], "yAxes": [{"label": "Events per second", "min": 0}], "alert": {"conditions": [{"query": {"queryType": "", "refId": "B"}, "reducer": {"type": "last", "params": []}, "evaluator": {"params": [5.0], "type": "gt"}}], "executionErrorState": "alerting", "for": "2m", "frequency": "10s", "handler": 1, "name": "High Failed Login Rate", "noDataState": "no_data", "notifications": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}}, {"id": 9, "title": "Business Metrics", "type": "stat", "targets": [{"expr": "ems_active_users_total", "legendFormat": "Active Users", "refId": "A"}, {"expr": "ems_pending_leave_requests_total", "legendFormat": "Pending Leave Requests", "refId": "B"}, {"expr": "ems_active_projects_total", "legendFormat": "Active Projects", "refId": "C"}, {"expr": "ems_overdue_tasks_total", "legendFormat": "Overdue Tasks", "refId": "D"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}}, {"id": 10, "title": "Top Slow Endpoints", "type": "table", "targets": [{"expr": "topk(10, histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"ems-backend\"}[5m])))", "legendFormat": "{{endpoint}}", "refId": "A", "format": "table"}], "columns": [{"text": "Endpoint", "value": "endpoint"}, {"text": "95th Percentile (seconds)", "value": "Value"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 40}}], "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(up{job=\"ems-backend\"}, instance)", "refresh": 1, "includeAll": true, "multi": true}, {"name": "endpoint", "type": "query", "query": "label_values(http_requests_total{job=\"ems-backend\"}, endpoint)", "refresh": 1, "includeAll": true, "multi": true}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "prometheus", "expr": "changes(up{job=\"ems-backend\"}[1m]) > 0", "iconColor": "blue", "textFormat": "Deployment detected"}]}, "refresh": "30s", "schemaVersion": 27, "version": 1, "links": [{"title": "EMS Application", "url": "http://localhost:3000", "type": "link"}, {"title": "Prometheus", "url": "http://localhost:9090", "type": "link"}]}}