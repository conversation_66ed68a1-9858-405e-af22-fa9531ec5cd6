"""
Simple database configuration for testing
"""

import os

def get_database_config():
    """Get database configuration"""
    return {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'db.sqlite3'),
    }

def get_database_health_check():
    """Simple health check"""
    return True

DATABASE_MONITORING = {
    'enabled': False,
    'LOG_SLOW_QUERIES': False,
    'SLOW_QUERY_THRESHOLD': 1.0,
    'LOG_QUERY_COUNTS': False
}