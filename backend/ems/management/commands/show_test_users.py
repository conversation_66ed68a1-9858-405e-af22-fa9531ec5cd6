"""
Django management command to display test user credentials
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from ems.models import UserProfile, Employee


class Command(BaseCommand):
    help = 'Display test user credentials and roles'

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('=== EMS Test User Credentials ===\n')
        )
        
        users = User.objects.filter(is_active=True).order_by('username')
        
        if not users.exists():
            self.stdout.write(
                self.style.WARNING('No users found. Run "python manage.py create_sample_data" first.')
            )
            return
        
        self.stdout.write('All test users have password: password123\n')
        
        for user in users:
            try:
                profile = user.userprofile
                employee = user.employee
                role_name = profile.role.get_name_display() if profile.role else 'No Role'
                department = employee.department.name if employee.department else 'No Department'
                
                self.stdout.write(
                    f"Username: {user.username:<15} | "
                    f"Name: {user.get_full_name():<20} | "
                    f"Role: {role_name:<20} | "
                    f"Department: {department}"
                )
            except (UserProfile.DoesNotExist, Employee.DoesNotExist):
                self.stdout.write(
                    f"Username: {user.username:<15} | "
                    f"Name: {user.get_full_name():<20} | "
                    f"Role: No Profile | "
                    f"Department: No Employee Record"
                )
        
        self.stdout.write('\n=== Quick Access URLs ===')
        self.stdout.write('Frontend: http://localhost:5174')
        self.stdout.write('Backend Admin: http://localhost:8000/admin')
        self.stdout.write('API Root: http://localhost:8000/api')
        
        self.stdout.write('\n=== Role-Based Access ===')
        self.stdout.write('Super Admin (superadmin): Full system access')
        self.stdout.write('HR Manager (hrmanager): HR module access')
        self.stdout.write('Finance Manager (financemanager): Finance module access')
        self.stdout.write('Department Managers: Department-specific access')
        self.stdout.write('Project Manager (projectmanager): Project management access')
        self.stdout.write('Employees: Basic employee access')
