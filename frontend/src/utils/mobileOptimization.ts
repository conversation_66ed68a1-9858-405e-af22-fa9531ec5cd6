/**
 * MOBILE FIX: Comprehensive Mobile Optimization Utilities
 * Provides mobile-specific optimizations and responsive behavior
 */

import React from 'react'

export type DeviceType = 'mobile' | 'tablet' | 'desktop'
export type Orientation = 'portrait' | 'landscape'

export interface DeviceInfo {
  type: DeviceType
  orientation: Orientation
  touchSupport: boolean
  screenSize: {
    width: number
    height: number
  }
  pixelRatio: number
  isLowEnd: boolean
  connectionType?: string
}

export interface ViewportBreakpoints {
  mobile: number
  tablet: number
  desktop: number
  wide: number
}

export const BREAKPOINTS: ViewportBreakpoints = {
  mobile: 768,
  tablet: 1024,
  desktop: 1280,
  wide: 1536
}

class MobileOptimizationManager {
  private deviceInfo: DeviceInfo
  private listeners: Map<string, Function[]> = new Map()
  private resizeObserver?: ResizeObserver
  private orientationChangeTimeout?: NodeJS.Timeout

  constructor() {
    this.deviceInfo = this.detectDevice()
    this.setupEventListeners()
    this.optimizeForDevice()
  }

  // MOBILE FIX: Comprehensive device detection
  private detectDevice(): DeviceInfo {
    const width = window.innerWidth
    const height = window.innerHeight
    
    // Determine device type
    let type: DeviceType = 'desktop'
    if (width < BREAKPOINTS.mobile) {
      type = 'mobile'
    } else if (width < BREAKPOINTS.tablet) {
      type = 'tablet'
    }

    // Determine orientation
    const orientation: Orientation = width > height ? 'landscape' : 'portrait'

    // Check touch support
    const touchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0

    // Check if low-end device
    const isLowEnd = this.detectLowEndDevice()

    // Get connection type
    const connection = (navigator as any).connection
    const connectionType = connection?.effectiveType

    return {
      type,
      orientation,
      touchSupport,
      screenSize: { width, height },
      pixelRatio: window.devicePixelRatio || 1,
      isLowEnd,
      connectionType
    }
  }

  // MOBILE FIX: Low-end device detection
  private detectLowEndDevice(): boolean {
    // Check device memory
    const deviceMemory = (navigator as any).deviceMemory
    if (deviceMemory && deviceMemory < 4) return true

    // Check hardware concurrency
    if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) return true

    // Check connection speed
    const connection = (navigator as any).connection
    if (connection && ['slow-2g', '2g'].includes(connection.effectiveType)) return true

    return false
  }

  // MOBILE FIX: Setup responsive event listeners
  private setupEventListeners() {
    // Resize listener with debouncing
    let resizeTimeout: NodeJS.Timeout
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout)
      resizeTimeout = setTimeout(() => {
        this.handleResize()
      }, 150)
    })

    // Orientation change listener
    window.addEventListener('orientationchange', () => {
      // Clear any existing timeout
      if (this.orientationChangeTimeout) {
        clearTimeout(this.orientationChangeTimeout)
      }

      // Wait for orientation change to complete
      this.orientationChangeTimeout = setTimeout(() => {
        this.handleOrientationChange()
      }, 300)
    })

    // Visibility change for performance optimization
    document.addEventListener('visibilitychange', () => {
      this.handleVisibilityChange()
    })

    // Touch events for mobile optimization
    if (this.deviceInfo.touchSupport) {
      this.setupTouchOptimizations()
    }
  }

  // MOBILE FIX: Handle resize events
  private handleResize() {
    const oldDeviceInfo = { ...this.deviceInfo }
    this.deviceInfo = this.detectDevice()

    // Check if device type changed
    if (oldDeviceInfo.type !== this.deviceInfo.type) {
      this.emit('deviceTypeChange', this.deviceInfo.type, oldDeviceInfo.type)
      this.optimizeForDevice()
    }

    // Check if orientation changed
    if (oldDeviceInfo.orientation !== this.deviceInfo.orientation) {
      this.emit('orientationChange', this.deviceInfo.orientation, oldDeviceInfo.orientation)
    }

    this.emit('resize', this.deviceInfo)
  }

  // MOBILE FIX: Handle orientation changes
  private handleOrientationChange() {
    // Force a resize check after orientation change
    this.handleResize()
    
    // Scroll to top to fix iOS Safari issues
    if (this.deviceInfo.type === 'mobile') {
      window.scrollTo(0, 0)
    }

    this.emit('orientationChange', this.deviceInfo.orientation)
  }

  // MOBILE FIX: Handle visibility changes for performance
  private handleVisibilityChange() {
    const isVisible = !document.hidden
    this.emit('visibilityChange', isVisible)

    // Pause/resume animations and timers based on visibility
    if (isVisible) {
      this.resumeOptimizations()
    } else {
      this.pauseOptimizations()
    }
  }

  // MOBILE FIX: Setup touch-specific optimizations
  private setupTouchOptimizations() {
    // Prevent double-tap zoom on buttons
    document.addEventListener('touchend', (e) => {
      const target = e.target as HTMLElement
      if (target.tagName === 'BUTTON' || target.closest('button')) {
        e.preventDefault()
      }
    })

    // Improve scroll performance
    document.addEventListener('touchstart', () => {
      // Add touch-active class for CSS optimizations
      document.body.classList.add('touch-active')
    }, { passive: true })

    document.addEventListener('touchend', () => {
      // Remove touch-active class
      setTimeout(() => {
        document.body.classList.remove('touch-active')
      }, 300)
    }, { passive: true })
  }

  // MOBILE FIX: Device-specific optimizations
  private optimizeForDevice() {
    const { type, isLowEnd, touchSupport } = this.deviceInfo

    // Add device classes to body
    document.body.className = document.body.className.replace(/device-\w+/g, '')
    document.body.classList.add(`device-${type}`)
    
    if (isLowEnd) {
      document.body.classList.add('device-low-end')
    }
    
    if (touchSupport) {
      document.body.classList.add('device-touch')
    }

    // Mobile-specific optimizations
    if (type === 'mobile') {
      this.applyMobileOptimizations()
    }

    // Low-end device optimizations
    if (isLowEnd) {
      this.applyLowEndOptimizations()
    }
  }

  // MOBILE FIX: Mobile-specific optimizations
  private applyMobileOptimizations() {
    // Prevent zoom on input focus (iOS Safari)
    const metaViewport = document.querySelector('meta[name="viewport"]')
    if (metaViewport) {
      metaViewport.setAttribute('content', 
        'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no'
      )
    }

    // Add mobile-specific CSS variables
    document.documentElement.style.setProperty('--mobile-safe-area-top', 'env(safe-area-inset-top)')
    document.documentElement.style.setProperty('--mobile-safe-area-bottom', 'env(safe-area-inset-bottom)')
    document.documentElement.style.setProperty('--mobile-safe-area-left', 'env(safe-area-inset-left)')
    document.documentElement.style.setProperty('--mobile-safe-area-right', 'env(safe-area-inset-right)')

    // Optimize scroll behavior
    document.body.style.overscrollBehavior = 'none'
    document.body.style.touchAction = 'pan-x pan-y'
  }

  // MOBILE FIX: Low-end device optimizations
  private applyLowEndOptimizations() {
    // Reduce animations
    document.body.classList.add('reduce-motion')
    
    // Disable expensive effects
    document.body.classList.add('disable-blur')
    document.body.classList.add('disable-shadows')
  }

  // MOBILE FIX: Pause optimizations when not visible
  private pauseOptimizations() {
    // Pause animations
    document.body.classList.add('paused')
  }

  // MOBILE FIX: Resume optimizations when visible
  private resumeOptimizations() {
    // Resume animations
    document.body.classList.remove('paused')
  }

  // Event system
  private emit(event: string, ...args: any[]) {
    const listeners = this.listeners.get(event) || []
    listeners.forEach(listener => listener(...args))
  }

  // Public API
  public on(event: string, listener: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(listener)
  }

  public off(event: string, listener: Function) {
    const listeners = this.listeners.get(event) || []
    const index = listeners.indexOf(listener)
    if (index > -1) {
      listeners.splice(index, 1)
    }
  }

  public getDeviceInfo(): DeviceInfo {
    return { ...this.deviceInfo }
  }

  public isMobile(): boolean {
    return this.deviceInfo.type === 'mobile'
  }

  public isTablet(): boolean {
    return this.deviceInfo.type === 'tablet'
  }

  public isDesktop(): boolean {
    return this.deviceInfo.type === 'desktop'
  }

  public isTouchDevice(): boolean {
    return this.deviceInfo.touchSupport
  }

  public isLowEndDevice(): boolean {
    return this.deviceInfo.isLowEnd
  }

  public isPortrait(): boolean {
    return this.deviceInfo.orientation === 'portrait'
  }

  public isLandscape(): boolean {
    return this.deviceInfo.orientation === 'landscape'
  }

  // MOBILE FIX: Get optimal image sizes for device
  public getOptimalImageSize(baseWidth: number, baseHeight: number): { width: number; height: number } {
    const { pixelRatio, type } = this.deviceInfo
    
    let scale = 1
    if (type === 'mobile') {
      scale = Math.min(pixelRatio, 2) // Cap at 2x for mobile
    } else {
      scale = pixelRatio
    }

    return {
      width: Math.round(baseWidth * scale),
      height: Math.round(baseHeight * scale)
    }
  }

  // MOBILE FIX: Get touch-friendly button size
  public getTouchButtonSize(): { minWidth: number; minHeight: number } {
    return this.deviceInfo.touchSupport 
      ? { minWidth: 44, minHeight: 44 } // iOS guidelines
      : { minWidth: 32, minHeight: 32 }
  }
}

// Export singleton instance
export const mobileOptimization = new MobileOptimizationManager()

// MOBILE FIX: React hook for mobile optimization
export function useMobileOptimization() {
  const [deviceInfo, setDeviceInfo] = React.useState(mobileOptimization.getDeviceInfo())

  React.useEffect(() => {
    const handleDeviceChange = (newDeviceInfo: DeviceInfo) => {
      setDeviceInfo(newDeviceInfo)
    }

    mobileOptimization.on('resize', handleDeviceChange)
    mobileOptimization.on('orientationChange', handleDeviceChange)

    return () => {
      mobileOptimization.off('resize', handleDeviceChange)
      mobileOptimization.off('orientationChange', handleDeviceChange)
    }
  }, [])

  return {
    deviceInfo,
    isMobile: mobileOptimization.isMobile(),
    isTablet: mobileOptimization.isTablet(),
    isDesktop: mobileOptimization.isDesktop(),
    isTouchDevice: mobileOptimization.isTouchDevice(),
    isLowEndDevice: mobileOptimization.isLowEndDevice(),
    isPortrait: mobileOptimization.isPortrait(),
    isLandscape: mobileOptimization.isLandscape(),
    getOptimalImageSize: mobileOptimization.getOptimalImageSize.bind(mobileOptimization),
    getTouchButtonSize: mobileOptimization.getTouchButtonSize.bind(mobileOptimization)
  }
}

export default mobileOptimization
