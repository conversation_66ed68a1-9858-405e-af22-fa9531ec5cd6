from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from .views import (
    SupportTicketCategoryViewSet, SupportAgentViewSet, ServiceLevelAgreementViewSet,
    SupportTicketViewSet, TicketCommentViewSet, KnowledgeBaseCategoryViewSet,
    KnowledgeBaseArticleViewSet, CustomerFeedbackViewSet, LiveChatSessionViewSet,
    ChatMessageViewSet, SupportDashboardViewSet
)
from .views_advanced import (
    TicketRoutingRuleViewSet, EscalationRuleViewSet, AIAssistantViewSet,
    AIInteractionViewSet, WorkflowAutomationViewSet, WorkflowExecutionViewSet,
    IntegrationConnectorViewSet, CustomerTierViewSet, CustomerProfileViewSet,
    AdvancedAnalyticsViewSet
)
from .views_public import PublicChatViewSet, CustomerChatViewSet
from .views_live_chat import LiveChatSessionViewSet, start_public_chat, send_public_message, get_public_messages

router = DefaultRouter()
router.register(r'ticket-categories', SupportTicketCategoryViewSet)
router.register(r'agents', SupportAgentViewSet)
router.register(r'sla', ServiceLevelAgreementViewSet)
router.register(r'tickets', SupportTicketViewSet)
router.register(r'ticket-comments', TicketCommentViewSet)
router.register(r'kb-categories', KnowledgeBaseCategoryViewSet)
router.register(r'kb-articles', KnowledgeBaseArticleViewSet)
router.register(r'feedback', CustomerFeedbackViewSet)
router.register(r'chat-sessions', LiveChatSessionViewSet)
router.register(r'chat-messages', ChatMessageViewSet)
router.register(r'dashboard', SupportDashboardViewSet, basename='support-dashboard')

# Advanced features
router.register(r'routing-rules', TicketRoutingRuleViewSet)
router.register(r'escalation-rules', EscalationRuleViewSet)
router.register(r'ai-assistants', AIAssistantViewSet)
router.register(r'ai-interactions', AIInteractionViewSet)
router.register(r'workflows', WorkflowAutomationViewSet)
router.register(r'workflow-executions', WorkflowExecutionViewSet)
router.register(r'integrations', IntegrationConnectorViewSet)
router.register(r'customer-tiers', CustomerTierViewSet)
router.register(r'customer-profiles', CustomerProfileViewSet)
router.register(r'advanced-analytics', AdvancedAnalyticsViewSet, basename='advanced-analytics')

# Public and customer chat endpoints
router.register(r'public-chat', PublicChatViewSet, basename='public-chat')
router.register(r'customer-chat', CustomerChatViewSet, basename='customer-chat')

# Live chat endpoints
router.register(r'live-chat-sessions', LiveChatSessionViewSet, basename='live-chat-sessions')

urlpatterns = [
    path('api/customer-service/', include(router.urls)),
    # Simple chat API for all users
    path('api/chat/', include('customer_service.simple_urls')),
    # Live chat public endpoints
    path('api/live-chat/start/', start_public_chat, name='start_public_chat'),
    path('api/live-chat/send/', send_public_message, name='send_public_message'),
    path('api/live-chat/messages/', get_public_messages, name='get_public_messages'),
]
