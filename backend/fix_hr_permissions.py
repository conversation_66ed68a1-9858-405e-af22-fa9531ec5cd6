#!/usr/bin/env python
"""
Quick script to fix HR Manager permissions
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ems_project.settings')
django.setup()

from ems.models import Role

def fix_hr_permissions():
    print("🔧 Fixing HR Manager permissions...")
    
    try:
        # Get HR_MANAGER role
        hr_role = Role.objects.get(name='HR_MANAGER')
        print(f"Found HR_MANAGER role: {hr_role}")
        
        # Update permissions
        hr_role.permissions = {
            # HR Management permissions
            'hr_management': True,
            'view_all_employees': True,
            'manage_employees': True,
            'manage_leave_requests': True,
            'view_attendance': True,
            'performance_management': True,
            'create_announcements': True,
            'edit_announcements': True,
            'export_hr_data': True,
            
            # Report permissions
            'create_reports': True,
            'edit_reports': True,
            'delete_reports': True,
            'view_reports': True,
            'export_reports': True,
            
            # General permissions
            'level': 5,
            'full_hr_access': True
        }
        
        hr_role.save()
        print("✅ HR Manager permissions updated successfully!")
        print(f"New permissions: {hr_role.permissions}")
        
        return True
        
    except Role.DoesNotExist:
        print("❌ HR_MANAGER role not found!")
        return False
    except Exception as e:
        print(f"❌ Error updating permissions: {e}")
        return False

if __name__ == '__main__':
    fix_hr_permissions()
