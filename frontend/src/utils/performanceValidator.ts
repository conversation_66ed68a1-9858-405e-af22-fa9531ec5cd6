/**
 * PERFORMANCE FIX: Comprehensive Performance Validator
 * Validates all performance optimizations and provides detailed reports
 */

import React from 'react'
import { getPerformanceMonitor } from './performanceMonitor'
import { criticalPerformanceMonitor } from './criticalPerformanceMonitor'
import { AdvancedCache, ResourceMonitor } from './finalPerformanceOptimizations'

export interface PerformanceValidationResult {
  score: number
  grade: 'A+' | 'A' | 'B+' | 'B' | 'C+' | 'C' | 'D' | 'F'
  issues: PerformanceIssue[]
  recommendations: string[]
  metrics: PerformanceMetrics
  timestamp: Date
}

export interface PerformanceIssue {
  category: 'critical' | 'high' | 'medium' | 'low'
  type: string
  description: string
  impact: string
  fix: string
  value?: number
  threshold?: number
}

export interface PerformanceMetrics {
  // Core Web Vitals
  lcp?: number // Largest Contentful Paint
  fid?: number // First Input Delay
  cls?: number // Cumulative Layout Shift
  fcp?: number // First Contentful Paint
  ttfb?: number // Time to First Byte

  // Application Metrics
  loadTime: number
  renderTime: number
  memoryUsage: number
  bundleSize: number
  apiResponseTime: number
  resourceCount: number
  cacheHitRate: number

  // Mobile Metrics
  mobileScore: number
  touchOptimization: number
  responsiveDesign: number

  // Security Metrics
  securityScore: number
  csrfProtection: boolean
  authenticationSecurity: boolean
}

class PerformanceValidator {
  private validationResults: PerformanceValidationResult[] = []

  // PERFORMANCE FIX: Comprehensive validation
  async validatePerformance(): Promise<PerformanceValidationResult> {
    const startTime = performance.now()
    
    console.log('🔍 Starting comprehensive performance validation...')

    const metrics = await this.collectMetrics()
    const issues = this.analyzeIssues(metrics)
    const recommendations = this.generateRecommendations(issues)
    const score = this.calculateScore(metrics, issues)
    const grade = this.calculateGrade(score)

    const result: PerformanceValidationResult = {
      score,
      grade,
      issues,
      recommendations,
      metrics,
      timestamp: new Date()
    }

    this.validationResults.push(result)
    
    const validationTime = performance.now() - startTime
    console.log(`✅ Performance validation completed in ${validationTime.toFixed(2)}ms`)
    
    return result
  }

  // Collect comprehensive metrics
  private async collectMetrics(): Promise<PerformanceMetrics> {
    const performanceMonitor = getPerformanceMonitor()
    const webVitals = performanceMonitor.getMetrics()
    const criticalMetrics = criticalPerformanceMonitor.getMetrics()
    const cacheStats = AdvancedCache.getStats()

    // Measure load time
    const loadTime = performance.timing 
      ? performance.timing.loadEventEnd - performance.timing.navigationStart
      : 0

    // Measure render time
    const renderTime = this.measureRenderTime()

    // Measure memory usage
    const memoryUsage = this.getMemoryUsage()

    // Measure bundle size
    const bundleSize = await this.estimateBundleSize()

    // Measure API response time
    const apiResponseTime = this.getAverageApiResponseTime()

    // Get resource count
    const resourceCount = ResourceMonitor.getResourceCount()

    // Calculate cache hit rate
    const cacheHitRate = cacheStats.totalHits > 0 
      ? (cacheStats.totalHits / (cacheStats.totalHits + cacheStats.size)) * 100
      : 0

    // Mobile optimization scores
    const mobileScore = this.calculateMobileScore()
    const touchOptimization = this.calculateTouchOptimization()
    const responsiveDesign = this.calculateResponsiveDesign()

    // Security scores
    const securityScore = this.calculateSecurityScore()
    const csrfProtection = this.checkCSRFProtection()
    const authenticationSecurity = this.checkAuthenticationSecurity()

    return {
      lcp: webVitals.lcp,
      fid: webVitals.fid,
      cls: webVitals.cls,
      fcp: webVitals.fcp,
      ttfb: webVitals.ttfb,
      loadTime,
      renderTime,
      memoryUsage,
      bundleSize,
      apiResponseTime,
      resourceCount,
      cacheHitRate,
      mobileScore,
      touchOptimization,
      responsiveDesign,
      securityScore,
      csrfProtection,
      authenticationSecurity
    }
  }

  // Analyze performance issues
  private analyzeIssues(metrics: PerformanceMetrics): PerformanceIssue[] {
    const issues: PerformanceIssue[] = []

    // Core Web Vitals analysis
    if (metrics.lcp && metrics.lcp > 2500) {
      issues.push({
        category: metrics.lcp > 4000 ? 'critical' : 'high',
        type: 'LCP',
        description: `Largest Contentful Paint is ${metrics.lcp.toFixed(0)}ms`,
        impact: 'Poor user experience and SEO ranking',
        fix: 'Optimize images, reduce server response time, eliminate render-blocking resources',
        value: metrics.lcp,
        threshold: 2500
      })
    }

    if (metrics.fid && metrics.fid > 100) {
      issues.push({
        category: metrics.fid > 300 ? 'critical' : 'high',
        type: 'FID',
        description: `First Input Delay is ${metrics.fid.toFixed(0)}ms`,
        impact: 'Poor interactivity and user experience',
        fix: 'Reduce JavaScript execution time, code splitting, optimize event handlers',
        value: metrics.fid,
        threshold: 100
      })
    }

    if (metrics.cls && metrics.cls > 0.1) {
      issues.push({
        category: metrics.cls > 0.25 ? 'critical' : 'medium',
        type: 'CLS',
        description: `Cumulative Layout Shift is ${metrics.cls.toFixed(3)}`,
        impact: 'Visual instability and poor user experience',
        fix: 'Set dimensions for images and videos, avoid inserting content above existing content',
        value: metrics.cls,
        threshold: 0.1
      })
    }

    // Application performance analysis
    if (metrics.loadTime > 3000) {
      issues.push({
        category: 'high',
        type: 'Load Time',
        description: `Page load time is ${(metrics.loadTime / 1000).toFixed(1)}s`,
        impact: 'Slow initial page load affects user engagement',
        fix: 'Implement code splitting, optimize bundle size, use CDN',
        value: metrics.loadTime,
        threshold: 3000
      })
    }

    if (metrics.memoryUsage > 100) {
      issues.push({
        category: 'medium',
        type: 'Memory Usage',
        description: `Memory usage is ${metrics.memoryUsage.toFixed(0)}MB`,
        impact: 'High memory usage can cause performance issues on low-end devices',
        fix: 'Implement proper cleanup, optimize data structures, use lazy loading',
        value: metrics.memoryUsage,
        threshold: 100
      })
    }

    if (metrics.bundleSize > 1024 * 1024) { // 1MB
      issues.push({
        category: 'medium',
        type: 'Bundle Size',
        description: `Bundle size is ${(metrics.bundleSize / 1024 / 1024).toFixed(1)}MB`,
        impact: 'Large bundle size increases load time, especially on slow networks',
        fix: 'Implement code splitting, tree shaking, remove unused dependencies',
        value: metrics.bundleSize,
        threshold: 1024 * 1024
      })
    }

    if (metrics.apiResponseTime > 500) {
      issues.push({
        category: 'high',
        type: 'API Response Time',
        description: `Average API response time is ${metrics.apiResponseTime.toFixed(0)}ms`,
        impact: 'Slow API responses affect user experience and perceived performance',
        fix: 'Optimize database queries, implement caching, use CDN for API',
        value: metrics.apiResponseTime,
        threshold: 500
      })
    }

    if (metrics.resourceCount > 20) {
      issues.push({
        category: 'medium',
        type: 'Resource Count',
        description: `${metrics.resourceCount} active resources detected`,
        impact: 'High resource count can indicate memory leaks',
        fix: 'Implement proper cleanup, use resource monitoring, optimize component lifecycle',
        value: metrics.resourceCount,
        threshold: 20
      })
    }

    return issues
  }

  // Generate recommendations
  private generateRecommendations(issues: PerformanceIssue[]): string[] {
    const recommendations: string[] = []
    const categories = new Set(issues.map(issue => issue.type))

    if (categories.has('LCP')) {
      recommendations.push('Implement image optimization and lazy loading')
      recommendations.push('Use a Content Delivery Network (CDN)')
      recommendations.push('Optimize server response times')
    }

    if (categories.has('FID')) {
      recommendations.push('Implement code splitting and lazy loading')
      recommendations.push('Optimize JavaScript execution')
      recommendations.push('Use web workers for heavy computations')
    }

    if (categories.has('CLS')) {
      recommendations.push('Set explicit dimensions for media elements')
      recommendations.push('Avoid inserting content above existing content')
      recommendations.push('Use CSS transforms instead of changing layout properties')
    }

    if (categories.has('Memory Usage')) {
      recommendations.push('Implement proper component cleanup')
      recommendations.push('Use React.memo and useMemo for optimization')
      recommendations.push('Monitor and fix memory leaks')
    }

    if (categories.has('Bundle Size')) {
      recommendations.push('Implement dynamic imports and code splitting')
      recommendations.push('Remove unused dependencies')
      recommendations.push('Use tree shaking and minification')
    }

    return recommendations
  }

  // Calculate overall performance score
  private calculateScore(metrics: PerformanceMetrics, issues: PerformanceIssue[]): number {
    let score = 100

    // Deduct points for issues
    issues.forEach(issue => {
      switch (issue.category) {
        case 'critical':
          score -= 20
          break
        case 'high':
          score -= 15
          break
        case 'medium':
          score -= 10
          break
        case 'low':
          score -= 5
          break
      }
    })

    // Bonus points for good metrics
    if (metrics.lcp && metrics.lcp < 1500) score += 5
    if (metrics.fid && metrics.fid < 50) score += 5
    if (metrics.cls && metrics.cls < 0.05) score += 5
    if (metrics.cacheHitRate > 80) score += 5
    if (metrics.mobileScore > 90) score += 5

    return Math.max(0, Math.min(100, score))
  }

  // Calculate grade based on score
  private calculateGrade(score: number): 'A+' | 'A' | 'B+' | 'B' | 'C+' | 'C' | 'D' | 'F' {
    if (score >= 97) return 'A+'
    if (score >= 93) return 'A'
    if (score >= 90) return 'B+'
    if (score >= 87) return 'B'
    if (score >= 83) return 'C+'
    if (score >= 80) return 'C'
    if (score >= 70) return 'D'
    return 'F'
  }

  // Helper methods for metric collection
  private measureRenderTime(): number {
    const paintEntries = performance.getEntriesByType('paint')
    const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint')
    return fcp ? fcp.startTime : 0
  }

  private getMemoryUsage(): number {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return memory.usedJSHeapSize / 1024 / 1024 // Convert to MB
    }
    return 0
  }

  private async estimateBundleSize(): Promise<number> {
    // Estimate based on loaded resources
    const resources = performance.getEntriesByType('resource')
    return resources
      .filter(resource => resource.name.includes('.js') || resource.name.includes('.css'))
      .reduce((total, resource) => total + (resource as any).transferSize || 0, 0)
  }

  private getAverageApiResponseTime(): number {
    const navigationEntries = performance.getEntriesByType('navigation')
    if (navigationEntries.length > 0) {
      const nav = navigationEntries[0] as PerformanceNavigationTiming
      return nav.responseEnd - nav.requestStart
    }
    return 0
  }

  private calculateMobileScore(): number {
    // Check mobile optimizations
    let score = 0
    
    // Check viewport meta tag
    const viewport = document.querySelector('meta[name="viewport"]')
    if (viewport) score += 20
    
    // Check touch-friendly elements
    const buttons = document.querySelectorAll('button')
    const touchFriendly = Array.from(buttons).every(button => {
      const rect = button.getBoundingClientRect()
      return rect.width >= 44 && rect.height >= 44
    })
    if (touchFriendly) score += 30
    
    // Check responsive design
    if (window.innerWidth < 768) {
      const hasResponsiveLayout = document.body.classList.contains('device-mobile')
      if (hasResponsiveLayout) score += 50
    } else {
      score += 50 // Desktop gets full points
    }
    
    return score
  }

  private calculateTouchOptimization(): number {
    // Check touch optimizations
    const hasTouchClass = document.body.classList.contains('device-touch')
    const hasTouchStyles = document.querySelector('style[data-touch-optimized]')
    
    return (hasTouchClass ? 50 : 0) + (hasTouchStyles ? 50 : 0)
  }

  private calculateResponsiveDesign(): number {
    // Check responsive design implementation
    const hasResponsiveClasses = document.body.classList.contains('device-mobile') ||
                                 document.body.classList.contains('device-tablet') ||
                                 document.body.classList.contains('device-desktop')
    
    const hasResponsiveCSS = document.querySelector('link[href*="mobile-responsive"]')
    
    return (hasResponsiveClasses ? 50 : 0) + (hasResponsiveCSS ? 50 : 0)
  }

  private calculateSecurityScore(): number {
    // Check security implementations
    let score = 0
    
    // Check CSRF protection
    if (this.checkCSRFProtection()) score += 30
    
    // Check authentication security
    if (this.checkAuthenticationSecurity()) score += 30
    
    // Check HTTPS
    if (location.protocol === 'https:') score += 20
    
    // Check secure headers
    const hasSecureHeaders = document.querySelector('meta[http-equiv="Content-Security-Policy"]')
    if (hasSecureHeaders) score += 20
    
    return score
  }

  private checkCSRFProtection(): boolean {
    // Check if CSRF token utilities are available
    try {
      return typeof window !== 'undefined' && 
             document.cookie.includes('csrftoken')
    } catch {
      return false
    }
  }

  private checkAuthenticationSecurity(): boolean {
    // Check if secure authentication is implemented
    try {
      return typeof window !== 'undefined' && 
             (document.cookie.includes('sessionid') || 
              document.cookie.includes('auth-token'))
    } catch {
      return false
    }
  }

  // Get validation history
  getValidationHistory(): PerformanceValidationResult[] {
    return [...this.validationResults]
  }

  // Generate performance report
  generateReport(result: PerformanceValidationResult): string {
    const { score, grade, issues, recommendations, metrics } = result
    
    let report = `
🎯 PERFORMANCE VALIDATION REPORT
================================

Overall Score: ${score}/100 (Grade: ${grade})
Timestamp: ${result.timestamp.toISOString()}

📊 CORE METRICS:
- Load Time: ${(metrics.loadTime / 1000).toFixed(1)}s
- Memory Usage: ${metrics.memoryUsage.toFixed(1)}MB
- Bundle Size: ${(metrics.bundleSize / 1024 / 1024).toFixed(1)}MB
- API Response Time: ${metrics.apiResponseTime.toFixed(0)}ms
- Resource Count: ${metrics.resourceCount}
- Cache Hit Rate: ${metrics.cacheHitRate.toFixed(1)}%

🌐 CORE WEB VITALS:
- LCP: ${metrics.lcp ? `${metrics.lcp.toFixed(0)}ms` : 'N/A'}
- FID: ${metrics.fid ? `${metrics.fid.toFixed(0)}ms` : 'N/A'}
- CLS: ${metrics.cls ? metrics.cls.toFixed(3) : 'N/A'}

📱 MOBILE OPTIMIZATION:
- Mobile Score: ${metrics.mobileScore}/100
- Touch Optimization: ${metrics.touchOptimization}/100
- Responsive Design: ${metrics.responsiveDesign}/100

🔒 SECURITY:
- Security Score: ${metrics.securityScore}/100
- CSRF Protection: ${metrics.csrfProtection ? '✅' : '❌'}
- Authentication Security: ${metrics.authenticationSecurity ? '✅' : '❌'}

⚠️ ISSUES FOUND (${issues.length}):
${issues.map(issue => `- ${issue.category.toUpperCase()}: ${issue.description}`).join('\n')}

💡 RECOMMENDATIONS:
${recommendations.map(rec => `- ${rec}`).join('\n')}

${grade === 'A+' ? '🎉 EXCELLENT PERFORMANCE!' : 
  grade.startsWith('A') ? '✅ GOOD PERFORMANCE' :
  grade.startsWith('B') ? '⚠️ NEEDS IMPROVEMENT' :
  '🚨 PERFORMANCE ISSUES DETECTED'}
`
    
    return report
  }
}

// Export singleton instance
export const performanceValidator = new PerformanceValidator()

// React hook for performance validation
export function usePerformanceValidation() {
  const [isValidating, setIsValidating] = React.useState(false)
  const [lastResult, setLastResult] = React.useState<PerformanceValidationResult | null>(null)

  const validate = React.useCallback(async () => {
    setIsValidating(true)
    try {
      const result = await performanceValidator.validatePerformance()
      setLastResult(result)
      return result
    } finally {
      setIsValidating(false)
    }
  }, [])

  return {
    validate,
    isValidating,
    lastResult,
    history: performanceValidator.getValidationHistory()
  }
}

export default performanceValidator
