"""
Advanced Customer Service Views
AI integration, automation, and advanced analytics
"""

from django.shortcuts import render
from django.db.models import Count, Avg, Q, F
from django.utils import timezone
from datetime import timedelta
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .models_advanced import (
    TicketRoutingRule, EscalationRule, AIAssistant, AIInteraction,
    WorkflowAutomation, WorkflowExecution, IntegrationConnector,
    CustomerTier, CustomerProfile
)
from .services import TicketRoutingService, AIAssistantService, WorkflowService
from .serializers_advanced import (
    TicketRoutingRuleSerializer, EscalationRuleSerializer, AIAssistantSerializer,
    AIInteractionSerializer, WorkflowAutomationSerializer, WorkflowExecutionSerializer,
    IntegrationConnectorSerializer, CustomerTierSerializer, CustomerProfileSerializer
)

class TicketRoutingRuleViewSet(viewsets.ModelViewSet):
    queryset = TicketRoutingRule.objects.all()
    serializer_class = TicketRoutingRuleSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=True, methods=['post'])
    def test_rule(self, request, pk=None):
        """Test routing rule against sample data"""
        rule = self.get_object()
        
        # This would test the rule against sample tickets
        test_results = {
            'rule_name': rule.name,
            'matches': 0,
            'total_tested': 0,
            'sample_matches': []
        }
        
        return Response(test_results)

class EscalationRuleViewSet(viewsets.ModelViewSet):
    queryset = EscalationRule.objects.all()
    serializer_class = EscalationRuleSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['post'])
    def trigger_escalation_check(self, request):
        """Manually trigger escalation check"""
        from .services import EscalationService
        
        try:
            EscalationService.check_escalations()
            return Response({'message': 'Escalation check completed successfully'})
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class AIAssistantViewSet(viewsets.ModelViewSet):
    queryset = AIAssistant.objects.all()
    serializer_class = AIAssistantSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=True, methods=['post'])
    def chat(self, request, pk=None):
        """Chat with AI assistant"""
        assistant = self.get_object()
        user_input = request.data.get('message', '')
        context = request.data.get('context', {})
        
        if not user_input:
            return Response({'error': 'Message is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            response_data = AIAssistantService.get_ai_response(assistant, user_input, context)
            return Response(response_data)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'])
    def analytics(self, request, pk=None):
        """Get AI assistant analytics"""
        assistant = self.get_object()
        
        # Get interaction statistics
        interactions = AIInteraction.objects.filter(assistant=assistant)
        
        analytics_data = {
            'total_interactions': interactions.count(),
            'helpful_interactions': interactions.filter(was_helpful=True).count(),
            'escalated_interactions': interactions.filter(escalated_to_human=True).count(),
            'average_confidence': interactions.aggregate(avg_confidence=Avg('confidence_score'))['avg_confidence'] or 0,
            'success_rate': assistant.success_rate,
            'recent_interactions': interactions.order_by('-created_at')[:10].values(
                'user_input', 'ai_response', 'confidence_score', 'was_helpful', 'created_at'
            )
        }
        
        return Response(analytics_data)

    @action(detail=False, methods=['post'])
    def analyze_sentiment(self, request):
        """Analyze sentiment of text"""
        text = request.data.get('text', '')
        
        if not text:
            return Response({'error': 'Text is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        sentiment_data = AIAssistantService.analyze_sentiment(text)
        return Response(sentiment_data)

class AIInteractionViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = AIInteraction.objects.all()
    serializer_class = AIInteractionSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=True, methods=['post'])
    def provide_feedback(self, request, pk=None):
        """Provide feedback on AI interaction"""
        interaction = self.get_object()
        
        was_helpful = request.data.get('was_helpful')
        feedback = request.data.get('feedback', '')
        
        interaction.was_helpful = was_helpful
        interaction.user_feedback = feedback
        interaction.save()
        
        # Update assistant statistics
        if was_helpful is True:
            interaction.assistant.successful_resolutions += 1
            interaction.assistant.save()
        
        return Response({'message': 'Feedback recorded successfully'})

class WorkflowAutomationViewSet(viewsets.ModelViewSet):
    queryset = WorkflowAutomation.objects.all()
    serializer_class = WorkflowAutomationSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=True, methods=['post'])
    def test_workflow(self, request, pk=None):
        """Test workflow with sample data"""
        workflow = self.get_object()
        
        # This would test the workflow with sample data
        test_results = {
            'workflow_name': workflow.name,
            'steps_tested': 0,
            'success': True,
            'execution_time_ms': 0
        }
        
        return Response(test_results)

    @action(detail=True, methods=['get'])
    def execution_history(self, request, pk=None):
        """Get workflow execution history"""
        workflow = self.get_object()
        
        executions = WorkflowExecution.objects.filter(workflow=workflow).order_by('-started_at')[:20]
        serializer = WorkflowExecutionSerializer(executions, many=True)
        
        return Response(serializer.data)

class WorkflowExecutionViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = WorkflowExecution.objects.all()
    serializer_class = WorkflowExecutionSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=True, methods=['post'])
    def retry(self, request, pk=None):
        """Retry failed workflow execution"""
        execution = self.get_object()
        
        if execution.status != 'failed':
            return Response({'error': 'Can only retry failed executions'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Create new execution
        new_execution = WorkflowExecution.objects.create(
            workflow=execution.workflow,
            related_ticket=execution.related_ticket,
            execution_data=execution.execution_data
        )
        
        # Execute workflow
        try:
            WorkflowService._execute_workflow(execution.workflow, execution.related_ticket, {})
            return Response({'message': 'Workflow retry initiated', 'execution_id': new_execution.id})
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class IntegrationConnectorViewSet(viewsets.ModelViewSet):
    queryset = IntegrationConnector.objects.all()
    serializer_class = IntegrationConnectorSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=True, methods=['post'])
    def test_connection(self, request, pk=None):
        """Test integration connection"""
        connector = self.get_object()
        
        # This would test the actual connection
        test_result = {
            'connector_name': connector.name,
            'connection_status': 'success',
            'response_time_ms': 150,
            'last_tested': timezone.now().isoformat()
        }
        
        return Response(test_result)

    @action(detail=True, methods=['post'])
    def sync_now(self, request, pk=None):
        """Trigger immediate sync"""
        connector = self.get_object()
        
        # This would trigger the actual sync
        connector.last_sync_at = timezone.now()
        connector.last_sync_status = 'success'
        connector.total_syncs += 1
        connector.save()
        
        return Response({'message': 'Sync initiated successfully'})

class CustomerTierViewSet(viewsets.ModelViewSet):
    queryset = CustomerTier.objects.all()
    serializer_class = CustomerTierSerializer
    permission_classes = [IsAuthenticated]

class CustomerProfileViewSet(viewsets.ModelViewSet):
    queryset = CustomerProfile.objects.all()
    serializer_class = CustomerProfileSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=True, methods=['get'])
    def support_history(self, request, pk=None):
        """Get customer support history"""
        profile = self.get_object()
        
        from .models import SupportTicket, CustomerFeedback
        
        # Get tickets
        tickets = SupportTicket.objects.filter(customer=profile.user).order_by('-created_at')[:10]
        
        # Get feedback
        feedback = CustomerFeedback.objects.filter(customer=profile.user).order_by('-created_at')[:5]
        
        history_data = {
            'total_tickets': profile.total_tickets,
            'resolved_tickets': profile.resolved_tickets,
            'average_satisfaction': profile.average_satisfaction,
            'recent_tickets': [
                {
                    'id': ticket.id,
                    'ticket_id': ticket.ticket_id,
                    'title': ticket.title,
                    'status': ticket.status,
                    'priority': ticket.priority,
                    'created_at': ticket.created_at,
                    'resolved_at': ticket.resolved_at
                }
                for ticket in tickets
            ],
            'recent_feedback': [
                {
                    'id': fb.id,
                    'overall_rating': fb.overall_rating,
                    'comments': fb.comments,
                    'created_at': fb.created_at
                }
                for fb in feedback
            ]
        }
        
        return Response(history_data)

# Advanced Analytics Views
class AdvancedAnalyticsViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'])
    def automation_metrics(self, request):
        """Get automation and AI metrics"""
        
        # Routing effectiveness
        routing_rules = TicketRoutingRule.objects.filter(is_active=True)
        
        # AI assistant performance
        ai_assistants = AIAssistant.objects.filter(is_active=True)
        
        # Workflow automation stats
        workflows = WorkflowAutomation.objects.filter(is_active=True)
        
        metrics = {
            'routing_rules': {
                'total_active': routing_rules.count(),
                'rules_by_type': dict(routing_rules.values_list('condition_type').annotate(count=Count('id')))
            },
            'ai_assistants': {
                'total_active': ai_assistants.count(),
                'total_interactions': sum(ai.total_interactions for ai in ai_assistants),
                'average_success_rate': sum(ai.success_rate for ai in ai_assistants) / ai_assistants.count() if ai_assistants.count() > 0 else 0
            },
            'workflows': {
                'total_active': workflows.count(),
                'total_executions': sum(wf.total_executions for wf in workflows),
                'average_success_rate': sum(wf.success_rate for wf in workflows) / workflows.count() if workflows.count() > 0 else 0
            },
            'integrations': {
                'total_active': IntegrationConnector.objects.filter(is_active=True).count(),
                'sync_success_rate': 95.5  # This would be calculated from actual sync data
            }
        }
        
        return Response(metrics)

    @action(detail=False, methods=['get'])
    def customer_insights(self, request):
        """Get customer behavior insights"""
        
        # Customer tier distribution
        tier_distribution = CustomerProfile.objects.values('customer_tier__tier_level').annotate(
            count=Count('id')
        )
        
        # Satisfaction trends
        from .models import CustomerFeedback
        satisfaction_trends = CustomerFeedback.objects.filter(
            created_at__gte=timezone.now() - timedelta(days=30)
        ).values('created_at__date').annotate(
            avg_rating=Avg('overall_rating')
        ).order_by('created_at__date')
        
        insights = {
            'customer_tiers': {
                'distribution': list(tier_distribution),
                'total_customers': CustomerProfile.objects.count()
            },
            'satisfaction_trends': list(satisfaction_trends),
            'contact_preferences': dict(
                CustomerProfile.objects.values_list('preferred_contact_method').annotate(count=Count('id'))
            ),
            'language_preferences': dict(
                CustomerProfile.objects.values_list('preferred_language').annotate(count=Count('id'))
            )
        }
        
        return Response(insights)
