"""
Custom permission classes for role-based access control
"""

from rest_framework.permissions import Base<PERSON>ermission
from rest_framework import status
from rest_framework.response import Response
from django.core.exceptions import PermissionDenied


class RoleBasedPermission(BasePermission):
    """
    Base permission class for role-based access control
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Get user role
        try:
            user_profile = request.user.userprofile
            user_role = user_profile.role
            if not user_role:
                return False
        except:
            return False
        
        # Check if user has required permissions
        return self.check_role_permissions(request, view, user_role)
    
    def check_role_permissions(self, request, view, user_role):
        """Override this method in subclasses"""
        return True


class EmployeePermission(RoleBasedPermission):
    """
    Permission class for Employee role - most restrictive
    """
    
    def check_role_permissions(self, request, view, user_role):
        # Only allow employees to access employee-specific endpoints
        if user_role.name != 'EMPLOYEE':
            return True  # Other roles handled by their own permissions
        
        # Employee restrictions
        employee_permissions = user_role.permissions or {}
        
        # Check specific permissions based on the view/action
        view_name = getattr(view, 'basename', '') or view.__class__.__name__.lower()
        action = getattr(view, 'action', request.method.lower())
        
        # Employee can only perform specific actions
        if view_name in ['task', 'employeetask']:
            # Tasks: Can view own tasks and mark complete only
            if action in ['list', 'retrieve']:
                return employee_permissions.get('view_own_tasks', False)
            elif action in ['update', 'partial_update']:
                # Only allow status updates (mark complete)
                return employee_permissions.get('update_task_status', False)
            else:
                return False  # No create, delete
        
        elif view_name in ['project']:
            # Projects: Read-only access to assigned projects
            if action in ['list', 'retrieve']:
                return employee_permissions.get('view_assigned_projects', False)
            else:
                return False  # No create, edit, delete
        
        elif view_name in ['document']:
            # Documents: Read-only access to public documents
            if action in ['list', 'retrieve']:
                return employee_permissions.get('view_public_documents', False)
            else:
                return False  # No create, edit, delete
        
        elif view_name in ['announcement']:
            # Announcements: Read-only access
            if action in ['list', 'retrieve']:
                return employee_permissions.get('view_company_announcements', False)
            else:
                return False  # No create, edit, delete
        
        elif view_name in ['leaverequest']:
            # Leave: Can submit and view own requests
            if action in ['list', 'retrieve', 'create']:
                return employee_permissions.get('submit_leave_requests', False)
            elif action in ['update', 'partial_update']:
                # Can only update own pending requests
                return employee_permissions.get('submit_leave_requests', False)
            else:
                return False
        
        elif view_name in ['userprofile', 'employee']:
            # Profile: Can view and edit own profile only
            if action in ['retrieve', 'update', 'partial_update']:
                return employee_permissions.get('view_own_profile', False)
            else:
                return False  # No list all, create, delete
        
        # Default deny for employees
        return False


class HRManagerPermission(RoleBasedPermission):
    """
    Permission class for HR Manager role
    """
    
    def check_role_permissions(self, request, view, user_role):
        if user_role.name != 'HR_MANAGER':
            return True  # Other roles handled by their own permissions
        
        hr_permissions = user_role.permissions or {}
        
        # HR Managers have broader access to HR-related endpoints
        view_name = getattr(view, 'basename', '') or view.__class__.__name__.lower()
        
        if view_name in ['employee', 'userprofile']:
            return hr_permissions.get('view_all_employees', False)
        elif view_name in ['leaverequest']:
            return hr_permissions.get('manage_leave_requests', False)
        elif view_name in ['attendance']:
            return hr_permissions.get('view_attendance', False)
        elif view_name in ['announcement']:
            return hr_permissions.get('create_announcements', False)
        
        return True  # Allow other operations


class AdminPermission(RoleBasedPermission):
    """
    Permission class for Admin and Super Admin roles
    """
    
    def check_role_permissions(self, request, view, user_role):
        # Super Admin and Admin have full access
        if user_role.name in ['SUPERADMIN', 'ADMIN']:
            return True
        
        return True  # Other roles handled by their own permissions


class IsOwnerOrReadOnly(BasePermission):
    """
    Custom permission to only allow owners of an object to edit it.
    """
    
    def has_object_permission(self, request, view, obj):
        # Read permissions for any request
        if request.method in ['GET', 'HEAD', 'OPTIONS']:
            return True
        
        # Write permissions only to the owner
        if hasattr(obj, 'user'):
            return obj.user == request.user
        elif hasattr(obj, 'employee') and hasattr(obj.employee, 'user'):
            return obj.employee.user == request.user
        elif hasattr(obj, 'created_by') and hasattr(obj.created_by, 'user'):
            return obj.created_by.user == request.user
        
        return False


class EmployeeDataPermission(BasePermission):
    """
    Permission to ensure employees can only access their own data
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        try:
            user_profile = request.user.userprofile
            user_role = user_profile.role
            
            # Super admin and admin can access all data
            if user_role and user_role.name in ['SUPERADMIN', 'ADMIN']:
                return True
            
            # HR managers can access employee data
            if user_role and user_role.name == 'HR_MANAGER':
                return True
            
            return True  # Allow access, but filter in has_object_permission
        except:
            return False
    
    def has_object_permission(self, request, view, obj):
        try:
            user_profile = request.user.userprofile
            user_role = user_profile.role
            
            # Super admin and admin can access all data
            if user_role and user_role.name in ['SUPERADMIN', 'ADMIN']:
                return True
            
            # HR managers can access employee data
            if user_role and user_role.name == 'HR_MANAGER':
                return True
            
            # Employees can only access their own data
            if user_role and user_role.name == 'EMPLOYEE':
                if hasattr(obj, 'user'):
                    return obj.user == request.user
                elif hasattr(obj, 'employee') and hasattr(obj.employee, 'user'):
                    return obj.employee.user == request.user
                elif hasattr(obj, 'assigned_to') and hasattr(obj.assigned_to, 'user'):
                    return obj.assigned_to.user == request.user
            
            return False
        except:
            return False


def check_employee_restrictions(user, action, resource_type):
    """
    Helper function to check if an employee can perform a specific action
    """
    try:
        user_profile = user.userprofile
        user_role = user_profile.role
        
        if not user_role or user_role.name != 'EMPLOYEE':
            return True  # Not an employee, allow
        
        permissions = user_role.permissions or {}
        
        # Define what employees can and cannot do
        restrictions = {
            'task': {
                'create': False,
                'edit': False,
                'delete': False,
                'export': False,
                'view': permissions.get('view_own_tasks', False),
                'update_status': permissions.get('update_task_status', False)
            },
            'project': {
                'create': False,
                'edit': False,
                'delete': False,
                'export': False,
                'view': permissions.get('view_assigned_projects', False)
            },
            'document': {
                'create': False,
                'edit': False,
                'delete': False,
                'export': False,
                'view': permissions.get('view_public_documents', False)
            },
            'announcement': {
                'create': False,
                'edit': False,
                'delete': False,
                'export': False,
                'view': permissions.get('view_company_announcements', False)
            }
        }
        
        if resource_type in restrictions:
            return restrictions[resource_type].get(action, False)
        
        return False  # Default deny for employees
    except:
        return False
