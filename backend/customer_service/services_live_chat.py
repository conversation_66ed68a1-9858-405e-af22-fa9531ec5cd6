"""
Live Chat Support Service
Handles real-time chat between customers and agents
"""

from django.contrib.auth.models import User
from django.utils import timezone
from django.db.models import Q
from .models import LiveChatSession, ChatMessage, SupportAgent
from .simple_customer_service import SimpleCustomerService
import uuid
import logging

logger = logging.getLogger(__name__)

class LiveChatService:
    """Service for managing live chat sessions"""

    @staticmethod
    def create_chat_session(customer_data, subject=None):
        """Create a new live chat session"""
        try:
            # Create or get customer
            customer, created = SimpleCustomerService.create_customer_from_chat(customer_data)

            # Generate session ID
            session_id = f"CHAT-{timezone.now().strftime('%Y%m%d%H%M%S')}-{uuid.uuid4().hex[:6].upper()}"

            # Create chat session
            session = LiveChatSession.objects.create(
                session_id=session_id,
                customer=customer,
                subject=subject or 'Live Chat Support',
                status='waiting',
                started_at=timezone.now()
            )

            logger.info(f"Created chat session: {session.session_id} for customer: {customer.username}")

            # Add initial system message
            ChatMessage.objects.create(
                session=session,
                sender=None,  # System message
                message_type='system',
                content=f"Chat session started. Waiting for an available agent...",
                is_read=False
            )

            return session, created

        except Exception as e:
            logger.error(f"Error creating chat session: {e}")
            raise

    @staticmethod
    def assign_agent_to_session(session_id, agent_user=None):
        """Assign an agent to a chat session"""
        try:
            session = LiveChatSession.objects.get(session_id=session_id)

            if agent_user:
                # Assign specific agent
                agent = SupportAgent.objects.get(user=agent_user)
            else:
                # Find available agent
                agent = SupportAgent.objects.filter(
                    is_available=True,
                    user__is_active=True
                ).first()

                if not agent:
                    # No agents available
                    return None, "No agents available"

            # Assign agent to session
            session.agent = agent.user
            session.status = 'active'
            session.save()

            # Update agent's active chat count (if the field exists)
            if hasattr(agent, 'current_active_chats'):
                agent.current_active_chats += 1
                agent.save()

            # Add system message about agent assignment
            ChatMessage.objects.create(
                session=session,
                sender=None,
                message_type='system',
                content=f"Agent {agent.user.get_full_name() or agent.user.username} has joined the chat.",
                is_read=False
            )

            logger.info(f"Assigned agent {agent.user.username} to session {session.session_id}")
            return session, "Agent assigned successfully"

        except LiveChatSession.DoesNotExist:
            return None, "Chat session not found"
        except SupportAgent.DoesNotExist:
            return None, "Agent not found"
        except Exception as e:
            logger.error(f"Error assigning agent: {e}")
            return None, str(e)

    @staticmethod
    def send_message(session_id, sender_user, content, message_type='text'):
        """Send a message in a chat session"""
        try:
            session = LiveChatSession.objects.get(session_id=session_id)

            # Create message
            message = ChatMessage.objects.create(
                session=session,
                sender=sender_user,
                message_type=message_type,
                content=content,
                is_read=False
            )

            # Update session last activity
            session.last_activity_at = timezone.now()
            session.save()

            logger.info(f"Message sent in session {session_id} by {sender_user.username if sender_user else 'System'}")
            return message

        except LiveChatSession.DoesNotExist:
            logger.error(f"Chat session {session_id} not found")
            return None
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            return None

    @staticmethod
    def get_session_messages(session_id, limit=50):
        """Get messages for a chat session"""
        try:
            session = LiveChatSession.objects.get(session_id=session_id)
            messages = ChatMessage.objects.filter(session=session).order_by('created_at')[:limit]
            return list(messages)
        except Exception as e:
            logger.error(f"Error getting messages: {e}")
            return []

    @staticmethod
    def end_chat_session(session_id, ended_by_user=None, reason=None):
        """End a chat session"""
        try:
            session = LiveChatSession.objects.get(session_id=session_id)

            # Update session status
            session.status = 'ended'
            session.ended_at = timezone.now()
            session.ended_by = ended_by_user
            session.save()

            # Update agent's active chat count
            if session.agent:
                try:
                    agent = SupportAgent.objects.get(user=session.agent)
                    if hasattr(agent, 'current_active_chats'):
                        agent.current_active_chats = max(0, agent.current_active_chats - 1)
                        agent.save()
                except SupportAgent.DoesNotExist:
                    pass

            # Add system message
            reason_text = f" Reason: {reason}" if reason else ""
            ended_by_text = f" by {ended_by_user.get_full_name() or ended_by_user.username}" if ended_by_user else ""

            ChatMessage.objects.create(
                session=session,
                sender=None,
                message_type='system',
                content=f"Chat session ended{ended_by_text}.{reason_text}",
                is_read=False
            )

            logger.info(f"Ended chat session {session_id}")
            return session

        except Exception as e:
            logger.error(f"Error ending chat session: {e}")
            return None

    @staticmethod
    def get_active_sessions_for_agent(agent_user):
        """Get active chat sessions for an agent"""
        try:
            sessions = LiveChatSession.objects.filter(
                agent=agent_user,
                status='active'
            ).order_by('-last_activity_at')
            return list(sessions)
        except Exception as e:
            logger.error(f"Error getting agent sessions: {e}")
            return []

    @staticmethod
    def get_waiting_sessions():
        """Get sessions waiting for agent assignment"""
        try:
            sessions = LiveChatSession.objects.filter(
                status='waiting'
            ).order_by('started_at')
            return list(sessions)
        except Exception as e:
            logger.error(f"Error getting waiting sessions: {e}")
            return []

    @staticmethod
    def mark_messages_as_read(session_id, user):
        """Mark messages as read for a user"""
        try:
            session = LiveChatSession.objects.get(session_id=session_id)

            # Mark messages as read (exclude messages from the same user)
            ChatMessage.objects.filter(
                session=session,
                is_read=False
            ).exclude(sender=user).update(is_read=True)

            return True
        except Exception as e:
            logger.error(f"Error marking messages as read: {e}")
            return False

    @staticmethod
    def get_session_stats(session_id):
        """Get statistics for a chat session"""
        try:
            session = LiveChatSession.objects.get(session_id=session_id)

            stats = {
                'session_id': session.session_id,
                'customer': session.customer.get_full_name() if session.customer else 'Unknown',
                'agent': session.agent.get_full_name() if session.agent else 'Unassigned',
                'status': session.status,
                'started_at': session.started_at,
                'ended_at': session.ended_at,
                'duration_minutes': None,
                'total_messages': ChatMessage.objects.filter(session=session).count(),
                'customer_messages': ChatMessage.objects.filter(session=session, sender=session.customer).count(),
                'agent_messages': ChatMessage.objects.filter(session=session, sender=session.agent).count(),
                'satisfaction_rating': session.customer_satisfaction_rating,
            }

            # Calculate duration
            if session.ended_at and session.started_at:
                duration = session.ended_at - session.started_at
                stats['duration_minutes'] = duration.total_seconds() / 60
            elif session.started_at:
                duration = timezone.now() - session.started_at
                stats['duration_minutes'] = duration.total_seconds() / 60

            return stats

        except Exception as e:
            logger.error(f"Error getting session stats: {e}")
            return {}

    @staticmethod
    def search_chat_sessions(query, status=None, agent=None, customer=None):
        """Search chat sessions"""
        try:
            sessions = LiveChatSession.objects.all()

            if query:
                sessions = sessions.filter(
                    Q(session_id__icontains=query) |
                    Q(subject__icontains=query) |
                    Q(customer__username__icontains=query) |
                    Q(customer__email__icontains=query) |
                    Q(agent__username__icontains=query)
                )

            if status:
                sessions = sessions.filter(status=status)

            if agent:
                sessions = sessions.filter(agent=agent)

            if customer:
                sessions = sessions.filter(customer=customer)

            return sessions.order_by('-started_at')

        except Exception as e:
            logger.error(f"Error searching sessions: {e}")
            return LiveChatSession.objects.none()

    @staticmethod
    def transfer_session_to_agent(session_id, new_agent_user, transfer_reason=None):
        """Transfer a chat session to another agent"""
        try:
            session = LiveChatSession.objects.get(session_id=session_id)
            old_agent = session.agent

            # Update old agent's chat count
            if old_agent:
                try:
                    old_agent_obj = SupportAgent.objects.get(user=old_agent)
                    if hasattr(old_agent_obj, 'current_active_chats'):
                        old_agent_obj.current_active_chats = max(0, old_agent_obj.current_active_chats - 1)
                        old_agent_obj.save()
                except SupportAgent.DoesNotExist:
                    pass

            # Assign new agent
            session.agent = new_agent_user
            session.save()

            # Update new agent's chat count
            try:
                new_agent_obj = SupportAgent.objects.get(user=new_agent_user)
                if hasattr(new_agent_obj, 'current_active_chats'):
                    new_agent_obj.current_active_chats += 1
                    new_agent_obj.save()
            except SupportAgent.DoesNotExist:
                pass

            # Add system message
            transfer_text = f" Reason: {transfer_reason}" if transfer_reason else ""
            ChatMessage.objects.create(
                session=session,
                sender=None,
                message_type='system',
                content=f"Chat transferred to {new_agent_user.get_full_name() or new_agent_user.username}.{transfer_text}",
                is_read=False
            )

            logger.info(f"Transferred session {session_id} to {new_agent_user.username}")
            return session

        except Exception as e:
            logger.error(f"Error transferring session: {e}")
            return None
