#!/usr/bin/env python3
"""
CRITICAL FIX: Enterprise-Grade Database Migration
Advanced migration for large datasets with real-time monitoring and recovery
"""

import os
import sys
import time
import threading
import queue
import json
import psutil
from pathlib import Path
from datetime import datetime, timedelta
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# Add Django to path
sys.path.append(str(Path(__file__).parent.parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

import django
django.setup()

from django.db import connections, transaction
from django.core.management import call_command
from django.apps import apps
from django.conf import settings

class EnterpriseMigrator:
    def __init__(self):
        self.migration_stats = {
            "start_time": None,
            "total_records": 0,
            "migrated_records": 0,
            "failed_records": 0,
            "current_table": None,
            "migration_rate": 0,
            "estimated_completion": None,
            "errors": [],
            "performance_metrics": {}
        }
        self.monitoring_thread = None
        self.stop_monitoring = threading.Event()
        self.progress_queue = queue.Queue()
        
    def execute_enterprise_migration(self):
        """Execute enterprise-grade migration with advanced monitoring"""
        print("🏢 Starting Enterprise Database Migration...")
        print("=" * 70)
        
        self.migration_stats["start_time"] = datetime.now()
        
        try:
            # Phase 1: Pre-migration analysis and optimization
            self.phase_1_pre_migration_optimization()
            
            # Phase 2: Setup parallel migration infrastructure
            self.phase_2_setup_parallel_infrastructure()
            
            # Phase 3: Execute parallel migration with monitoring
            self.phase_3_parallel_migration()
            
            # Phase 4: Data integrity validation
            self.phase_4_comprehensive_validation()
            
            # Phase 5: Performance optimization
            self.phase_5_post_migration_optimization()
            
            # Phase 6: Monitoring and alerting setup
            self.phase_6_monitoring_setup()
            
            print("\n🎉 Enterprise migration completed successfully!")
            self.print_migration_summary()
            return True
            
        except Exception as e:
            print(f"\n💥 Enterprise migration failed: {e}")
            self.handle_migration_failure(e)
            return False
        finally:
            self.cleanup_migration_resources()
    
    def phase_1_pre_migration_optimization(self):
        """Phase 1: Pre-migration analysis and optimization"""
        print("\n📋 Phase 1: Pre-migration optimization...")
        
        # Analyze data distribution
        self.analyze_data_distribution()
        
        # Optimize source database
        self.optimize_source_database()
        
        # Setup PostgreSQL for optimal performance
        self.optimize_postgresql_for_migration()
        
        # Create migration plan based on data analysis
        self.create_optimized_migration_plan()
        
        print("  ✅ Pre-migration optimization complete")
    
    def analyze_data_distribution(self):
        """Analyze data distribution for optimal migration strategy"""
        print("  📊 Analyzing data distribution...")
        
        models = apps.get_models()
        table_stats = {}
        
        for model in models:
            try:
                table_name = model._meta.db_table
                record_count = model.objects.count()
                
                # Analyze foreign key relationships
                fk_fields = [f for f in model._meta.fields if hasattr(f, 'related_model')]
                
                # Estimate record size
                sample_record = model.objects.first()
                estimated_size = 0
                if sample_record:
                    # Rough estimation based on field types
                    for field in model._meta.fields:
                        if hasattr(field, 'max_length') and field.max_length:
                            estimated_size += field.max_length
                        else:
                            estimated_size += 50  # Default field size
                
                table_stats[table_name] = {
                    "model": model.__name__,
                    "record_count": record_count,
                    "estimated_size_mb": (record_count * estimated_size) / (1024 * 1024),
                    "foreign_keys": len(fk_fields),
                    "migration_priority": self.calculate_migration_priority(model, record_count, fk_fields)
                }
                
                self.migration_stats["total_records"] += record_count
                
            except Exception as e:
                print(f"    ⚠️ Could not analyze {model.__name__}: {e}")
        
        # Sort tables by migration priority
        sorted_tables = sorted(table_stats.items(), key=lambda x: x[1]["migration_priority"])
        
        self.migration_stats["table_stats"] = dict(sorted_tables)
        print(f"    📈 Total records to migrate: {self.migration_stats['total_records']:,}")
    
    def calculate_migration_priority(self, model, record_count, fk_fields):
        """Calculate migration priority based on dependencies and size"""
        
        # Base priority on foreign key dependencies (fewer FKs = higher priority)
        fk_penalty = len(fk_fields) * 10
        
        # Size penalty for very large tables
        size_penalty = min(record_count // 100000, 50)  # Max 50 points for size
        
        # Special handling for core models
        core_models = ['User', 'Department', 'Role']
        if model.__name__ in core_models:
            return 0  # Highest priority
        
        return fk_penalty + size_penalty
    
    def optimize_source_database(self):
        """Optimize SQLite database for faster reads"""
        print("  🔧 Optimizing source database...")
        
        import sqlite3
        conn = sqlite3.connect('db.sqlite3')
        cursor = conn.cursor()
        
        # Optimize SQLite for read performance
        optimizations = [
            "PRAGMA cache_size = 10000;",
            "PRAGMA temp_store = MEMORY;",
            "PRAGMA mmap_size = 268435456;",  # 256MB
            "PRAGMA synchronous = OFF;",
            "PRAGMA journal_mode = OFF;",
        ]
        
        for optimization in optimizations:
            try:
                cursor.execute(optimization)
                print(f"    ✅ Applied: {optimization}")
            except Exception as e:
                print(f"    ⚠️ Failed to apply {optimization}: {e}")
        
        conn.close()
    
    def optimize_postgresql_for_migration(self):
        """Optimize PostgreSQL for bulk data loading"""
        print("  🐘 Optimizing PostgreSQL for migration...")
        
        postgresql_conn = connections['postgresql']
        
        with postgresql_conn.cursor() as cursor:
            # Optimize PostgreSQL for bulk loading
            optimizations = [
                "SET maintenance_work_mem = '1GB';",
                "SET checkpoint_completion_target = 0.9;",
                "SET wal_buffers = '16MB';",
                "SET shared_buffers = '256MB';",
                "SET effective_cache_size = '1GB';",
            ]
            
            for optimization in optimizations:
                try:
                    cursor.execute(optimization)
                    print(f"    ✅ Applied: {optimization}")
                except Exception as e:
                    print(f"    ⚠️ Failed to apply {optimization}: {e}")
    
    def phase_2_setup_parallel_infrastructure(self):
        """Phase 2: Setup parallel migration infrastructure"""
        print("\n📋 Phase 2: Setting up parallel infrastructure...")
        
        # Start monitoring thread
        self.start_migration_monitoring()
        
        # Setup connection pools
        self.setup_connection_pools()
        
        # Create temporary staging tables
        self.create_staging_tables()
        
        print("  ✅ Parallel infrastructure ready")
    
    def start_migration_monitoring(self):
        """Start real-time migration monitoring"""
        print("  📊 Starting migration monitoring...")
        
        def monitor_migration():
            while not self.stop_monitoring.is_set():
                try:
                    # Update migration statistics
                    self.update_migration_statistics()
                    
                    # Check system resources
                    self.monitor_system_resources()
                    
                    # Print progress update
                    self.print_progress_update()
                    
                    time.sleep(10)  # Update every 10 seconds
                    
                except Exception as e:
                    print(f"    ⚠️ Monitoring error: {e}")
        
        self.monitoring_thread = threading.Thread(target=monitor_migration, daemon=True)
        self.monitoring_thread.start()
    
    def update_migration_statistics(self):
        """Update migration statistics"""
        
        if self.migration_stats["migrated_records"] > 0 and self.migration_stats["start_time"]:
            elapsed_time = (datetime.now() - self.migration_stats["start_time"]).total_seconds()
            self.migration_stats["migration_rate"] = self.migration_stats["migrated_records"] / elapsed_time
            
            if self.migration_stats["migration_rate"] > 0:
                remaining_records = self.migration_stats["total_records"] - self.migration_stats["migrated_records"]
                remaining_time = remaining_records / self.migration_stats["migration_rate"]
                self.migration_stats["estimated_completion"] = datetime.now() + timedelta(seconds=remaining_time)
    
    def monitor_system_resources(self):
        """Monitor system resource usage"""
        
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        self.migration_stats["performance_metrics"] = {
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_available_gb": memory.available / (1024**3),
            "disk_free_gb": disk.free / (1024**3),
            "timestamp": datetime.now().isoformat()
        }
        
        # Alert if resources are running low
        if cpu_percent > 90:
            print(f"    ⚠️ High CPU usage: {cpu_percent}%")
        
        if memory.percent > 90:
            print(f"    ⚠️ High memory usage: {memory.percent}%")
        
        if disk.free / disk.total < 0.1:
            print(f"    ⚠️ Low disk space: {disk.free / (1024**3):.2f} GB remaining")
    
    def print_progress_update(self):
        """Print migration progress update"""
        
        if self.migration_stats["total_records"] > 0:
            progress_percent = (self.migration_stats["migrated_records"] / self.migration_stats["total_records"]) * 100
            
            print(f"    📈 Progress: {progress_percent:.1f}% "
                  f"({self.migration_stats['migrated_records']:,}/{self.migration_stats['total_records']:,}) "
                  f"Rate: {self.migration_stats['migration_rate']:.0f} records/sec")
            
            if self.migration_stats["estimated_completion"]:
                eta = self.migration_stats["estimated_completion"].strftime("%H:%M:%S")
                print(f"    ⏰ ETA: {eta}")
    
    def phase_3_parallel_migration(self):
        """Phase 3: Execute parallel migration"""
        print("\n📋 Phase 3: Executing parallel migration...")
        
        # Get migration plan
        table_stats = self.migration_stats.get("table_stats", {})
        
        # Migrate tables in dependency order with parallel processing
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = []
            
            for table_name, stats in table_stats.items():
                if stats["record_count"] > 0:
                    future = executor.submit(self.migrate_table_parallel, table_name, stats)
                    futures.append(future)
            
            # Process completed migrations
            for future in as_completed(futures):
                try:
                    result = future.result()
                    print(f"    ✅ Completed: {result['table']} ({result['records']} records)")
                except Exception as e:
                    print(f"    ❌ Migration failed: {e}")
                    self.migration_stats["errors"].append(str(e))
        
        print("  ✅ Parallel migration complete")
    
    def migrate_table_parallel(self, table_name, stats):
        """Migrate a single table with parallel processing"""
        
        model_name = stats["model"]
        record_count = stats["record_count"]
        
        try:
            # Get model class
            model = None
            for app_model in apps.get_models():
                if app_model.__name__ == model_name:
                    model = app_model
                    break
            
            if not model:
                raise Exception(f"Model {model_name} not found")
            
            self.migration_stats["current_table"] = table_name
            
            # Migrate in batches for large tables
            batch_size = min(1000, max(100, record_count // 100))
            total_batches = (record_count + batch_size - 1) // batch_size
            
            migrated_count = 0
            
            for batch_num in range(total_batches):
                offset = batch_num * batch_size
                
                # Get batch from SQLite
                batch_records = list(model.objects.using('default')[offset:offset + batch_size])
                
                if batch_records:
                    # Clear primary keys for bulk create
                    for record in batch_records:
                        record.pk = None
                    
                    # Bulk create in PostgreSQL
                    model.objects.using('postgresql').bulk_create(
                        batch_records, 
                        batch_size=batch_size,
                        ignore_conflicts=True
                    )
                    
                    migrated_count += len(batch_records)
                    self.migration_stats["migrated_records"] += len(batch_records)
                
                # Update progress
                if batch_num % 10 == 0:  # Update every 10 batches
                    progress = (batch_num / total_batches) * 100
                    print(f"      📊 {table_name}: {progress:.1f}% ({migrated_count:,}/{record_count:,})")
            
            return {
                "table": table_name,
                "records": migrated_count,
                "success": True
            }
            
        except Exception as e:
            self.migration_stats["failed_records"] += record_count
            raise Exception(f"Failed to migrate {table_name}: {e}")
    
    def phase_4_comprehensive_validation(self):
        """Phase 4: Comprehensive data validation"""
        print("\n📋 Phase 4: Comprehensive validation...")
        
        validation_results = {
            "record_counts": {},
            "data_integrity": {},
            "foreign_key_validation": {},
            "performance_validation": {}
        }
        
        # Validate record counts
        print("  🔢 Validating record counts...")
        models = apps.get_models()
        
        for model in models:
            try:
                sqlite_count = model.objects.using('default').count()
                postgresql_count = model.objects.using('postgresql').count()
                
                validation_results["record_counts"][model.__name__] = {
                    "sqlite": sqlite_count,
                    "postgresql": postgresql_count,
                    "match": sqlite_count == postgresql_count
                }
                
                if sqlite_count != postgresql_count:
                    print(f"    ❌ {model.__name__}: SQLite={sqlite_count}, PostgreSQL={postgresql_count}")
                else:
                    print(f"    ✅ {model.__name__}: {sqlite_count} records")
                    
            except Exception as e:
                print(f"    ⚠️ Could not validate {model.__name__}: {e}")
        
        # Validate foreign key relationships
        print("  🔗 Validating foreign key relationships...")
        self.validate_foreign_key_integrity(validation_results)
        
        # Performance validation
        print("  ⚡ Validating performance...")
        self.validate_migration_performance(validation_results)
        
        # Save validation results
        with open('migration_validation_results.json', 'w') as f:
            json.dump(validation_results, f, indent=2, default=str)
        
        print("  ✅ Comprehensive validation complete")
    
    def validate_foreign_key_integrity(self, validation_results):
        """Validate foreign key relationships"""
        
        models = apps.get_models()
        fk_issues = []
        
        for model in models:
            try:
                for field in model._meta.fields:
                    if hasattr(field, 'related_model') and field.related_model:
                        # Check for orphaned records
                        orphaned_count = model.objects.using('postgresql').filter(
                            **{f"{field.name}__isnull": True}
                        ).exclude(**{f"{field.name}": None}).count()
                        
                        if orphaned_count > 0:
                            fk_issues.append({
                                "model": model.__name__,
                                "field": field.name,
                                "orphaned_records": orphaned_count
                            })
                            
            except Exception as e:
                print(f"    ⚠️ FK validation error for {model.__name__}: {e}")
        
        validation_results["foreign_key_validation"] = {
            "issues_found": len(fk_issues),
            "issues": fk_issues
        }
        
        if fk_issues:
            print(f"    ⚠️ Found {len(fk_issues)} foreign key issues")
        else:
            print(f"    ✅ No foreign key issues found")
    
    def validate_migration_performance(self, validation_results):
        """Validate migration performance metrics"""
        
        elapsed_time = (datetime.now() - self.migration_stats["start_time"]).total_seconds()
        
        performance_metrics = {
            "total_migration_time_seconds": elapsed_time,
            "total_migration_time_minutes": elapsed_time / 60,
            "records_per_second": self.migration_stats["migrated_records"] / elapsed_time,
            "success_rate": (self.migration_stats["migrated_records"] / 
                           (self.migration_stats["migrated_records"] + self.migration_stats["failed_records"])) * 100
        }
        
        validation_results["performance_validation"] = performance_metrics
        
        print(f"    📊 Migration rate: {performance_metrics['records_per_second']:.0f} records/second")
        print(f"    📊 Success rate: {performance_metrics['success_rate']:.1f}%")
        print(f"    📊 Total time: {performance_metrics['total_migration_time_minutes']:.1f} minutes")
    
    def phase_5_post_migration_optimization(self):
        """Phase 5: Post-migration optimization"""
        print("\n📋 Phase 5: Post-migration optimization...")
        
        postgresql_conn = connections['postgresql']
        
        with postgresql_conn.cursor() as cursor:
            # Update table statistics
            print("  📊 Updating table statistics...")
            cursor.execute("ANALYZE;")
            
            # Rebuild indexes
            print("  📇 Rebuilding indexes...")
            cursor.execute("REINDEX DATABASE ems_production;")
            
            # Vacuum database
            print("  🧹 Vacuuming database...")
            cursor.execute("VACUUM ANALYZE;")
        
        print("  ✅ Post-migration optimization complete")
    
    def phase_6_monitoring_setup(self):
        """Phase 6: Setup monitoring and alerting"""
        print("\n📋 Phase 6: Setting up monitoring...")
        
        # Create monitoring configuration
        monitoring_config = {
            "migration_completed": True,
            "completion_time": datetime.now().isoformat(),
            "total_records_migrated": self.migration_stats["migrated_records"],
            "migration_duration_minutes": (datetime.now() - self.migration_stats["start_time"]).total_seconds() / 60,
            "performance_metrics": self.migration_stats["performance_metrics"]
        }
        
        with open('migration_monitoring.json', 'w') as f:
            json.dump(monitoring_config, f, indent=2)
        
        print("  ✅ Monitoring setup complete")
    
    def print_migration_summary(self):
        """Print comprehensive migration summary"""
        print("\n" + "=" * 70)
        print("📋 ENTERPRISE MIGRATION SUMMARY")
        print("=" * 70)
        
        elapsed_time = (datetime.now() - self.migration_stats["start_time"]).total_seconds()
        
        print(f"\n🎯 Migration Results:")
        print(f"  • Total records: {self.migration_stats['total_records']:,}")
        print(f"  • Successfully migrated: {self.migration_stats['migrated_records']:,}")
        print(f"  • Failed records: {self.migration_stats['failed_records']:,}")
        print(f"  • Success rate: {(self.migration_stats['migrated_records']/self.migration_stats['total_records']*100):.2f}%")
        
        print(f"\n⏱️ Performance Metrics:")
        print(f"  • Total time: {elapsed_time/60:.1f} minutes")
        print(f"  • Migration rate: {self.migration_stats['migrated_records']/elapsed_time:.0f} records/second")
        
        if self.migration_stats["errors"]:
            print(f"\n❌ Errors ({len(self.migration_stats['errors'])}):")
            for error in self.migration_stats["errors"][:5]:  # Show first 5 errors
                print(f"  • {error}")
        
        print(f"\n📁 Generated Files:")
        print(f"  • migration_validation_results.json")
        print(f"  • migration_monitoring.json")
    
    def handle_migration_failure(self, error):
        """Handle migration failure with detailed logging"""
        print(f"\n💥 Migration Failure Handler")
        print(f"Error: {error}")
        
        # Save failure state
        failure_state = {
            "error": str(error),
            "migration_stats": self.migration_stats,
            "timestamp": datetime.now().isoformat()
        }
        
        with open('migration_failure_state.json', 'w') as f:
            json.dump(failure_state, f, indent=2, default=str)
        
        print(f"📁 Failure state saved to: migration_failure_state.json")
    
    def cleanup_migration_resources(self):
        """Cleanup migration resources"""
        
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.stop_monitoring.set()
            self.monitoring_thread.join(timeout=5)

if __name__ == '__main__':
    migrator = EnterpriseMigrator()
    success = migrator.execute_enterprise_migration()
    sys.exit(0 if success else 1)
