"""
Public Customer Service Views
For anonymous and customer-facing support features
"""

from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from rest_framework import viewsets, status
from rest_framework.decorators import action, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from django.contrib.auth.models import User
import json
import uuid
from datetime import datetime

from .models_advanced import AIAssistant, AIInteraction
from .models import SupportTicket, SupportTicketCategory, KnowledgeBaseArticle
from .services import AIAssistantService

class PublicChatViewSet(viewsets.ViewSet):
    """Public chat interface for customers (no authentication required)"""
    permission_classes = [AllowAny]

    @action(detail=False, methods=['post'])
    def chat(self, request):
        """Public chat endpoint for customers"""
        try:
            message = request.data.get('message', '')
            session_id = request.data.get('session_id', str(uuid.uuid4()))
            customer_email = request.data.get('customer_email', '')
            customer_name = request.data.get('customer_name', 'Anonymous')
            
            if not message:
                return Response({'error': 'Message is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Get the first active chatbot
            try:
                assistant = AIAssistant.objects.filter(
                    assistant_type='chatbot',
                    is_active=True
                ).first()
                
                if not assistant:
                    return Response({
                        'response': 'I apologize, but our AI assistant is currently unavailable. Please try again later or contact our support team directly.',
                        'confidence': 0.0,
                        'suggestions': [],
                        'escalate_to_human': True
                    })
                
                # Generate AI response
                context = {
                    'session_id': session_id,
                    'customer_email': customer_email,
                    'customer_name': customer_name,
                    'is_public': True,
                    'language': request.data.get('language', 'en')
                }
                
                response_data = AIAssistantService.get_ai_response(assistant, message, context)
                
                # Log interaction without requiring user authentication
                AIInteraction.objects.create(
                    assistant=assistant,
                    user=None,  # No user for public chat
                    session_id=session_id,
                    user_input=message,
                    ai_response=response_data['response'],
                    confidence_score=response_data['confidence'],
                    context_data=json.dumps(context)
                )
                
                # Update assistant statistics
                assistant.total_interactions += 1
                assistant.save()
                
                return Response(response_data)
                
            except Exception as e:
                return Response({
                    'response': 'I apologize, but I encountered an error. Please try again or contact our support team.',
                    'confidence': 0.0,
                    'suggestions': [],
                    'escalate_to_human': True,
                    'error': str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def create_ticket(self, request):
        """Create support ticket from chat escalation"""
        try:
            data = request.data
            
            # Create ticket for anonymous user
            ticket = SupportTicket.objects.create(
                title=data.get('title', 'Chat Escalation'),
                description=data.get('description', ''),
                customer_email=data.get('customer_email', ''),
                customer_phone=data.get('customer_phone', ''),
                source='chat_widget',
                priority='medium',
                status='open'
            )
            
            return Response({
                'ticket_id': ticket.ticket_id,
                'message': 'Support ticket created successfully. You will receive updates via email.'
            })
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def knowledge_search(self, request):
        """Search knowledge base articles (public access)"""
        try:
            query = request.query_params.get('q', '')
            
            if not query:
                return Response({'articles': []})
            
            # Simple search in knowledge base
            articles = KnowledgeBaseArticle.objects.filter(
                is_published=True,
                title__icontains=query
            )[:5]
            
            results = []
            for article in articles:
                results.append({
                    'id': article.id,
                    'title': article.title,
                    'summary': article.summary,
                    'view_count': article.view_count
                })
            
            return Response({'articles': results})
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class CustomerChatViewSet(viewsets.ViewSet):
    """Chat interface for authenticated customers (non-admin users)"""
    
    def get_permissions(self):
        """Allow authenticated users who are not admin"""
        from rest_framework.permissions import IsAuthenticated
        return [IsAuthenticated()]

    @action(detail=False, methods=['post'])
    def chat(self, request):
        """Authenticated customer chat"""
        try:
            message = request.data.get('message', '')
            session_id = request.data.get('session_id', str(uuid.uuid4()))
            
            if not message:
                return Response({'error': 'Message is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Get the first active chatbot
            assistant = AIAssistant.objects.filter(
                assistant_type='chatbot',
                is_active=True
            ).first()
            
            if not assistant:
                return Response({
                    'response': 'I apologize, but our AI assistant is currently unavailable. Please try again later or contact our support team directly.',
                    'confidence': 0.0,
                    'suggestions': [],
                    'escalate_to_human': True
                })
            
            # Generate AI response with user context
            context = {
                'session_id': session_id,
                'user_id': request.user.id,
                'user_email': request.user.email,
                'user_name': request.user.get_full_name() or request.user.username,
                'is_authenticated': True,
                'language': request.data.get('language', 'en')
            }
            
            response_data = AIAssistantService.get_ai_response(assistant, message, context)
            
            # Log interaction with user
            AIInteraction.objects.create(
                assistant=assistant,
                user=request.user,
                session_id=session_id,
                user_input=message,
                ai_response=response_data['response'],
                confidence_score=response_data['confidence'],
                context_data=json.dumps(context)
            )
            
            # Update assistant statistics
            assistant.total_interactions += 1
            assistant.save()
            
            return Response(response_data)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def create_ticket(self, request):
        """Create support ticket for authenticated customer"""
        try:
            data = request.data
            
            ticket = SupportTicket.objects.create(
                title=data.get('title', 'Customer Support Request'),
                description=data.get('description', ''),
                customer=request.user,
                customer_email=request.user.email,
                source='customer_portal',
                priority=data.get('priority', 'medium'),
                status='open'
            )
            
            return Response({
                'ticket_id': ticket.ticket_id,
                'message': 'Support ticket created successfully.'
            })
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def my_tickets(self, request):
        """Get customer's own tickets"""
        try:
            tickets = SupportTicket.objects.filter(customer=request.user).order_by('-created_at')[:10]
            
            results = []
            for ticket in tickets:
                results.append({
                    'id': ticket.id,
                    'ticket_id': ticket.ticket_id,
                    'title': ticket.title,
                    'status': ticket.status,
                    'priority': ticket.priority,
                    'created_at': ticket.created_at.isoformat(),
                    'updated_at': ticket.updated_at.isoformat()
                })
            
            return Response({'tickets': results})
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Legacy support for existing endpoints
class SupportInfoView(View):
    """Public support information"""
    
    def get(self, request):
        return JsonResponse({
            'support_available': True,
            'business_hours': '9 AM - 6 PM (Mon-Fri)',
            'emergency_contact': '+1-234-567-8900',
            'email': '<EMAIL>',
            'chat_available': True
        })

@method_decorator(csrf_exempt, name='dispatch')
class PublicChatView(View):
    """Simple public chat endpoint (legacy)"""
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            message = data.get('message', '')
            
            if not message:
                return JsonResponse({'error': 'Message is required'}, status=400)
            
            # Simple response for demo
            response = f"Thank you for your message: '{message}'. Our support team will get back to you soon. For immediate assistance, please call +1-234-567-8900."
            
            return JsonResponse({
                'response': response,
                'confidence': 0.8,
                'suggestions': [],
                'escalate_to_human': False
            })
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
