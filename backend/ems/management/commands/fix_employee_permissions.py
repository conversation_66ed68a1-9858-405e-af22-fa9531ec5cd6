"""
Management command to fix Employee role permissions
Restricts Employee role to appropriate access levels for security
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from ems.models import Role


class Command(BaseCommand):
    help = 'Fix Employee role permissions to restrict access appropriately'

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🔒 Fixing Employee role permissions...')
        )

        try:
            with transaction.atomic():
                # Update Employee role with restricted permissions
                employee_role, created = Role.objects.get_or_create(
                    name='EMPLOYEE',
                    defaults={
                        'name_ar': 'موظف',
                        'description': 'Regular Employee with limited access',
                        'description_ar': 'موظف عادي مع صلاحيات محدودة',
                        'permissions': {
                            # Basic employee permissions
                            'view_own_profile': True,
                            'edit_own_profile': True,
                            'view_own_tasks': True,
                            'update_task_status': True,  # Can mark tasks complete
                            'view_assigned_projects': True,  # Read-only
                            'submit_leave_requests': True,
                            'view_own_attendance': True,
                            'view_own_performance': True,
                            'view_company_announcements': True,  # Read-only
                            'view_public_documents': True,  # Read-only, filtered
                            'view_own_meetings': True,
                            'send_messages': True,
                            'view_company_directory': True,  # Basic contact info
                            
                            # Explicitly denied permissions
                            'create_tasks': False,
                            'edit_tasks': False,
                            'delete_tasks': False,
                            'export_data': False,
                            'create_projects': False,
                            'edit_projects': False,
                            'delete_projects': False,
                            'create_documents': False,
                            'edit_documents': False,
                            'delete_documents': False,
                            'create_announcements': False,
                            'edit_announcements': False,
                            'delete_announcements': False,
                            'view_all_employees': False,
                            'manage_users': False,
                            'system_admin': False,
                            'financial_access': False,
                            'hr_management': False,
                            'department_management': False,
                            'advanced_analytics': False,
                            'security_management': False,
                            'audit_access': False,
                            'compliance_management': False,
                            'ai_management': False,
                            'backup_management': False,
                            'system_configuration': False,
                            
                            # Access level
                            'level': 8  # Lowest privilege level
                        }
                    }
                )

                if not created:
                    # Update existing Employee role with restricted permissions
                    employee_role.permissions = {
                        # Basic employee permissions
                        'view_own_profile': True,
                        'edit_own_profile': True,
                        'view_own_tasks': True,
                        'update_task_status': True,  # Can mark tasks complete
                        'view_assigned_projects': True,  # Read-only
                        'submit_leave_requests': True,
                        'view_own_attendance': True,
                        'view_own_performance': True,
                        'view_company_announcements': True,  # Read-only
                        'view_public_documents': True,  # Read-only, filtered
                        'view_own_meetings': True,
                        'send_messages': True,
                        'view_company_directory': True,  # Basic contact info
                        
                        # Explicitly denied permissions
                        'create_tasks': False,
                        'edit_tasks': False,
                        'delete_tasks': False,
                        'export_data': False,
                        'create_projects': False,
                        'edit_projects': False,
                        'delete_projects': False,
                        'create_documents': False,
                        'edit_documents': False,
                        'delete_documents': False,
                        'create_announcements': False,
                        'edit_announcements': False,
                        'delete_announcements': False,
                        'view_all_employees': False,
                        'manage_users': False,
                        'system_admin': False,
                        'financial_access': False,
                        'hr_management': False,
                        'department_management': False,
                        'advanced_analytics': False,
                        'security_management': False,
                        'audit_access': False,
                        'compliance_management': False,
                        'ai_management': False,
                        'backup_management': False,
                        'system_configuration': False,
                        
                        # Access level
                        'level': 8  # Lowest privilege level
                    }
                    employee_role.save()
                    self.stdout.write(
                        self.style.SUCCESS('✅ Updated existing Employee role permissions')
                    )
                else:
                    self.stdout.write(
                        self.style.SUCCESS('✅ Created Employee role with restricted permissions')
                    )

                # Also update other roles to ensure proper hierarchy
                self.stdout.write('🔧 Updating other role permissions...')
                
                # Update HR Manager role
                hr_role = Role.objects.filter(name='HR_MANAGER').first()
                if hr_role:
                    hr_role.permissions.update({
                        'level': 5,
                        'hr_management': True,
                        'view_all_employees': True,
                        'manage_leave_requests': True,
                        'view_attendance': True,
                        'performance_management': True,
                        'create_announcements': True,
                        'edit_announcements': True,
                        'export_hr_data': True
                    })
                    hr_role.save()
                    self.stdout.write('✅ Updated HR Manager permissions')

                # Update Finance Manager role
                finance_role = Role.objects.filter(name='FINANCE_MANAGER').first()
                if finance_role:
                    finance_role.permissions.update({
                        'level': 5,
                        'financial_access': True,
                        'budget_management': True,
                        'expense_management': True,
                        'financial_reports': True,
                        'export_financial_data': True
                    })
                    finance_role.save()
                    self.stdout.write('✅ Updated Finance Manager permissions')

                # Update Department Manager role
                dept_role = Role.objects.filter(name='DEPARTMENT_MANAGER').first()
                if dept_role:
                    dept_role.permissions.update({
                        'level': 6,
                        'department_management': True,
                        'view_department_employees': True,
                        'manage_department_projects': True,
                        'department_reports': True
                    })
                    dept_role.save()
                    self.stdout.write('✅ Updated Department Manager permissions')

                self.stdout.write(
                    self.style.SUCCESS(
                        '\n🎉 Successfully fixed Employee role permissions!\n'
                        '\n📋 Employee Role Restrictions Applied:'
                        '\n  ✅ Can view own tasks (read-only + mark complete)'
                        '\n  ✅ Can view assigned projects (read-only)'
                        '\n  ✅ Can view public documents (read-only)'
                        '\n  ✅ Can view company announcements (read-only)'
                        '\n  ❌ Cannot create/edit/delete tasks'
                        '\n  ❌ Cannot create/edit/delete projects'
                        '\n  ❌ Cannot create/edit/delete documents'
                        '\n  ❌ Cannot export any data'
                        '\n  ❌ Cannot access admin features'
                        '\n  ❌ Cannot manage other users'
                        '\n\n🔒 Security Level: Appropriate for basic employees'
                    )
                )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error fixing Employee permissions: {str(e)}')
            )
            raise e
