#!/bin/bash
# Backend Security Implementation Deployment Script
# This script deploys the critical security fixes to the Django backend

set -e  # Exit on any error

# Configuration
PROJECT_ROOT="/var/www/ems"
VENV_PATH="/var/www/ems/backend_env"
BACKUP_DIR="/var/backups/ems"
LOG_FILE="/var/log/ems/deployment.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Pre-deployment checks
pre_deployment_checks() {
    log "🔍 Running pre-deployment checks..."
    
    # Check if running as correct user
    if [ "$USER" != "www-data" ] && [ "$USER" != "root" ]; then
        warning "Not running as www-data or root. Some operations may fail."
    fi
    
    # Check if backup directory exists
    if [ ! -d "$BACKUP_DIR" ]; then
        log "Creating backup directory: $BACKUP_DIR"
        mkdir -p "$BACKUP_DIR"
    fi
    
    # Check if virtual environment exists
    if [ ! -d "$VENV_PATH" ]; then
        error "Virtual environment not found at $VENV_PATH"
    fi
    
    # Check database connectivity
    log "Testing database connectivity..."
    cd "$PROJECT_ROOT/backend"
    source "$VENV_PATH/bin/activate"
    
    if ! python manage.py check --database default; then
        error "Database connectivity check failed"
    fi
    
    success "Pre-deployment checks passed"
}

# Create backup
create_backup() {
    log "📦 Creating backup..."
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_PATH="$BACKUP_DIR/backup_$TIMESTAMP"
    
    mkdir -p "$BACKUP_PATH"
    
    # Backup database
    log "Backing up database..."
    pg_dump ems_production > "$BACKUP_PATH/database.sql"
    
    # Backup current code
    log "Backing up current code..."
    tar -czf "$BACKUP_PATH/backend_code.tar.gz" -C "$PROJECT_ROOT" backend/
    
    # Backup environment file
    if [ -f "$PROJECT_ROOT/.env" ]; then
        cp "$PROJECT_ROOT/.env" "$BACKUP_PATH/.env"
    fi
    
    # Backup static files
    if [ -d "$PROJECT_ROOT/backend/static" ]; then
        tar -czf "$BACKUP_PATH/static_files.tar.gz" -C "$PROJECT_ROOT/backend" static/
    fi
    
    echo "$BACKUP_PATH" > "$BACKUP_DIR/latest_backup.txt"
    success "Backup created at $BACKUP_PATH"
}

# Deploy backend changes
deploy_backend() {
    log "🚀 Deploying backend security fixes..."
    
    cd "$PROJECT_ROOT/backend"
    source "$VENV_PATH/bin/activate"
    
    # Install/update dependencies
    log "Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Apply database migrations
    log "Applying database migrations..."
    python manage.py migrate
    
    # Collect static files
    log "Collecting static files..."
    python manage.py collectstatic --noinput
    
    # Test the new authentication system
    log "Testing authentication system..."
    python manage.py shell << EOF
from django.contrib.auth.models import User
from ems.auth_views import CookieJWTAuthentication
from django.test import RequestFactory

# Test that the custom authentication class can be imported
auth = CookieJWTAuthentication()
print("✅ CookieJWTAuthentication imported successfully")

# Test user creation (if needed)
if not User.objects.filter(username='testuser').exists():
    User.objects.create_user('testuser', '<EMAIL>', 'testpass123')
    print("✅ Test user created")
else:
    print("✅ Test user already exists")
EOF
    
    success "Backend deployment completed"
}

# Update configuration files
update_configuration() {
    log "⚙️ Updating configuration files..."
    
    # Update Django settings for production
    cat > "$PROJECT_ROOT/backend/backend/settings_production.py" << 'EOF'
from .settings import *
import os

# Production-specific settings
DEBUG = False
ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', 'yourdomain.com,www.yourdomain.com').split(',')

# Security settings
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'

# Session security
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'
SESSION_COOKIE_AGE = 3600  # 1 hour

# CSRF protection
CSRF_COOKIE_SECURE = True
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Lax'
CSRF_TRUSTED_ORIGINS = [
    'https://yourdomain.com',
    'https://www.yourdomain.com',
]

# CORS settings for httpOnly cookies
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [
    'https://yourdomain.com',
    'https://www.yourdomain.com',
]

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/ems/django.log',
        },
        'security_file': {
            'level': 'WARNING',
            'class': 'logging.FileHandler',
            'filename': '/var/log/ems/security.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
        'ems.auth_views': {
            'handlers': ['security_file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
EOF
    
    # Update Gunicorn configuration
    cat > "$PROJECT_ROOT/backend/gunicorn.conf.py" << 'EOF'
import multiprocessing

# Server socket
bind = "127.0.0.1:8000"
backlog = 2048

# Worker processes
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2

# Restart workers after this many requests, to prevent memory leaks
max_requests = 1000
max_requests_jitter = 50

# Logging
accesslog = "/var/log/ems/gunicorn_access.log"
errorlog = "/var/log/ems/gunicorn_error.log"
loglevel = "info"

# Process naming
proc_name = "ems_backend"

# Server mechanics
daemon = False
pidfile = "/var/run/ems/gunicorn.pid"
user = "www-data"
group = "www-data"
tmp_upload_dir = None

# SSL (if terminating SSL at Gunicorn level)
# keyfile = "/path/to/ssl/private.key"
# certfile = "/path/to/ssl/cert.pem"
EOF
    
    success "Configuration files updated"
}

# Restart services
restart_services() {
    log "🔄 Restarting services..."
    
    # Create systemd service file if it doesn't exist
    if [ ! -f "/etc/systemd/system/ems-backend.service" ]; then
        log "Creating systemd service file..."
        cat > "/etc/systemd/system/ems-backend.service" << EOF
[Unit]
Description=EMS Backend (Gunicorn)
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=$PROJECT_ROOT/backend
Environment=PATH=$VENV_PATH/bin
Environment=DJANGO_SETTINGS_MODULE=backend.settings_production
ExecStart=$VENV_PATH/bin/gunicorn --config gunicorn.conf.py backend.wsgi:application
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF
        systemctl daemon-reload
        systemctl enable ems-backend
    fi
    
    # Restart backend service
    log "Restarting EMS backend service..."
    systemctl restart ems-backend
    
    # Check service status
    if systemctl is-active --quiet ems-backend; then
        success "EMS backend service restarted successfully"
    else
        error "Failed to restart EMS backend service"
    fi
    
    # Restart Nginx
    log "Restarting Nginx..."
    if systemctl restart nginx; then
        success "Nginx restarted successfully"
    else
        error "Failed to restart Nginx"
    fi
}

# Post-deployment validation
post_deployment_validation() {
    log "✅ Running post-deployment validation..."
    
    # Wait for services to start
    sleep 5
    
    # Test health endpoint
    log "Testing health endpoint..."
    if curl -f http://localhost:8000/api/health/ > /dev/null 2>&1; then
        success "Health endpoint responding"
    else
        error "Health endpoint not responding"
    fi
    
    # Test authentication endpoint
    log "Testing authentication endpoint..."
    RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" -X POST http://localhost:8000/api/auth/login/ \
        -H "Content-Type: application/json" \
        -d '{"username":"testuser","password":"testpass123"}')
    
    if [ "$RESPONSE" = "200" ] || [ "$RESPONSE" = "400" ]; then
        success "Authentication endpoint responding (HTTP $RESPONSE)"
    else
        error "Authentication endpoint not responding properly (HTTP $RESPONSE)"
    fi
    
    # Check log files
    log "Checking log files..."
    if [ -f "/var/log/ems/django.log" ]; then
        success "Django log file created"
    else
        warning "Django log file not found"
    fi
    
    success "Post-deployment validation completed"
}

# Main deployment function
main() {
    log "🚀 Starting Backend Security Implementation Deployment"
    log "=================================================="
    
    pre_deployment_checks
    create_backup
    deploy_backend
    update_configuration
    restart_services
    post_deployment_validation
    
    success "🎉 Backend deployment completed successfully!"
    log "Backup location: $(cat $BACKUP_DIR/latest_backup.txt)"
    log "Next step: Deploy frontend changes"
}

# Run main function
main "$@"
