#!/usr/bin/env python3
"""
CRITICAL FIX: Advanced Query Performance Analyzer
Real-time query optimization and performance monitoring
"""

import os
import sys
import time
import json
import psutil
from pathlib import Path
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from dataclasses import dataclass
from typing import List, Dict, Any

# Add Django to path
sys.path.append(str(Path(__file__).parent.parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

import django
django.setup()

from django.db import connection, connections
from django.test.utils import override_settings
from django.apps import apps
from django.core.cache import cache

@dataclass
class QueryAnalysis:
    query: str
    execution_time: float
    rows_examined: int
    rows_returned: int
    index_usage: List[str]
    optimization_suggestions: List[str]
    severity: str
    timestamp: datetime

class QueryPerformanceAnalyzer:
    def __init__(self):
        self.analysis_results = {
            "analysis_timestamp": datetime.now().isoformat(),
            "slow_queries": [],
            "n_plus_one_queries": [],
            "missing_indexes": [],
            "optimization_opportunities": [],
            "performance_metrics": {},
            "recommendations": []
        }
        self.query_log = []
        self.monitoring_active = False
        
    def run_comprehensive_analysis(self):
        """Run comprehensive query performance analysis"""
        print("🔍 Starting Comprehensive Query Performance Analysis...")
        print("=" * 70)
        
        try:
            # Enable query logging
            self.enable_query_logging()
            
            # Analyze existing slow queries
            self.analyze_slow_queries()
            
            # Test common query patterns
            self.test_common_query_patterns()
            
            # Detect N+1 query problems
            self.detect_n_plus_one_queries()
            
            # Analyze index usage
            self.analyze_index_usage()
            
            # Check query plan efficiency
            self.analyze_query_plans()
            
            # Generate optimization recommendations
            self.generate_optimization_recommendations()
            
            # Save analysis results
            self.save_analysis_results()
            
            self.print_analysis_summary()
            return True
            
        except Exception as e:
            print(f"💥 Query analysis failed: {e}")
            return False
        finally:
            self.disable_query_logging()
    
    def enable_query_logging(self):
        """Enable detailed query logging"""
        print("📊 Enabling query logging...")
        
        # Enable Django query logging
        import logging
        logging.getLogger('django.db.backends').setLevel(logging.DEBUG)
        
        # Enable PostgreSQL query logging
        with connection.cursor() as cursor:
            cursor.execute("SET log_statement = 'all';")
            cursor.execute("SET log_min_duration_statement = 0;")
            cursor.execute("SET log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h ';")
        
        self.monitoring_active = True
        print("  ✅ Query logging enabled")
    
    def disable_query_logging(self):
        """Disable query logging"""
        if self.monitoring_active:
            with connection.cursor() as cursor:
                cursor.execute("SET log_statement = 'none';")
                cursor.execute("SET log_min_duration_statement = -1;")
            
            self.monitoring_active = False
            print("  ✅ Query logging disabled")
    
    def analyze_slow_queries(self):
        """Analyze slow queries from PostgreSQL logs"""
        print("🐌 Analyzing slow queries...")
        
        try:
            with connection.cursor() as cursor:
                # Get slow queries from pg_stat_statements if available
                cursor.execute("""
                    SELECT query, calls, total_time, mean_time, rows, 
                           100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
                    FROM pg_stat_statements 
                    WHERE mean_time > 100  -- Queries taking more than 100ms on average
                    ORDER BY total_time DESC 
                    LIMIT 20;
                """)
                
                slow_queries = cursor.fetchall()
                
                for query_data in slow_queries:
                    query, calls, total_time, mean_time, rows, hit_percent = query_data
                    
                    analysis = QueryAnalysis(
                        query=query[:500] + "..." if len(query) > 500 else query,
                        execution_time=mean_time,
                        rows_examined=rows or 0,
                        rows_returned=rows or 0,
                        index_usage=[],
                        optimization_suggestions=self.suggest_query_optimizations(query, mean_time),
                        severity=self.classify_query_severity(mean_time),
                        timestamp=datetime.now()
                    )
                    
                    self.analysis_results["slow_queries"].append({
                        "query": analysis.query,
                        "mean_execution_time_ms": analysis.execution_time,
                        "total_calls": calls,
                        "total_time_ms": total_time,
                        "cache_hit_percent": hit_percent,
                        "severity": analysis.severity,
                        "suggestions": analysis.optimization_suggestions
                    })
                
                print(f"  📊 Found {len(slow_queries)} slow queries")
                
        except Exception as e:
            print(f"  ⚠️ Could not analyze slow queries: {e}")
            # Fallback to Django query analysis
            self.analyze_django_queries()
    
    def analyze_django_queries(self):
        """Analyze Django ORM queries"""
        print("  🔄 Analyzing Django ORM queries...")
        
        with override_settings(DEBUG=True):
            from django.db import reset_queries
            
            # Test common ORM patterns
            test_queries = [
                self.test_employee_queries,
                self.test_department_queries,
                self.test_leave_request_queries,
                self.test_project_queries
            ]
            
            for test_func in test_queries:
                reset_queries()
                start_time = time.time()
                
                try:
                    test_func()
                    execution_time = (time.time() - start_time) * 1000
                    query_count = len(connection.queries)
                    
                    if execution_time > 100 or query_count > 10:
                        self.analysis_results["slow_queries"].append({
                            "test_function": test_func.__name__,
                            "execution_time_ms": execution_time,
                            "query_count": query_count,
                            "severity": self.classify_query_severity(execution_time),
                            "queries": [q["sql"] for q in connection.queries[-5:]]  # Last 5 queries
                        })
                        
                except Exception as e:
                    print(f"    ⚠️ Test {test_func.__name__} failed: {e}")
    
    def test_employee_queries(self):
        """Test employee-related queries"""
        from ems.models import Employee
        
        # Test potential N+1 query
        employees = Employee.objects.all()[:10]
        for emp in employees:
            _ = emp.user.username  # This could cause N+1
            _ = emp.department.name if emp.department else None
    
    def test_department_queries(self):
        """Test department-related queries"""
        from ems.models import Department
        
        departments = Department.objects.all()
        for dept in departments:
            _ = dept.employee_set.count()  # Potential N+1
    
    def test_leave_request_queries(self):
        """Test leave request queries"""
        from ems.models import LeaveRequest
        
        leave_requests = LeaveRequest.objects.all()[:20]
        for lr in leave_requests:
            _ = lr.employee.user.username
            _ = lr.leave_type.name if lr.leave_type else None
    
    def test_project_queries(self):
        """Test project-related queries"""
        from ems.models import Project
        
        projects = Project.objects.all()[:10]
        for project in projects:
            _ = project.team_members.count()
            _ = project.tasks.count()
    
    def detect_n_plus_one_queries(self):
        """Detect N+1 query patterns"""
        print("🔍 Detecting N+1 query patterns...")
        
        with override_settings(DEBUG=True):
            from django.db import reset_queries
            
            # Test for N+1 patterns
            n_plus_one_tests = [
                ("Employee -> User", self.test_employee_user_n_plus_one),
                ("Employee -> Department", self.test_employee_department_n_plus_one),
                ("Project -> Team Members", self.test_project_team_n_plus_one),
                ("Leave Request -> Employee", self.test_leave_employee_n_plus_one)
            ]
            
            for test_name, test_func in n_plus_one_tests:
                reset_queries()
                
                try:
                    test_func()
                    query_count = len(connection.queries)
                    
                    # Detect N+1 pattern (more than 3 queries for simple operations)
                    if query_count > 3:
                        self.analysis_results["n_plus_one_queries"].append({
                            "test_name": test_name,
                            "query_count": query_count,
                            "severity": "high" if query_count > 10 else "medium",
                            "fix_suggestion": self.get_n_plus_one_fix(test_name)
                        })
                        
                        print(f"  ❌ N+1 detected in {test_name}: {query_count} queries")
                    else:
                        print(f"  ✅ {test_name}: {query_count} queries (optimized)")
                        
                except Exception as e:
                    print(f"  ⚠️ N+1 test {test_name} failed: {e}")
    
    def test_employee_user_n_plus_one(self):
        """Test Employee -> User N+1 pattern"""
        from ems.models import Employee
        
        employees = Employee.objects.all()[:5]
        usernames = [emp.user.username for emp in employees]
        return usernames
    
    def test_employee_department_n_plus_one(self):
        """Test Employee -> Department N+1 pattern"""
        from ems.models import Employee
        
        employees = Employee.objects.all()[:5]
        dept_names = [emp.department.name if emp.department else None for emp in employees]
        return dept_names
    
    def test_project_team_n_plus_one(self):
        """Test Project -> Team Members N+1 pattern"""
        from ems.models import Project
        
        projects = Project.objects.all()[:3]
        team_counts = [project.team_members.count() for project in projects]
        return team_counts
    
    def test_leave_employee_n_plus_one(self):
        """Test Leave Request -> Employee N+1 pattern"""
        from ems.models import LeaveRequest
        
        leave_requests = LeaveRequest.objects.all()[:5]
        employee_names = [lr.employee.user.get_full_name() for lr in leave_requests]
        return employee_names
    
    def get_n_plus_one_fix(self, test_name):
        """Get fix suggestion for N+1 query"""
        
        fixes = {
            "Employee -> User": "Use select_related('user') in Employee queries",
            "Employee -> Department": "Use select_related('department') in Employee queries",
            "Project -> Team Members": "Use prefetch_related('team_members') in Project queries",
            "Leave Request -> Employee": "Use select_related('employee__user') in LeaveRequest queries"
        }
        
        return fixes.get(test_name, "Use select_related() or prefetch_related() as appropriate")
    
    def analyze_index_usage(self):
        """Analyze database index usage"""
        print("📇 Analyzing index usage...")
        
        try:
            with connection.cursor() as cursor:
                # Get index usage statistics
                cursor.execute("""
                    SELECT 
                        schemaname,
                        tablename,
                        indexname,
                        idx_tup_read,
                        idx_tup_fetch,
                        idx_scan
                    FROM pg_stat_user_indexes 
                    ORDER BY idx_scan DESC;
                """)
                
                index_stats = cursor.fetchall()
                
                # Find unused indexes
                unused_indexes = []
                for stat in index_stats:
                    schema, table, index, tup_read, tup_fetch, scan_count = stat
                    
                    if scan_count == 0:
                        unused_indexes.append({
                            "table": table,
                            "index": index,
                            "suggestion": f"Consider dropping unused index {index} on {table}"
                        })
                
                self.analysis_results["missing_indexes"] = unused_indexes
                print(f"  📊 Found {len(unused_indexes)} unused indexes")
                
                # Suggest missing indexes based on slow queries
                self.suggest_missing_indexes()
                
        except Exception as e:
            print(f"  ⚠️ Could not analyze index usage: {e}")
    
    def suggest_missing_indexes(self):
        """Suggest missing indexes based on query patterns"""
        
        # Common index suggestions based on EMS schema
        suggested_indexes = [
            {
                "table": "ems_employee",
                "columns": ["department_id", "is_active"],
                "reason": "Frequent filtering by department and active status"
            },
            {
                "table": "ems_leaverequest", 
                "columns": ["employee_id", "status", "start_date"],
                "reason": "Common leave request queries"
            },
            {
                "table": "ems_attendance",
                "columns": ["employee_id", "date"],
                "reason": "Attendance lookups by employee and date"
            },
            {
                "table": "ems_task",
                "columns": ["assigned_to_id", "status", "due_date"],
                "reason": "Task assignment and status queries"
            }
        ]
        
        # Check if these indexes exist
        with connection.cursor() as cursor:
            for suggestion in suggested_indexes:
                table = suggestion["table"]
                columns = suggestion["columns"]
                
                # Check if similar index exists
                cursor.execute("""
                    SELECT indexname 
                    FROM pg_indexes 
                    WHERE tablename = %s 
                    AND indexdef LIKE %s
                """, [table, f"%{columns[0]}%"])
                
                existing = cursor.fetchall()
                
                if not existing:
                    self.analysis_results["missing_indexes"].append({
                        "table": table,
                        "suggested_columns": columns,
                        "reason": suggestion["reason"],
                        "create_sql": f"CREATE INDEX CONCURRENTLY idx_{table}_{'_'.join(columns)} ON {table}({', '.join(columns)});"
                    })
    
    def analyze_query_plans(self):
        """Analyze query execution plans"""
        print("📋 Analyzing query execution plans...")
        
        # Sample queries to analyze
        sample_queries = [
            "SELECT * FROM ems_employee WHERE department_id = 1 AND is_active = true",
            "SELECT * FROM ems_leaverequest WHERE employee_id = 1 ORDER BY start_date DESC",
            "SELECT * FROM ems_attendance WHERE employee_id = 1 AND date >= '2024-01-01'"
        ]
        
        query_plans = []
        
        with connection.cursor() as cursor:
            for query in sample_queries:
                try:
                    # Get query execution plan
                    cursor.execute(f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query}")
                    plan = cursor.fetchone()[0]
                    
                    # Analyze plan for performance issues
                    issues = self.analyze_execution_plan(plan[0])
                    
                    query_plans.append({
                        "query": query,
                        "execution_plan": plan[0],
                        "performance_issues": issues
                    })
                    
                except Exception as e:
                    print(f"  ⚠️ Could not analyze plan for query: {e}")
        
        self.analysis_results["query_plans"] = query_plans
    
    def analyze_execution_plan(self, plan):
        """Analyze execution plan for performance issues"""
        
        issues = []
        
        # Check for sequential scans on large tables
        if plan.get("Node Type") == "Seq Scan":
            rows = plan.get("Actual Rows", 0)
            if rows > 1000:
                issues.append({
                    "type": "sequential_scan",
                    "severity": "high",
                    "description": f"Sequential scan on {rows} rows - consider adding index"
                })
        
        # Check for nested loops with high cost
        if plan.get("Node Type") == "Nested Loop":
            cost = plan.get("Total Cost", 0)
            if cost > 1000:
                issues.append({
                    "type": "expensive_nested_loop",
                    "severity": "medium", 
                    "description": f"Expensive nested loop (cost: {cost}) - consider join optimization"
                })
        
        # Recursively check child plans
        for child in plan.get("Plans", []):
            issues.extend(self.analyze_execution_plan(child))
        
        return issues
    
    def suggest_query_optimizations(self, query, execution_time):
        """Suggest optimizations for slow queries"""
        
        suggestions = []
        
        if execution_time > 1000:  # > 1 second
            suggestions.append("Critical: Query takes over 1 second - immediate optimization needed")
        
        if "SELECT *" in query.upper():
            suggestions.append("Avoid SELECT * - specify only needed columns")
        
        if "ORDER BY" in query.upper() and "LIMIT" not in query.upper():
            suggestions.append("Consider adding LIMIT to ORDER BY queries")
        
        if "WHERE" not in query.upper():
            suggestions.append("Add WHERE clause to filter results")
        
        if query.upper().count("JOIN") > 3:
            suggestions.append("Complex joins detected - consider query restructuring")
        
        return suggestions
    
    def classify_query_severity(self, execution_time):
        """Classify query severity based on execution time"""
        
        if execution_time > 1000:
            return "critical"
        elif execution_time > 500:
            return "high"
        elif execution_time > 100:
            return "medium"
        else:
            return "low"
    
    def test_common_query_patterns(self):
        """Test common query patterns for performance"""
        print("🧪 Testing common query patterns...")
        
        patterns = [
            ("Dashboard Stats", self.test_dashboard_queries),
            ("Employee List", self.test_employee_list_queries),
            ("Leave Requests", self.test_leave_request_queries),
            ("Project Management", self.test_project_management_queries)
        ]
        
        for pattern_name, test_func in patterns:
            with override_settings(DEBUG=True):
                from django.db import reset_queries
                reset_queries()
                
                start_time = time.time()
                
                try:
                    test_func()
                    execution_time = (time.time() - start_time) * 1000
                    query_count = len(connection.queries)
                    
                    self.analysis_results["performance_metrics"][pattern_name] = {
                        "execution_time_ms": execution_time,
                        "query_count": query_count,
                        "queries_per_second": query_count / (execution_time / 1000) if execution_time > 0 else 0,
                        "severity": self.classify_query_severity(execution_time)
                    }
                    
                    print(f"  📊 {pattern_name}: {execution_time:.2f}ms, {query_count} queries")
                    
                except Exception as e:
                    print(f"  ⚠️ Pattern test {pattern_name} failed: {e}")
    
    def test_dashboard_queries(self):
        """Test dashboard-related queries"""
        from ems.models import Employee, Department, LeaveRequest, Project
        
        # Simulate dashboard data loading
        total_employees = Employee.objects.count()
        active_employees = Employee.objects.filter(is_active=True).count()
        total_departments = Department.objects.count()
        pending_leaves = LeaveRequest.objects.filter(status='pending').count()
        active_projects = Project.objects.filter(status='active').count()
        
        return {
            "total_employees": total_employees,
            "active_employees": active_employees,
            "total_departments": total_departments,
            "pending_leaves": pending_leaves,
            "active_projects": active_projects
        }
    
    def test_employee_list_queries(self):
        """Test employee list queries"""
        from ems.models import Employee
        
        # Test optimized vs unoptimized queries
        employees = list(Employee.objects.select_related('user', 'department')[:50])
        return len(employees)
    
    def test_leave_request_queries(self):
        """Test leave request queries"""
        from ems.models import LeaveRequest
        
        leave_requests = list(LeaveRequest.objects.select_related(
            'employee__user', 'leave_type', 'approved_by__user'
        )[:50])
        return len(leave_requests)
    
    def test_project_management_queries(self):
        """Test project management queries"""
        from ems.models import Project
        
        projects = list(Project.objects.select_related('project_manager__user', 'department')
                       .prefetch_related('team_members', 'tasks')[:20])
        return len(projects)
    
    def generate_optimization_recommendations(self):
        """Generate comprehensive optimization recommendations"""
        print("💡 Generating optimization recommendations...")
        
        recommendations = []
        
        # Analyze slow queries
        slow_query_count = len(self.analysis_results["slow_queries"])
        if slow_query_count > 0:
            recommendations.append({
                "category": "Query Optimization",
                "priority": "high",
                "description": f"Found {slow_query_count} slow queries requiring optimization",
                "action": "Review and optimize slow queries using suggested improvements"
            })
        
        # Analyze N+1 queries
        n_plus_one_count = len(self.analysis_results["n_plus_one_queries"])
        if n_plus_one_count > 0:
            recommendations.append({
                "category": "ORM Optimization", 
                "priority": "high",
                "description": f"Found {n_plus_one_count} N+1 query patterns",
                "action": "Implement select_related() and prefetch_related() optimizations"
            })
        
        # Analyze missing indexes
        missing_index_count = len(self.analysis_results["missing_indexes"])
        if missing_index_count > 0:
            recommendations.append({
                "category": "Database Indexing",
                "priority": "medium",
                "description": f"Found {missing_index_count} missing index opportunities",
                "action": "Create suggested database indexes for better query performance"
            })
        
        # Performance metrics analysis
        critical_patterns = [name for name, metrics in self.analysis_results["performance_metrics"].items() 
                           if metrics.get("severity") in ["critical", "high"]]
        
        if critical_patterns:
            recommendations.append({
                "category": "Performance Patterns",
                "priority": "high", 
                "description": f"Critical performance issues in: {', '.join(critical_patterns)}",
                "action": "Optimize critical query patterns and consider caching strategies"
            })
        
        self.analysis_results["recommendations"] = recommendations
        
        print(f"  📋 Generated {len(recommendations)} optimization recommendations")
    
    def save_analysis_results(self):
        """Save analysis results to file"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"query_performance_analysis_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(self.analysis_results, f, indent=2, default=str)
        
        print(f"\n📄 Analysis results saved to: {filename}")
    
    def print_analysis_summary(self):
        """Print comprehensive analysis summary"""
        
        print("\n" + "=" * 70)
        print("📊 QUERY PERFORMANCE ANALYSIS SUMMARY")
        print("=" * 70)
        
        # Summary statistics
        slow_queries = len(self.analysis_results["slow_queries"])
        n_plus_one = len(self.analysis_results["n_plus_one_queries"])
        missing_indexes = len(self.analysis_results["missing_indexes"])
        recommendations = len(self.analysis_results["recommendations"])
        
        print(f"\n🎯 Analysis Results:")
        print(f"  • Slow queries found: {slow_queries}")
        print(f"  • N+1 query patterns: {n_plus_one}")
        print(f"  • Missing indexes: {missing_indexes}")
        print(f"  • Optimization recommendations: {recommendations}")
        
        # Performance metrics summary
        if self.analysis_results["performance_metrics"]:
            print(f"\n⚡ Performance Metrics:")
            for pattern, metrics in self.analysis_results["performance_metrics"].items():
                severity_icon = {"critical": "🔴", "high": "🟠", "medium": "🟡", "low": "🟢"}
                icon = severity_icon.get(metrics["severity"], "⚪")
                print(f"  {icon} {pattern}: {metrics['execution_time_ms']:.2f}ms, {metrics['query_count']} queries")
        
        # Top recommendations
        if self.analysis_results["recommendations"]:
            print(f"\n💡 Top Recommendations:")
            for rec in self.analysis_results["recommendations"][:3]:
                priority_icon = {"high": "🔴", "medium": "🟡", "low": "🟢"}
                icon = priority_icon.get(rec["priority"], "⚪")
                print(f"  {icon} {rec['category']}: {rec['description']}")
        
        # Overall assessment
        total_issues = slow_queries + n_plus_one + missing_indexes
        if total_issues == 0:
            print(f"\n🎉 Excellent! No major performance issues found.")
        elif total_issues < 5:
            print(f"\n✅ Good performance with minor optimization opportunities.")
        elif total_issues < 10:
            print(f"\n⚠️ Moderate performance issues requiring attention.")
        else:
            print(f"\n🚨 Significant performance issues requiring immediate optimization.")

if __name__ == '__main__':
    analyzer = QueryPerformanceAnalyzer()
    success = analyzer.run_comprehensive_analysis()
    sys.exit(0 if success else 1)
