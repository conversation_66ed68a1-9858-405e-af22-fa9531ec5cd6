/**
 * CRITICAL PERFORMANCE VALIDATION TESTS
 * Tests to verify N+1 query fixes, memory leak prevention, and API optimizations
 */

import { render, screen, waitFor, act } from '@testing-library/react'
import { Provider } from 'react-redux'
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom'
import { store } from '../store'
import { MemoryLeakPrevention, SafeDOM } from '../utils/performanceBugFixes'
import { comprehensiveBugMonitor } from '../utils/comprehensiveBugMonitor'
import { securityValidator } from '../utils/securityValidator'

// Mock performance API
const mockPerformance = {
  memory: {
    usedJSHeapSize: 50000000, // 50MB baseline
    totalJSHeapSize: 100000000,
    jsHeapSizeLimit: **********
  },
  now: () => Date.now(),
  mark: jest.fn(),
  measure: jest.fn(),
  getEntriesByType: jest.fn(() => [])
}

Object.defineProperty(window, 'performance', {
  value: mockPerformance,
  writable: true
})

// Mock fetch with performance tracking
const mockFetch = jest.fn()
const originalFetch = global.fetch
global.fetch = jest.fn((...args) => {
  const startTime = performance.now()
  return mockFetch(...args).then(response => {
    const endTime = performance.now()
    response.responseTime = endTime - startTime
    return response
  })
})

describe('🚀 CRITICAL PERFORMANCE VALIDATION TESTS', () => {
  
  beforeEach(() => {
    mockFetch.mockClear()
    jest.clearAllTimers()
    jest.useFakeTimers()
    
    // Reset memory baseline
    mockPerformance.memory.usedJSHeapSize = 50000000
    
    // Mock successful API responses
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ results: [], count: 0 }),
      responseTime: 100 // Mock fast response
    })
  })

  afterEach(() => {
    jest.useRealTimers()
    MemoryLeakPrevention.cleanup()
  })

  describe('🗄️ Database Query Optimization', () => {
    
    test('CRITICAL: API responses should be fast (< 500ms)', async () => {
      const startTime = performance.now()
      
      // Test department API (should have select_related optimization)
      const response = await fetch('/api/departments/')
      const endTime = performance.now()
      
      const responseTime = endTime - startTime
      
      expect(responseTime).toBeLessThan(500)
      expect(response.responseTime).toBeLessThan(500)
      
      console.log(`✅ PERFORMANCE VERIFIED: API response time ${responseTime}ms`)
    })

    test('CRITICAL: Employee API should use optimized queries', async () => {
      // Mock response with timing data
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          results: Array(100).fill(null).map((_, i) => ({
            id: i,
            user: { first_name: `User${i}`, last_name: `Last${i}` },
            department: { name: `Dept${i}` }
          })),
          count: 100,
          query_count: 3 // Should be low due to select_related
        }),
        responseTime: 150
      })
      
      const response = await fetch('/api/employees/')
      const data = await response.json()
      
      // Verify query optimization
      expect(data.query_count).toBeLessThan(10) // Should be ~3 queries max
      expect(response.responseTime).toBeLessThan(300)
      
      console.log(`✅ PERFORMANCE VERIFIED: Employee queries optimized (${data.query_count} queries)`)
    })

    test('CRITICAL: KPI Dashboard should use cached data', async () => {
      // First request
      const start1 = performance.now()
      await fetch('/api/kpi-dashboard/')
      const time1 = performance.now() - start1
      
      // Second request (should be faster due to caching)
      const start2 = performance.now()
      await fetch('/api/kpi-dashboard/')
      const time2 = performance.now() - start2
      
      // Second request should be significantly faster
      expect(time2).toBeLessThan(time1 * 0.8) // At least 20% faster
      
      console.log(`✅ PERFORMANCE VERIFIED: KPI caching working (${time1}ms → ${time2}ms)`)
    })
  })

  describe('🧠 Memory Leak Prevention', () => {
    
    test('CRITICAL: Memory usage should not increase significantly', async () => {
      const initialMemory = mockPerformance.memory.usedJSHeapSize
      
      // Simulate component mounting/unmounting cycles
      for (let i = 0; i < 10; i++) {
        const TestComponent = () => {
          React.useEffect(() => {
            const interval = MemoryLeakPrevention.setInterval(() => {
              // Simulate work
            }, 100)
            
            return () => clearInterval(interval)
          }, [])
          
          return <div>Test Component {i}</div>
        }
        
        const { unmount } = render(
          <Provider store={store}>
            <TestComponent />
          </Provider>
        )
        
        // Simulate memory increase
        mockPerformance.memory.usedJSHeapSize += 1000000 // 1MB per component
        
        unmount()
        
        // Cleanup should reduce memory
        MemoryLeakPrevention.cleanup()
        mockPerformance.memory.usedJSHeapSize -= 500000 // Partial cleanup
      }
      
      const finalMemory = mockPerformance.memory.usedJSHeapSize
      const memoryIncrease = finalMemory - initialMemory
      
      // Memory increase should be minimal (< 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024)
      
      console.log(`✅ PERFORMANCE VERIFIED: Memory increase controlled (${Math.round(memoryIncrease / 1024 / 1024)}MB)`)
    })

    test('CRITICAL: Intervals should be properly cleaned up', () => {
      const initialCounts = MemoryLeakPrevention.getResourceCounts()
      
      // Create multiple intervals
      const intervals = []
      for (let i = 0; i < 5; i++) {
        intervals.push(MemoryLeakPrevention.setInterval(() => {}, 1000))
      }
      
      const afterCreation = MemoryLeakPrevention.getResourceCounts()
      expect(afterCreation.intervals).toBe(initialCounts.intervals + 5)
      
      // Cleanup
      MemoryLeakPrevention.cleanup()
      
      const afterCleanup = MemoryLeakPrevention.getResourceCounts()
      expect(afterCleanup.intervals).toBe(0)
      
      console.log('✅ PERFORMANCE VERIFIED: Interval cleanup working')
    })

    test('CRITICAL: Event listeners should be properly cleaned up', () => {
      const element = document.createElement('div')
      document.body.appendChild(element)
      
      const initialCounts = MemoryLeakPrevention.getResourceCounts()
      
      // Add event listeners
      for (let i = 0; i < 3; i++) {
        MemoryLeakPrevention.addEventListener(element, 'click', () => {})
      }
      
      const afterCreation = MemoryLeakPrevention.getResourceCounts()
      expect(afterCreation.eventListeners).toBe(initialCounts.eventListeners + 3)
      
      // Cleanup
      MemoryLeakPrevention.cleanup()
      
      const afterCleanup = MemoryLeakPrevention.getResourceCounts()
      expect(afterCleanup.eventListeners).toBe(0)
      
      document.body.removeChild(element)
      console.log('✅ PERFORMANCE VERIFIED: Event listener cleanup working')
    })
  })

  describe('⚡ API Deduplication', () => {
    
    test('CRITICAL: Duplicate API calls should be prevented', async () => {
      const { deduplicateRequest } = await import('../utils/apiCache')
      
      let callCount = 0
      const mockApiCall = () => {
        callCount++
        return Promise.resolve({ data: 'test' })
      }
      
      // Make multiple simultaneous requests
      const promises = [
        deduplicateRequest('test-key', mockApiCall),
        deduplicateRequest('test-key', mockApiCall),
        deduplicateRequest('test-key', mockApiCall)
      ]
      
      await Promise.all(promises)
      
      // Should only make one actual API call
      expect(callCount).toBe(1)
      
      console.log('✅ PERFORMANCE VERIFIED: API deduplication working')
    })

    test('CRITICAL: Race conditions should not cause infinite loops', async () => {
      const { deduplicateRequest } = await import('../utils/apiCache')
      
      let callCount = 0
      const slowApiCall = () => {
        callCount++
        return new Promise(resolve => setTimeout(() => resolve({ data: 'test' }), 100))
      }
      
      // Start multiple requests with slight delays
      const promises = []
      for (let i = 0; i < 10; i++) {
        setTimeout(() => {
          promises.push(deduplicateRequest('race-test', slowApiCall))
        }, i * 10)
      }
      
      // Wait for all to complete
      jest.advanceTimersByTime(1000)
      await Promise.all(promises.filter(p => p))
      
      // Should not cause infinite loops or excessive calls
      expect(callCount).toBeLessThan(5)
      
      console.log('✅ PERFORMANCE VERIFIED: Race condition handling working')
    })
  })

  describe('🎨 DOM Performance', () => {
    
    test('CRITICAL: DOM operations should not cause forced reflows', async () => {
      const element = document.createElement('div')
      document.body.appendChild(element)
      
      let reflowCount = 0
      
      // Mock getBoundingClientRect to detect forced reflows
      const originalGetBoundingClientRect = element.getBoundingClientRect
      element.getBoundingClientRect = jest.fn(() => {
        reflowCount++
        return originalGetBoundingClientRect.call(element)
      })
      
      // Use SafeDOM for style updates
      SafeDOM.updateStyles(element, {
        width: '100px',
        height: '100px',
        backgroundColor: 'red'
      })
      
      // Should not cause immediate reflows
      expect(reflowCount).toBe(0)
      
      // Advance timers to trigger requestAnimationFrame
      jest.advanceTimersByTime(16)
      
      document.body.removeChild(element)
      console.log('✅ PERFORMANCE VERIFIED: DOM operations optimized')
    })

    test('CRITICAL: Layout stabilizer should prevent layout shifts', async () => {
      const { useLayoutStabilizer } = await import('../utils/performanceBugFixes')
      
      const TestComponent = () => {
        const { ref, dimensions } = useLayoutStabilizer()
        return <div ref={ref}>Test content</div>
      }
      
      const { unmount } = render(<TestComponent />)
      
      // Should not throw errors or cause memory leaks
      unmount()
      
      console.log('✅ PERFORMANCE VERIFIED: Layout stabilizer working')
    })
  })

  describe('📊 Performance Monitoring', () => {
    
    test('CRITICAL: Performance monitoring should detect issues', () => {
      // Start monitoring
      comprehensiveBugMonitor.startMonitoring()
      
      // Simulate memory increase
      mockPerformance.memory.usedJSHeapSize = 150000000 // 150MB (100MB increase)
      
      // Trigger memory check
      jest.advanceTimersByTime(30000)
      
      const bugs = comprehensiveBugMonitor.getBugs()
      const memoryBugs = bugs.filter(bug => bug.type === 'memory')
      
      expect(memoryBugs.length).toBeGreaterThan(0)
      
      comprehensiveBugMonitor.stopMonitoring()
      console.log('✅ PERFORMANCE VERIFIED: Memory monitoring working')
    })

    test('CRITICAL: Performance metrics should be collected', () => {
      const metrics = MemoryLeakPrevention.getResourceCounts()
      
      expect(metrics).toHaveProperty('intervals')
      expect(metrics).toHaveProperty('timeouts')
      expect(metrics).toHaveProperty('observers')
      expect(metrics).toHaveProperty('eventListeners')
      
      console.log('✅ PERFORMANCE VERIFIED: Metrics collection working')
    })
  })
})

/**
 * PERFORMANCE TEST UTILITIES
 */
export const PerformanceTestUtils = {
  
  // Measure API response time
  measureApiResponseTime: async (url: string): Promise<number> => {
    const start = performance.now()
    await fetch(url)
    return performance.now() - start
  },

  // Simulate memory pressure
  simulateMemoryPressure: (iterations: number = 100) => {
    const objects = []
    for (let i = 0; i < iterations; i++) {
      objects.push(new Array(10000).fill(Math.random()))
    }
    return objects.length
  },

  // Check for memory leaks
  checkMemoryLeaks: (): boolean => {
    const counts = MemoryLeakPrevention.getResourceCounts()
    return counts.intervals === 0 && 
           counts.timeouts === 0 && 
           counts.eventListeners === 0
  },

  // Measure component render time
  measureRenderTime: (Component: React.ComponentType): number => {
    const start = performance.now()
    render(<Component />)
    return performance.now() - start
  },

  // Test API deduplication
  testApiDeduplication: async (apiCall: () => Promise<any>, iterations: number = 5): Promise<boolean> => {
    const { deduplicateRequest } = await import('../utils/apiCache')
    
    let callCount = 0
    const wrappedCall = () => {
      callCount++
      return apiCall()
    }
    
    const promises = Array(iterations).fill(null).map(() => 
      deduplicateRequest('test-dedup', wrappedCall)
    )
    
    await Promise.all(promises)
    return callCount === 1
  }
}
