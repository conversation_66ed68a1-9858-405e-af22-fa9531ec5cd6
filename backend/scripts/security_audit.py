#!/usr/bin/env python3
"""
CRITICAL FIX: Comprehensive Security Audit & Penetration Testing
Enterprise-grade security assessment with automated vulnerability detection
"""

import os
import sys
import json
import requests
import subprocess
import time
import hashlib
from pathlib import Path
from datetime import datetime
from urllib.parse import urljoin
from typing import Dict, List, Any

# Add Django to path
sys.path.append(str(Path(__file__).parent.parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

import django
django.setup()

from django.conf import settings
from django.test import Client
from django.contrib.auth.models import User
from django.core.management import call_command

class SecurityAuditor:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.client = Client()
        self.session = requests.Session()
        self.audit_results = {
            "audit_timestamp": datetime.now().isoformat(),
            "base_url": base_url,
            "vulnerabilities": [],
            "security_headers": {},
            "authentication_tests": {},
            "authorization_tests": {},
            "input_validation_tests": {},
            "configuration_audit": {},
            "recommendations": []
        }
        
    def run_comprehensive_audit(self):
        """Run comprehensive security audit"""
        print("🔒 Starting Comprehensive Security Audit...")
        print("=" * 60)
        
        try:
            # Security configuration audit
            self.audit_security_configuration()
            
            # Security headers analysis
            self.audit_security_headers()
            
            # Authentication security tests
            self.test_authentication_security()
            
            # Authorization tests
            self.test_authorization_security()
            
            # Input validation tests
            self.test_input_validation()
            
            # Session security tests
            self.test_session_security()
            
            # CSRF protection tests
            self.test_csrf_protection()
            
            # SQL injection tests
            self.test_sql_injection()
            
            # XSS vulnerability tests
            self.test_xss_vulnerabilities()
            
            # File upload security tests
            self.test_file_upload_security()
            
            # Rate limiting tests
            self.test_rate_limiting()
            
            # Generate security recommendations
            self.generate_security_recommendations()
            
            # Save audit results
            self.save_audit_results()
            
            self.print_audit_summary()
            return len(self.audit_results["vulnerabilities"]) == 0
            
        except Exception as e:
            print(f"💥 Security audit failed: {e}")
            return False
    
    def audit_security_configuration(self):
        """Audit Django security configuration"""
        print("🔧 Auditing security configuration...")
        
        config_issues = []
        
        # Check DEBUG setting
        if getattr(settings, 'DEBUG', True):
            config_issues.append({
                "setting": "DEBUG",
                "issue": "DEBUG is enabled in production",
                "severity": "high",
                "recommendation": "Set DEBUG = False in production"
            })
        
        # Check SECRET_KEY
        secret_key = getattr(settings, 'SECRET_KEY', '')
        if not secret_key or len(secret_key) < 50:
            config_issues.append({
                "setting": "SECRET_KEY",
                "issue": "Weak or missing SECRET_KEY",
                "severity": "critical",
                "recommendation": "Use a strong, randomly generated SECRET_KEY"
            })
        
        # Check ALLOWED_HOSTS
        allowed_hosts = getattr(settings, 'ALLOWED_HOSTS', [])
        if '*' in allowed_hosts:
            config_issues.append({
                "setting": "ALLOWED_HOSTS",
                "issue": "Wildcard in ALLOWED_HOSTS",
                "severity": "medium",
                "recommendation": "Specify exact hostnames instead of using '*'"
            })
        
        # Check SECURE_SSL_REDIRECT
        if not getattr(settings, 'SECURE_SSL_REDIRECT', False):
            config_issues.append({
                "setting": "SECURE_SSL_REDIRECT",
                "issue": "SSL redirect not enforced",
                "severity": "medium",
                "recommendation": "Set SECURE_SSL_REDIRECT = True"
            })
        
        # Check SECURE_HSTS_SECONDS
        hsts_seconds = getattr(settings, 'SECURE_HSTS_SECONDS', 0)
        if hsts_seconds < 31536000:  # 1 year
            config_issues.append({
                "setting": "SECURE_HSTS_SECONDS",
                "issue": "HSTS not configured or too short",
                "severity": "medium",
                "recommendation": "Set SECURE_HSTS_SECONDS = 31536000 (1 year)"
            })
        
        # Check SESSION_COOKIE_SECURE
        if not getattr(settings, 'SESSION_COOKIE_SECURE', False):
            config_issues.append({
                "setting": "SESSION_COOKIE_SECURE",
                "issue": "Session cookies not marked as secure",
                "severity": "medium",
                "recommendation": "Set SESSION_COOKIE_SECURE = True"
            })
        
        # Check CSRF_COOKIE_SECURE
        if not getattr(settings, 'CSRF_COOKIE_SECURE', False):
            config_issues.append({
                "setting": "CSRF_COOKIE_SECURE",
                "issue": "CSRF cookies not marked as secure",
                "severity": "medium",
                "recommendation": "Set CSRF_COOKIE_SECURE = True"
            })
        
        self.audit_results["configuration_audit"] = config_issues
        
        if config_issues:
            print(f"  ❌ Found {len(config_issues)} configuration issues")
            for issue in config_issues:
                self.add_vulnerability("configuration", issue["issue"], issue["severity"], issue)
        else:
            print(f"  ✅ Security configuration looks good")
    
    def audit_security_headers(self):
        """Audit HTTP security headers"""
        print("🛡️ Auditing security headers...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/health/")
            headers = response.headers
            
            # Required security headers
            required_headers = {
                'X-Content-Type-Options': 'nosniff',
                'X-Frame-Options': ['DENY', 'SAMEORIGIN'],
                'X-XSS-Protection': '1; mode=block',
                'Strict-Transport-Security': None,  # Should exist
                'Content-Security-Policy': None,    # Should exist
                'Referrer-Policy': ['strict-origin-when-cross-origin', 'no-referrer']
            }
            
            header_results = {}
            
            for header, expected_values in required_headers.items():
                header_value = headers.get(header)
                
                if not header_value:
                    header_results[header] = {
                        "present": False,
                        "value": None,
                        "status": "missing",
                        "severity": "medium"
                    }
                    self.add_vulnerability("headers", f"Missing security header: {header}", "medium", {
                        "header": header,
                        "recommendation": f"Add {header} header to responses"
                    })
                else:
                    is_valid = True
                    if expected_values and isinstance(expected_values, list):
                        is_valid = header_value in expected_values
                    
                    header_results[header] = {
                        "present": True,
                        "value": header_value,
                        "status": "valid" if is_valid else "invalid",
                        "severity": "low" if is_valid else "medium"
                    }
                    
                    if not is_valid and expected_values:
                        self.add_vulnerability("headers", f"Invalid {header} header value", "medium", {
                            "header": header,
                            "current_value": header_value,
                            "expected_values": expected_values
                        })
            
            # Check for information disclosure headers
            disclosure_headers = ['Server', 'X-Powered-By', 'X-AspNet-Version']
            for header in disclosure_headers:
                if headers.get(header):
                    header_results[header] = {
                        "present": True,
                        "value": headers[header],
                        "status": "information_disclosure",
                        "severity": "low"
                    }
                    self.add_vulnerability("headers", f"Information disclosure header: {header}", "low", {
                        "header": header,
                        "value": headers[header],
                        "recommendation": f"Remove or obfuscate {header} header"
                    })
            
            self.audit_results["security_headers"] = header_results
            
            missing_count = sum(1 for h in header_results.values() if not h["present"])
            if missing_count > 0:
                print(f"  ❌ {missing_count} security headers missing")
            else:
                print(f"  ✅ All security headers present")
                
        except Exception as e:
            print(f"  ⚠️ Could not audit security headers: {e}")
    
    def test_authentication_security(self):
        """Test authentication security"""
        print("🔐 Testing authentication security...")
        
        auth_tests = {}
        
        # Test 1: Brute force protection
        auth_tests["brute_force_protection"] = self.test_brute_force_protection()
        
        # Test 2: Password policy
        auth_tests["password_policy"] = self.test_password_policy()
        
        # Test 3: Account lockout
        auth_tests["account_lockout"] = self.test_account_lockout()
        
        # Test 4: Session fixation
        auth_tests["session_fixation"] = self.test_session_fixation()
        
        # Test 5: Credential enumeration
        auth_tests["credential_enumeration"] = self.test_credential_enumeration()
        
        self.audit_results["authentication_tests"] = auth_tests
    
    def test_brute_force_protection(self):
        """Test brute force protection"""
        
        try:
            # Attempt multiple failed logins
            failed_attempts = 0
            for i in range(10):
                response = self.session.post(f"{self.base_url}/api/auth/login/", {
                    'username': 'testuser',
                    'password': f'wrongpassword{i}'
                })
                
                if response.status_code == 429:  # Rate limited
                    return {
                        "protected": True,
                        "attempts_before_limit": i,
                        "status": "pass"
                    }
                
                failed_attempts += 1
            
            # If we get here, no rate limiting was applied
            self.add_vulnerability("authentication", "No brute force protection detected", "high", {
                "test": "brute_force_protection",
                "attempts": failed_attempts,
                "recommendation": "Implement rate limiting for login attempts"
            })
            
            return {
                "protected": False,
                "attempts": failed_attempts,
                "status": "fail"
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "status": "error"
            }
    
    def test_password_policy(self):
        """Test password policy enforcement"""
        
        weak_passwords = [
            "123456",
            "password",
            "admin",
            "test",
            "abc123",
            "qwerty"
        ]
        
        policy_results = {
            "weak_passwords_rejected": 0,
            "total_tested": len(weak_passwords),
            "status": "unknown"
        }
        
        try:
            for password in weak_passwords:
                # Try to create user with weak password
                response = self.session.post(f"{self.base_url}/api/auth/register/", {
                    'username': f'testuser_{hashlib.md5(password.encode()).hexdigest()[:8]}',
                    'password': password,
                    'email': f'test_{hashlib.md5(password.encode()).hexdigest()[:8]}@example.com'
                })
                
                if response.status_code == 400:  # Password rejected
                    policy_results["weak_passwords_rejected"] += 1
            
            rejection_rate = policy_results["weak_passwords_rejected"] / policy_results["total_tested"]
            
            if rejection_rate >= 0.8:  # 80% of weak passwords rejected
                policy_results["status"] = "pass"
            else:
                policy_results["status"] = "fail"
                self.add_vulnerability("authentication", "Weak password policy", "medium", {
                    "test": "password_policy",
                    "rejection_rate": rejection_rate,
                    "recommendation": "Implement stronger password requirements"
                })
            
        except Exception as e:
            policy_results["error"] = str(e)
            policy_results["status"] = "error"
        
        return policy_results
    
    def test_account_lockout(self):
        """Test account lockout mechanism"""
        
        # This would require creating a test user and attempting lockout
        # For now, return a placeholder result
        return {
            "implemented": False,
            "status": "not_tested",
            "recommendation": "Implement account lockout after multiple failed attempts"
        }
    
    def test_session_fixation(self):
        """Test session fixation vulnerability"""
        
        try:
            # Get initial session
            response1 = self.session.get(f"{self.base_url}/api/health/")
            session_id_1 = self.session.cookies.get('sessionid')
            
            # Attempt login
            login_response = self.session.post(f"{self.base_url}/api/auth/login/", {
                'username': 'admin',
                'password': 'wrongpassword'
            })
            
            # Check if session ID changed
            session_id_2 = self.session.cookies.get('sessionid')
            
            if session_id_1 == session_id_2:
                self.add_vulnerability("authentication", "Session fixation vulnerability", "medium", {
                    "test": "session_fixation",
                    "recommendation": "Regenerate session ID after login attempts"
                })
                return {"vulnerable": True, "status": "fail"}
            else:
                return {"vulnerable": False, "status": "pass"}
                
        except Exception as e:
            return {"error": str(e), "status": "error"}
    
    def test_credential_enumeration(self):
        """Test for username enumeration vulnerabilities"""
        
        try:
            # Test with valid vs invalid usernames
            valid_response = self.session.post(f"{self.base_url}/api/auth/login/", {
                'username': 'admin',
                'password': 'wrongpassword'
            })
            
            invalid_response = self.session.post(f"{self.base_url}/api/auth/login/", {
                'username': 'nonexistentuser12345',
                'password': 'wrongpassword'
            })
            
            # Check if responses are different (indicating enumeration)
            if (valid_response.status_code != invalid_response.status_code or
                valid_response.text != invalid_response.text):
                
                self.add_vulnerability("authentication", "Username enumeration possible", "low", {
                    "test": "credential_enumeration",
                    "recommendation": "Return identical responses for valid and invalid usernames"
                })
                return {"vulnerable": True, "status": "fail"}
            else:
                return {"vulnerable": False, "status": "pass"}
                
        except Exception as e:
            return {"error": str(e), "status": "error"}
    
    def test_authorization_security(self):
        """Test authorization and access control"""
        print("🔒 Testing authorization security...")
        
        # Test for common authorization issues
        auth_tests = {
            "privilege_escalation": self.test_privilege_escalation(),
            "horizontal_access": self.test_horizontal_access_control(),
            "vertical_access": self.test_vertical_access_control(),
            "direct_object_reference": self.test_direct_object_reference()
        }
        
        self.audit_results["authorization_tests"] = auth_tests
    
    def test_privilege_escalation(self):
        """Test for privilege escalation vulnerabilities"""
        
        # This would require creating test users with different privilege levels
        return {
            "tested": False,
            "status": "not_implemented",
            "recommendation": "Implement comprehensive privilege escalation tests"
        }
    
    def test_horizontal_access_control(self):
        """Test horizontal access control (user accessing other user's data)"""
        
        return {
            "tested": False,
            "status": "not_implemented",
            "recommendation": "Test access to other users' resources"
        }
    
    def test_vertical_access_control(self):
        """Test vertical access control (user accessing admin functions)"""
        
        return {
            "tested": False,
            "status": "not_implemented",
            "recommendation": "Test access to administrative functions"
        }
    
    def test_direct_object_reference(self):
        """Test for insecure direct object references"""
        
        return {
            "tested": False,
            "status": "not_implemented",
            "recommendation": "Test direct access to objects by ID manipulation"
        }
    
    def test_input_validation(self):
        """Test input validation security"""
        print("🔍 Testing input validation...")
        
        validation_tests = {
            "sql_injection": self.test_sql_injection(),
            "xss_vulnerabilities": self.test_xss_vulnerabilities(),
            "command_injection": self.test_command_injection(),
            "path_traversal": self.test_path_traversal()
        }
        
        self.audit_results["input_validation_tests"] = validation_tests
    
    def test_sql_injection(self):
        """Test for SQL injection vulnerabilities"""
        
        sql_payloads = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "' UNION SELECT * FROM users --",
            "1' OR 1=1 --",
            "admin'--",
            "' OR 1=1#"
        ]
        
        vulnerable_endpoints = []
        
        try:
            # Test login endpoint
            for payload in sql_payloads:
                response = self.session.post(f"{self.base_url}/api/auth/login/", {
                    'username': payload,
                    'password': 'test'
                })
                
                # Check for SQL error messages
                if any(error in response.text.lower() for error in 
                       ['sql', 'mysql', 'postgresql', 'sqlite', 'syntax error']):
                    vulnerable_endpoints.append({
                        "endpoint": "/api/auth/login/",
                        "payload": payload,
                        "response_snippet": response.text[:200]
                    })
            
            if vulnerable_endpoints:
                self.add_vulnerability("input_validation", "SQL injection vulnerability detected", "critical", {
                    "test": "sql_injection",
                    "vulnerable_endpoints": vulnerable_endpoints,
                    "recommendation": "Use parameterized queries and input validation"
                })
                return {"vulnerable": True, "endpoints": vulnerable_endpoints, "status": "fail"}
            else:
                return {"vulnerable": False, "status": "pass"}
                
        except Exception as e:
            return {"error": str(e), "status": "error"}
    
    def test_xss_vulnerabilities(self):
        """Test for XSS vulnerabilities"""
        
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "';alert('XSS');//",
            "<svg onload=alert('XSS')>",
            "'\"><script>alert('XSS')</script>"
        ]
        
        vulnerable_endpoints = []
        
        try:
            # Test various endpoints with XSS payloads
            test_endpoints = [
                "/api/employees/",
                "/api/departments/",
                "/api/projects/"
            ]
            
            for endpoint in test_endpoints:
                for payload in xss_payloads:
                    # Test GET parameters
                    response = self.session.get(f"{self.base_url}{endpoint}?search={payload}")
                    
                    if payload in response.text and 'text/html' in response.headers.get('content-type', ''):
                        vulnerable_endpoints.append({
                            "endpoint": endpoint,
                            "parameter": "search",
                            "payload": payload,
                            "type": "reflected_xss"
                        })
            
            if vulnerable_endpoints:
                self.add_vulnerability("input_validation", "XSS vulnerability detected", "high", {
                    "test": "xss_vulnerabilities",
                    "vulnerable_endpoints": vulnerable_endpoints,
                    "recommendation": "Implement proper output encoding and CSP headers"
                })
                return {"vulnerable": True, "endpoints": vulnerable_endpoints, "status": "fail"}
            else:
                return {"vulnerable": False, "status": "pass"}
                
        except Exception as e:
            return {"error": str(e), "status": "error"}
    
    def test_command_injection(self):
        """Test for command injection vulnerabilities"""
        
        return {
            "tested": False,
            "status": "not_implemented",
            "recommendation": "Implement command injection tests"
        }
    
    def test_path_traversal(self):
        """Test for path traversal vulnerabilities"""
        
        return {
            "tested": False,
            "status": "not_implemented",
            "recommendation": "Implement path traversal tests"
        }
    
    def test_session_security(self):
        """Test session security"""
        print("🍪 Testing session security...")
        
        # Test session cookie attributes
        response = self.session.get(f"{self.base_url}/api/health/")
        
        session_cookie = None
        for cookie in self.session.cookies:
            if cookie.name == 'sessionid':
                session_cookie = cookie
                break
        
        if session_cookie:
            session_security = {
                "secure": session_cookie.secure,
                "httponly": session_cookie.has_nonstandard_attr('HttpOnly'),
                "samesite": session_cookie.get_nonstandard_attr('SameSite'),
                "status": "analyzed"
            }
            
            if not session_cookie.secure:
                self.add_vulnerability("session", "Session cookie not marked as secure", "medium", {
                    "recommendation": "Set SESSION_COOKIE_SECURE = True"
                })
            
            if not session_cookie.has_nonstandard_attr('HttpOnly'):
                self.add_vulnerability("session", "Session cookie not marked as HttpOnly", "medium", {
                    "recommendation": "Set SESSION_COOKIE_HTTPONLY = True"
                })
        else:
            session_security = {"status": "no_session_cookie"}
        
        return session_security
    
    def test_csrf_protection(self):
        """Test CSRF protection"""
        print("🛡️ Testing CSRF protection...")
        
        try:
            # Test if CSRF token is required
            response = self.session.post(f"{self.base_url}/api/auth/login/", {
                'username': 'test',
                'password': 'test'
            })
            
            if 'csrf' in response.text.lower() or response.status_code == 403:
                return {"protected": True, "status": "pass"}
            else:
                self.add_vulnerability("csrf", "CSRF protection not implemented", "medium", {
                    "recommendation": "Implement CSRF protection for state-changing operations"
                })
                return {"protected": False, "status": "fail"}
                
        except Exception as e:
            return {"error": str(e), "status": "error"}
    
    def test_file_upload_security(self):
        """Test file upload security"""
        print("📁 Testing file upload security...")
        
        # This would test file upload endpoints for various security issues
        return {
            "tested": False,
            "status": "not_implemented",
            "recommendation": "Implement file upload security tests"
        }
    
    def test_rate_limiting(self):
        """Test rate limiting implementation"""
        print("⏱️ Testing rate limiting...")
        
        try:
            # Test API rate limiting
            request_count = 0
            for i in range(50):  # Try 50 requests quickly
                response = self.session.get(f"{self.base_url}/api/health/")
                request_count += 1
                
                if response.status_code == 429:  # Rate limited
                    return {
                        "implemented": True,
                        "requests_before_limit": request_count,
                        "status": "pass"
                    }
                
                time.sleep(0.1)  # Small delay
            
            # If we get here, no rate limiting was detected
            self.add_vulnerability("rate_limiting", "No rate limiting detected", "medium", {
                "test": "rate_limiting",
                "requests_tested": request_count,
                "recommendation": "Implement rate limiting to prevent abuse"
            })
            
            return {
                "implemented": False,
                "requests_tested": request_count,
                "status": "fail"
            }
            
        except Exception as e:
            return {"error": str(e), "status": "error"}
    
    def add_vulnerability(self, category: str, description: str, severity: str, details: Dict[str, Any]):
        """Add vulnerability to audit results"""
        
        vulnerability = {
            "category": category,
            "description": description,
            "severity": severity,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        
        self.audit_results["vulnerabilities"].append(vulnerability)
        
        # Print vulnerability immediately
        severity_icon = {"critical": "🔴", "high": "🟠", "medium": "🟡", "low": "🟢"}
        icon = severity_icon.get(severity, "⚪")
        print(f"  {icon} {severity.upper()}: {description}")
    
    def generate_security_recommendations(self):
        """Generate security recommendations based on findings"""
        
        recommendations = []
        
        # Group vulnerabilities by severity
        critical_vulns = [v for v in self.audit_results["vulnerabilities"] if v["severity"] == "critical"]
        high_vulns = [v for v in self.audit_results["vulnerabilities"] if v["severity"] == "high"]
        medium_vulns = [v for v in self.audit_results["vulnerabilities"] if v["severity"] == "medium"]
        
        if critical_vulns:
            recommendations.append({
                "priority": "immediate",
                "category": "Critical Vulnerabilities",
                "description": f"Address {len(critical_vulns)} critical security vulnerabilities immediately",
                "action": "Review and fix all critical vulnerabilities before production deployment"
            })
        
        if high_vulns:
            recommendations.append({
                "priority": "high",
                "category": "High-Risk Vulnerabilities", 
                "description": f"Address {len(high_vulns)} high-risk security vulnerabilities",
                "action": "Schedule immediate remediation for high-risk vulnerabilities"
            })
        
        if medium_vulns:
            recommendations.append({
                "priority": "medium",
                "category": "Medium-Risk Vulnerabilities",
                "description": f"Address {len(medium_vulns)} medium-risk security vulnerabilities",
                "action": "Include medium-risk vulnerability fixes in next release cycle"
            })
        
        # Add general security recommendations
        recommendations.extend([
            {
                "priority": "high",
                "category": "Security Headers",
                "description": "Implement comprehensive security headers",
                "action": "Add CSP, HSTS, and other security headers to all responses"
            },
            {
                "priority": "medium",
                "category": "Security Monitoring",
                "description": "Implement security monitoring and alerting",
                "action": "Set up SIEM system and security event monitoring"
            },
            {
                "priority": "medium",
                "category": "Regular Security Audits",
                "description": "Schedule regular security audits and penetration testing",
                "action": "Conduct quarterly security assessments"
            }
        ])
        
        self.audit_results["recommendations"] = recommendations
    
    def save_audit_results(self):
        """Save audit results to file"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"security_audit_report_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(self.audit_results, f, indent=2, default=str)
        
        print(f"\n📄 Security audit report saved to: {filename}")
    
    def print_audit_summary(self):
        """Print comprehensive audit summary"""
        
        print("\n" + "=" * 60)
        print("🔒 SECURITY AUDIT SUMMARY")
        print("=" * 60)
        
        # Vulnerability summary
        vulnerabilities = self.audit_results["vulnerabilities"]
        vuln_by_severity = {}
        for vuln in vulnerabilities:
            severity = vuln["severity"]
            vuln_by_severity[severity] = vuln_by_severity.get(severity, 0) + 1
        
        print(f"\n🎯 Vulnerability Summary:")
        print(f"  • Total vulnerabilities: {len(vulnerabilities)}")
        
        for severity in ["critical", "high", "medium", "low"]:
            count = vuln_by_severity.get(severity, 0)
            if count > 0:
                severity_icon = {"critical": "🔴", "high": "🟠", "medium": "🟡", "low": "🟢"}
                icon = severity_icon.get(severity, "⚪")
                print(f"  {icon} {severity.title()}: {count}")
        
        # Security score calculation
        total_score = 100
        total_score -= vuln_by_severity.get("critical", 0) * 25
        total_score -= vuln_by_severity.get("high", 0) * 15
        total_score -= vuln_by_severity.get("medium", 0) * 10
        total_score -= vuln_by_severity.get("low", 0) * 5
        total_score = max(0, total_score)
        
        print(f"\n📊 Security Score: {total_score}/100")
        
        if total_score >= 90:
            print(f"🎉 Excellent security posture!")
        elif total_score >= 75:
            print(f"✅ Good security with minor improvements needed")
        elif total_score >= 50:
            print(f"⚠️ Moderate security issues requiring attention")
        else:
            print(f"🚨 Significant security issues requiring immediate action")
        
        # Top recommendations
        recommendations = self.audit_results["recommendations"]
        if recommendations:
            print(f"\n💡 Top Security Recommendations:")
            for rec in recommendations[:3]:
                priority_icon = {"immediate": "🔴", "high": "🟠", "medium": "🟡", "low": "🟢"}
                icon = priority_icon.get(rec["priority"], "⚪")
                print(f"  {icon} {rec['category']}: {rec['description']}")

if __name__ == '__main__':
    auditor = SecurityAuditor()
    success = auditor.run_comprehensive_audit()
    sys.exit(0 if success else 1)
