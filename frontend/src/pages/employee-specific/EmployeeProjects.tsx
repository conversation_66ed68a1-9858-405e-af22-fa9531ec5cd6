/**
 * Employee Projects Page - READ-ONLY Implementation
 * Restricted version for employees - can only view assigned projects
 * No create, edit, delete, or export capabilities
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  FolderOpen,
  Users,
  Calendar,
  Target,
  Clock,
  CheckCircle,
  AlertTriangle,
  Eye,
  User,
  RefreshCw
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { projectService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface EmployeeProjectsProps {
  language: 'ar' | 'en'
}

interface Project {
  id: number
  name: string
  nameAr: string
  description: string
  descriptionAr: string
  status: 'planning' | 'in_progress' | 'on_hold' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  startDate: string
  endDate: string
  budget: number
  spentBudget: number
  progress: number
  manager: string
  teamSize: number
  department: string
  tags: string[]
}

// Translations
const translations = {
  ar: {
    employeeProjects: 'المشاريع المكلف بها',
    searchPlaceholder: 'البحث في المشاريع...',
    view: 'عرض',
    name: 'اسم المشروع',
    status: 'الحالة',
    priority: 'الأولوية',
    progress: 'التقدم',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    manager: 'مدير المشروع',
    teamSize: 'حجم الفريق',
    department: 'القسم',
    planning: 'التخطيط',
    in_progress: 'قيد التنفيذ',
    on_hold: 'معلق',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    low: 'منخفضة',
    medium: 'متوسطة',
    high: 'عالية',
    urgent: 'عاجلة',
    all: 'الكل',
    filterByStatus: 'تصفية حسب الحالة',
    filterByPriority: 'تصفية حسب الأولوية',
    filterByDepartment: 'تصفية حسب القسم'
  },
  en: {
    employeeProjects: 'Assigned Projects',
    searchPlaceholder: 'Search projects...',
    view: 'View',
    name: 'Project Name',
    status: 'Status',
    priority: 'Priority',
    progress: 'Progress',
    startDate: 'Start Date',
    endDate: 'End Date',
    manager: 'Project Manager',
    teamSize: 'Team Size',
    department: 'Department',
    planning: 'Planning',
    in_progress: 'In Progress',
    on_hold: 'On Hold',
    completed: 'Completed',
    cancelled: 'Cancelled',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    urgent: 'Urgent',
    all: 'All',
    filterByStatus: 'Filter by Status',
    filterByPriority: 'Filter by Priority',
    filterByDepartment: 'Filter by Department'
  }
}

export default function EmployeeProjects({ language }: EmployeeProjectsProps) {
  const [showModal, setShowModal] = useState(false)

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook - READ-ONLY for employees
  const {
    items: projects,
    selectedItem,
    loading,
    error,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    clearError
  } = useCrud<Project>({
    service: projectService,
    autoLoad: true,
    pageSize: 20
  })

  // Table columns configuration
  const columns: TableColumn<Project>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: Project) => (
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <FolderOpen className="h-5 w-5 text-blue-400" />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.nameAr : item.name}
            </div>
            <div className="text-sm text-gray-400">
              {language === 'ar' ? item.descriptionAr : item.description}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Project) => {
        const statusColors = {
          planning: 'bg-yellow-500/20 text-yellow-400',
          in_progress: 'bg-blue-500/20 text-blue-400',
          on_hold: 'bg-orange-500/20 text-orange-400',
          completed: 'bg-green-500/20 text-green-400',
          cancelled: 'bg-red-500/20 text-red-400'
        }
        return (
          <Badge className={statusColors[item.status]}>
            {t[item.status]}
          </Badge>
        )
      }
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: Project) => {
        const priorityColors = {
          low: 'bg-gray-500/20 text-gray-400',
          medium: 'bg-yellow-500/20 text-yellow-400',
          high: 'bg-orange-500/20 text-orange-400',
          urgent: 'bg-red-500/20 text-red-400'
        }
        return (
          <Badge className={priorityColors[item.priority]}>
            {t[item.priority]}
          </Badge>
        )
      }
    },
    {
      key: 'progress',
      label: t.progress,
      sortable: true,
      render: (item: Project) => (
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <div className="w-16 bg-gray-700 rounded-full h-2">
            <div 
              className="bg-blue-500 h-2 rounded-full" 
              style={{ width: `${item.progress}%` }}
            />
          </div>
          <span className="text-sm text-gray-400">{item.progress}%</span>
        </div>
      )
    },
    {
      key: 'manager',
      label: t.manager,
      render: (item: Project) => (
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <User className="h-4 w-4 text-gray-400" />
          <span className="text-gray-300">{item.manager}</span>
        </div>
      )
    },
    {
      key: 'teamSize',
      label: t.teamSize,
      render: (item: Project) => (
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <Users className="h-4 w-4 text-gray-400" />
          <span className="text-gray-300">{item.teamSize}</span>
        </div>
      )
    },
    {
      key: 'startDate',
      label: t.startDate,
      sortable: true,
      render: (item: Project) => (
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <Calendar className="h-4 w-4 text-gray-400" />
          <span className="text-gray-300">{item.startDate}</span>
        </div>
      )
    },
    {
      key: 'endDate',
      label: t.endDate,
      sortable: true,
      render: (item: Project) => (
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <Calendar className="h-4 w-4 text-gray-400" />
          <span className="text-gray-300">{item.endDate}</span>
        </div>
      )
    }
  ]

  // Table actions configuration - RESTRICTED for employees (VIEW ONLY)
  const actions: TableAction<Project>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Project) => {
        selectItem(item)
        setShowModal(true)
      },
      variant: 'ghost'
    }
    // REMOVED: Edit, Delete, and other management actions
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.filterByStatus,
      options: [
        { value: '', label: t.all },
        { value: 'planning', label: t.planning },
        { value: 'in_progress', label: t.in_progress },
        { value: 'on_hold', label: t.on_hold },
        { value: 'completed', label: t.completed },
        { value: 'cancelled', label: t.cancelled }
      ]
    },
    {
      key: 'priority',
      label: t.filterByPriority,
      options: [
        { value: '', label: t.all },
        { value: 'low', label: t.low },
        { value: 'medium', label: t.medium },
        { value: 'high', label: t.high },
        { value: 'urgent', label: t.urgent }
      ]
    }
  ]

  // Event handlers - RESTRICTED for employees
  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* READ-ONLY Table for Employee Projects */}
      <CrudTable
        title={t.employeeProjects}
        data={projects}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        // REMOVED: onCreate - employees cannot create projects
        onRefresh={refresh}
        // REMOVED: onExport - employees cannot export data
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        showCreateButton={false}
        showExportButton={false}
      />

      {/* VIEW-ONLY Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        // REMOVED: onSave - employees cannot save/edit projects
        title={t.view}
        fields={[]} // No form fields needed for view-only
        initialData={selectedItem as any}
        language={language}
        loading={false}
        readOnly={true}
      />
    </div>
  )
}
