"""
Django management command to fix leave request data integrity issues.
Fixes orphaned leave requests and ensures proper employee associations.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from ems.models import LeaveRequest, Employee, User
from django.contrib.auth.models import User as AuthUser


class Command(BaseCommand):
    help = 'Fix leave request data integrity issues'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be fixed without making changes',
        )
        parser.add_argument(
            '--fix-orphaned',
            action='store_true',
            help='Fix orphaned leave requests',
        )
        parser.add_argument(
            '--fix-missing-users',
            action='store_true',
            help='Fix employees without associated users',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        fix_orphaned = options['fix_orphaned']
        fix_missing_users = options['fix_missing_users']

        if not any([fix_orphaned, fix_missing_users]):
            # If no specific fix is requested, do all
            fix_orphaned = True
            fix_missing_users = True

        self.stdout.write(
            self.style.SUCCESS('🔍 Starting leave request data integrity check...')
        )

        if dry_run:
            self.stdout.write(
                self.style.WARNING('🔍 DRY RUN MODE - No changes will be made')
            )

        # Check 1: Find leave requests with missing employees
        if fix_orphaned:
            self.fix_orphaned_leave_requests(dry_run)

        # Check 2: Find employees without users
        if fix_missing_users:
            self.fix_employees_without_users(dry_run)

        # Check 3: Verify data integrity
        self.verify_data_integrity()

        self.stdout.write(
            self.style.SUCCESS('✅ Leave request data integrity check completed!')
        )

    def fix_orphaned_leave_requests(self, dry_run=False):
        """Fix leave requests that reference non-existent employees"""
        self.stdout.write('\n🔍 Checking for orphaned leave requests...')
        
        # Find leave requests where employee is None or doesn't exist
        orphaned_requests = []
        all_requests = LeaveRequest.objects.all()
        
        for request in all_requests:
            try:
                # Try to access the employee
                if request.employee is None:
                    orphaned_requests.append(request)
                else:
                    # Try to access employee.user to see if it exists
                    _ = request.employee.user
            except Employee.DoesNotExist:
                orphaned_requests.append(request)
            except AttributeError:
                # Employee exists but has no user
                orphaned_requests.append(request)

        if orphaned_requests:
            self.stdout.write(
                self.style.WARNING(f'⚠️  Found {len(orphaned_requests)} orphaned leave requests')
            )
            
            for request in orphaned_requests:
                self.stdout.write(f'   - Request ID {request.id}: {request.leave_type} ({request.start_date})')
            
            if not dry_run:
                # Option 1: Delete orphaned requests
                # Option 2: Assign to a default employee
                # For now, let's delete them as they're invalid
                with transaction.atomic():
                    deleted_count = LeaveRequest.objects.filter(
                        id__in=[r.id for r in orphaned_requests]
                    ).delete()[0]
                    self.stdout.write(
                        self.style.SUCCESS(f'🗑️  Deleted {deleted_count} orphaned leave requests')
                    )
        else:
            self.stdout.write(
                self.style.SUCCESS('✅ No orphaned leave requests found')
            )

    def fix_employees_without_users(self, dry_run=False):
        """Fix employees that don't have associated users"""
        self.stdout.write('\n🔍 Checking for employees without users...')
        
        employees_without_users = []
        all_employees = Employee.objects.all()
        
        for employee in all_employees:
            try:
                if employee.user is None:
                    employees_without_users.append(employee)
                else:
                    # Try to access user properties
                    _ = employee.user.get_full_name()
            except AttributeError:
                employees_without_users.append(employee)
            except User.DoesNotExist:
                employees_without_users.append(employee)

        if employees_without_users:
            self.stdout.write(
                self.style.WARNING(f'⚠️  Found {len(employees_without_users)} employees without users')
            )
            
            for employee in employees_without_users:
                self.stdout.write(f'   - Employee ID {employee.id}: {employee.employee_id}')
            
            if not dry_run:
                # Create placeholder users for employees without users
                with transaction.atomic():
                    for employee in employees_without_users:
                        # Create a placeholder user
                        placeholder_user = AuthUser.objects.create_user(
                            username=f'placeholder_{employee.employee_id}',
                            email=f'placeholder_{employee.employee_id}@company.com',
                            first_name='Unknown',
                            last_name='Employee',
                            is_active=False  # Mark as inactive
                        )
                        employee.user = placeholder_user
                        employee.save()
                        
                        self.stdout.write(
                            self.style.SUCCESS(f'✅ Created placeholder user for employee {employee.employee_id}')
                        )
        else:
            self.stdout.write(
                self.style.SUCCESS('✅ All employees have associated users')
            )

    def verify_data_integrity(self):
        """Verify that all leave requests now have proper employee associations"""
        self.stdout.write('\n🔍 Verifying data integrity...')
        
        total_requests = LeaveRequest.objects.count()
        valid_requests = 0
        
        for request in LeaveRequest.objects.all():
            try:
                employee_name = request.employee.user.get_full_name()
                if employee_name:
                    valid_requests += 1
                else:
                    self.stdout.write(
                        self.style.WARNING(f'⚠️  Request {request.id} has employee with empty name')
                    )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'❌ Request {request.id} has invalid employee: {e}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ {valid_requests}/{total_requests} leave requests have valid employee data')
        )
        
        if valid_requests == total_requests:
            self.stdout.write(
                self.style.SUCCESS('🎉 All leave requests now have proper employee associations!')
            )
