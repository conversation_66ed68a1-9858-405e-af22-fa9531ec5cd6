/**
 * FIXED: Integration Tests for Bug Fixes
 * End-to-end testing of critical bug fixes in real application scenarios
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { Provider } from 'react-redux'
import { <PERSON>rowserRouter } from 'react-router-dom'
import { configureStore } from '@reduxjs/toolkit'
import userEvent from '@testing-library/user-event'

// Import the main components
import App from '../App'
import HREmployees from '../pages/hr-specific/HREmployees'
import authSlice from '../store/slices/authSlice'

// Mock API responses
const mockEmployees = [
  {
    id: 1,
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+**********',
    position: 'Developer',
    department: 'IT',
    status: 'active',
    performance: 'excellent'
  },
  {
    id: 2,
    firstName: 'Jane',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+**********',
    position: 'Manager',
    department: 'HR',
    status: 'active',
    performance: 'good'
  }
]

// Mock services
jest.mock('../services/crudService', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => ({
    getAll: jest.fn().mockResolvedValue({
      data: mockEmployees,
      total: mockEmployees.length
    }),
    create: jest.fn().mockResolvedValue(mockEmployees[0]),
    update: jest.fn().mockResolvedValue(mockEmployees[0]),
    delete: jest.fn().mockResolvedValue(true)
  }))
}))

// Mock WebSocket
jest.mock('../services/websocket', () => ({
  __esModule: true,
  default: {
    connect: jest.fn(),
    disconnect: jest.fn(),
    send: jest.fn(),
    on: jest.fn(),
    off: jest.fn()
  }
}))

const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authSlice
    },
    preloadedState: {
      auth: {
        user: {
          id: 1,
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
          role: { id: 1, name: 'Admin' }
        },
        isAuthenticated: true,
        loading: false,
        error: null,
        ...initialState
      }
    }
  })
}

const renderWithProviders = (component: React.ReactElement, initialState = {}) => {
  const store = createTestStore(initialState)
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  )
}

describe('Integration Tests - Bug Fixes', () => {
  
  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks()
    
    // Reset console methods
    jest.spyOn(console, 'error').mockImplementation(() => {})
    jest.spyOn(console, 'warn').mockImplementation(() => {})
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('Application Initialization', () => {
    it('should initialize without infinite re-renders', async () => {
      const consoleSpy = jest.spyOn(console, 'error')
      
      renderWithProviders(<App />)
      
      // Wait for initial render
      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument()
      }, { timeout: 5000 })
      
      // Should not have any console errors about infinite renders
      const infiniteRenderErrors = consoleSpy.mock.calls.filter(call => 
        call[0]?.includes?.('infinite') || call[0]?.includes?.('re-render')
      )
      expect(infiniteRenderErrors).toHaveLength(0)
    })

    it('should handle token verification without race conditions', async () => {
      // Mock localStorage
      const mockToken = 'mock-jwt-token'
      Object.defineProperty(window, 'localStorage', {
        value: {
          getItem: jest.fn().mockReturnValue(mockToken),
          setItem: jest.fn(),
          removeItem: jest.fn()
        }
      })

      renderWithProviders(<App />)

      // Should not make multiple simultaneous token verification requests
      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument()
      })

      // Verify localStorage was accessed appropriately
      expect(localStorage.getItem).toHaveBeenCalledWith('access_token')
    })
  })

  describe('HREmployees Component Integration', () => {
    it('should load and display employees without performance issues', async () => {
      const startTime = performance.now()
      
      renderWithProviders(<HREmployees />)
      
      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      })
      
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      // Should render within reasonable time (less than 1 second)
      expect(renderTime).toBeLessThan(1000)
    })

    it('should handle search without causing excessive re-renders', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(<HREmployees />)
      
      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })
      
      // Find search input
      const searchInput = screen.getByPlaceholderText(/search/i)
      
      // Type in search - should be debounced
      await user.type(searchInput, 'John')
      
      // Should still show results
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })
    })

    it('should handle modal operations without layout shifts', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(<HREmployees />)
      
      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })
      
      // Click create button
      const createButton = screen.getByRole('button', { name: /create/i })
      await user.click(createButton)
      
      // Modal should appear without layout shift
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument()
      })
      
      // Close modal
      const closeButton = screen.getByRole('button', { name: /close/i })
      await user.click(closeButton)
      
      // Modal should disappear
      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
      })
    })
  })

  describe('Accessibility Integration', () => {
    it('should have proper keyboard navigation', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(<HREmployees />)
      
      // Wait for load
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })
      
      // Tab through interactive elements
      await user.tab()
      expect(document.activeElement).toHaveAttribute('role', 'button')
      
      // Should be able to navigate with keyboard
      await user.keyboard('{Enter}')
      
      // Should handle keyboard events properly
      expect(document.activeElement).toBeDefined()
    })

    it('should announce important changes to screen readers', async () => {
      const user = userEvent.setup()
      
      renderWithProviders(<HREmployees />)
      
      // Wait for load
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })
      
      // Check for ARIA live regions
      const liveRegions = screen.getAllByRole('status', { hidden: true })
      expect(liveRegions.length).toBeGreaterThan(0)
    })
  })

  describe('Performance Integration', () => {
    it('should handle large datasets efficiently', async () => {
      // Create large dataset
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: i + 1,
        firstName: `User${i}`,
        lastName: `Test${i}`,
        email: `user${i}@example.com`,
        phone: `+123456789${i}`,
        position: 'Developer',
        department: 'IT',
        status: 'active',
        performance: 'good'
      }))

      // Mock service to return large dataset
      const mockService = {
        getAll: jest.fn().mockResolvedValue({
          data: largeDataset,
          total: largeDataset.length
        }),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn()
      }

      const startTime = performance.now()
      
      renderWithProviders(<HREmployees />)
      
      // Should handle large dataset without freezing
      await waitFor(() => {
        expect(screen.getByText(/User0/)).toBeInTheDocument()
      }, { timeout: 3000 })
      
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      // Should render large dataset within reasonable time
      expect(renderTime).toBeLessThan(3000)
    })

    it('should not cause memory leaks during component lifecycle', async () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0
      
      const { unmount } = renderWithProviders(<HREmployees />)
      
      // Wait for component to fully mount
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })
      
      // Unmount component
      unmount()
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }
      
      // Memory should not increase significantly
      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0
      const memoryIncrease = finalMemory - initialMemory
      
      // Should not leak more than 10MB
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024)
    })
  })

  describe('Error Handling Integration', () => {
    it('should handle API errors gracefully', async () => {
      // Mock API to throw error
      const mockService = {
        getAll: jest.fn().mockRejectedValue(new Error('API Error')),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn()
      }

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      
      renderWithProviders(<HREmployees />)
      
      // Should show error state instead of crashing
      await waitFor(() => {
        expect(screen.getByText(/error/i) || screen.getByText(/failed/i)).toBeInTheDocument()
      })
      
      // Should not cause unhandled promise rejections
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('Unhandled promise rejection')
      )
    })

    it('should recover from errors', async () => {
      const user = userEvent.setup()
      
      // Start with error state
      const mockService = {
        getAll: jest.fn()
          .mockRejectedValueOnce(new Error('API Error'))
          .mockResolvedValue({
            data: mockEmployees,
            total: mockEmployees.length
          }),
        create: jest.fn(),
        update: jest.fn(),
        delete: jest.fn()
      }

      renderWithProviders(<HREmployees />)
      
      // Should show error initially
      await waitFor(() => {
        expect(screen.getByText(/error/i) || screen.getByText(/failed/i)).toBeInTheDocument()
      })
      
      // Click retry button if available
      const retryButton = screen.queryByRole('button', { name: /retry/i })
      if (retryButton) {
        await user.click(retryButton)
        
        // Should recover and show data
        await waitFor(() => {
          expect(screen.getByText('John Doe')).toBeInTheDocument()
        })
      }
    })
  })
})
