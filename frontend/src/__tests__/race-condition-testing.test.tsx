/**
 * CRITICAL RACE CONDITION TESTING
 * Stress tests to verify WebSocket stability, API deduplication, and concurrent operations
 */

import { render, screen, waitFor, act } from '@testing-library/react'
import { Provider } from 'react-redux'
import { store } from '../store'
import { webSocketService } from '../services/websocket'
import { deduplicateRequest, cleanupDeduplicationCache } from '../utils/apiCache'

// Mock WebSocket for testing
class MockWebSocket {
  static instances: MockWebSocket[] = []
  
  readyState = WebSocket.CONNECTING
  onopen: ((event: Event) => void) | null = null
  onclose: ((event: CloseEvent) => void) | null = null
  onerror: ((event: Event) => void) | null = null
  onmessage: ((event: MessageEvent) => void) | null = null

  constructor(public url: string) {
    MockWebSocket.instances.push(this)
    
    // Simulate connection after short delay
    setTimeout(() => {
      this.readyState = WebSocket.OPEN
      if (this.onopen) {
        this.onopen(new Event('open'))
      }
    }, 10)
  }

  close() {
    this.readyState = WebSocket.CLOSED
    if (this.onclose) {
      this.onclose(new CloseEvent('close'))
    }
    
    // Remove from instances
    const index = MockWebSocket.instances.indexOf(this)
    if (index > -1) {
      MockWebSocket.instances.splice(index, 1)
    }
  }

  send(data: string) {
    // Mock send
  }

  static getActiveConnections(): number {
    return MockWebSocket.instances.filter(ws => ws.readyState === WebSocket.OPEN).length
  }

  static closeAll() {
    MockWebSocket.instances.forEach(ws => ws.close())
    MockWebSocket.instances = []
  }
}

global.WebSocket = MockWebSocket as any

// Mock fetch with controllable delays
const mockFetch = jest.fn()
global.fetch = mockFetch

describe('🧪 CRITICAL RACE CONDITION TESTING', () => {
  
  beforeEach(() => {
    MockWebSocket.closeAll()
    mockFetch.mockClear()
    cleanupDeduplicationCache()
    jest.useFakeTimers()
    
    // Default successful response
    mockFetch.mockImplementation(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({
          ok: true,
          json: () => Promise.resolve({ data: 'test' })
        }), 100)
      )
    )
  })

  afterEach(() => {
    MockWebSocket.closeAll()
    jest.useRealTimers()
    cleanupDeduplicationCache()
  })

  describe('🔌 WebSocket Connection Race Conditions', () => {
    
    test('CRITICAL: Multiple connection attempts should not create multiple WebSockets', async () => {
      // Attempt multiple simultaneous connections
      const connectionPromises = Array(10).fill(null).map(() => {
        return new Promise(resolve => {
          webSocketService.connect()
          setTimeout(resolve, 50)
        })
      })

      // Advance timers to trigger connections
      jest.advanceTimersByTime(100)
      await Promise.all(connectionPromises)

      // Should only have one active connection
      const activeConnections = MockWebSocket.getActiveConnections()
      expect(activeConnections).toBeLessThanOrEqual(1)
      
      console.log(`✅ RACE CONDITION TEST PASSED: ${activeConnections} WebSocket connection(s)`)
    })

    test('CRITICAL: Rapid connect/disconnect should not cause connection leaks', async () => {
      // Rapidly connect and disconnect
      for (let i = 0; i < 20; i++) {
        webSocketService.connect()
        jest.advanceTimersByTime(10)
        webSocketService.disconnect()
        jest.advanceTimersByTime(10)
      }

      // Should have no active connections
      const activeConnections = MockWebSocket.getActiveConnections()
      expect(activeConnections).toBe(0)
      
      console.log('✅ RACE CONDITION TEST PASSED: No connection leaks detected')
    })

    test('CRITICAL: Connection timeout should not cause infinite loops', async () => {
      // Mock slow connection
      const originalWebSocket = global.WebSocket
      let connectionAttempts = 0
      
      global.WebSocket = jest.fn().mockImplementation(() => {
        connectionAttempts++
        return {
          readyState: WebSocket.CONNECTING,
          close: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn()
        }
      })

      // Attempt connection
      webSocketService.connect()
      
      // Advance time to trigger timeout
      jest.advanceTimersByTime(10000)
      
      // Should not have excessive connection attempts
      expect(connectionAttempts).toBeLessThan(5)
      
      global.WebSocket = originalWebSocket
      console.log(`✅ RACE CONDITION TEST PASSED: ${connectionAttempts} connection attempts`)
    })

    test('CRITICAL: Concurrent reconnection attempts should be prevented', async () => {
      let reconnectionAttempts = 0
      
      // Mock failed connection
      global.WebSocket = jest.fn().mockImplementation(() => {
        reconnectionAttempts++
        const ws = {
          readyState: WebSocket.CONNECTING,
          close: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn()
        }
        
        // Simulate connection failure
        setTimeout(() => {
          if (ws.onerror) ws.onerror(new Event('error'))
        }, 50)
        
        return ws
      })

      // Trigger multiple reconnection attempts
      for (let i = 0; i < 5; i++) {
        webSocketService.connect()
        jest.advanceTimersByTime(100)
      }

      // Should not have excessive reconnection attempts
      expect(reconnectionAttempts).toBeLessThan(10)
      
      console.log(`✅ RACE CONDITION TEST PASSED: ${reconnectionAttempts} reconnection attempts`)
    })
  })

  describe('🔄 API Deduplication Race Conditions', () => {
    
    test('CRITICAL: Simultaneous identical requests should be deduplicated', async () => {
      let apiCallCount = 0
      
      const mockApiCall = () => {
        apiCallCount++
        return Promise.resolve({ data: 'test' })
      }

      // Make 20 simultaneous identical requests
      const promises = Array(20).fill(null).map(() => 
        deduplicateRequest('test-endpoint', mockApiCall)
      )

      await Promise.all(promises)

      // Should only make one actual API call
      expect(apiCallCount).toBe(1)
      
      console.log(`✅ RACE CONDITION TEST PASSED: ${apiCallCount} API call for 20 requests`)
    })

    test('CRITICAL: Race condition in lock mechanism should not cause infinite recursion', async () => {
      let callCount = 0
      let maxDepth = 0
      let currentDepth = 0
      
      const recursiveApiCall = () => {
        currentDepth++
        maxDepth = Math.max(maxDepth, currentDepth)
        callCount++
        
        return new Promise(resolve => {
          setTimeout(() => {
            currentDepth--
            resolve({ data: 'test' })
          }, Math.random() * 100)
        })
      }

      // Create race condition with staggered requests
      const promises = []
      for (let i = 0; i < 15; i++) {
        setTimeout(() => {
          promises.push(deduplicateRequest('race-test', recursiveApiCall))
        }, i * 5)
      }

      jest.advanceTimersByTime(200)
      await Promise.all(promises.filter(p => p))

      // Should not cause infinite recursion
      expect(maxDepth).toBeLessThan(15)
      expect(callCount).toBeLessThan(10)
      
      console.log(`✅ RACE CONDITION TEST PASSED: Max depth ${maxDepth}, ${callCount} calls`)
    })

    test('CRITICAL: Lock timeout should prevent deadlocks', async () => {
      let timeoutOccurred = false
      
      const slowApiCall = () => {
        return new Promise(resolve => {
          // Simulate very slow API call
          setTimeout(() => resolve({ data: 'test' }), 5000)
        })
      }

      // Start slow request
      const slowPromise = deduplicateRequest('slow-test', slowApiCall)
      
      // Try to make same request after delay
      setTimeout(async () => {
        try {
          await deduplicateRequest('slow-test', slowApiCall)
        } catch (error) {
          if (error.message.includes('timeout')) {
            timeoutOccurred = true
          }
        }
      }, 100)

      jest.advanceTimersByTime(6000)
      
      // Should handle timeout gracefully
      expect(timeoutOccurred).toBe(true)
      
      console.log('✅ RACE CONDITION TEST PASSED: Lock timeout handled')
    })

    test('CRITICAL: Cleanup should prevent memory leaks in deduplication', async () => {
      // Make many requests to different endpoints
      const promises = []
      for (let i = 0; i < 100; i++) {
        promises.push(
          deduplicateRequest(`endpoint-${i}`, () => Promise.resolve({ data: i }))
        )
      }

      await Promise.all(promises)

      // Cleanup should clear all caches
      cleanupDeduplicationCache()

      // Verify cleanup worked by making new request
      let newCallMade = false
      await deduplicateRequest('test-cleanup', () => {
        newCallMade = true
        return Promise.resolve({ data: 'cleaned' })
      })

      expect(newCallMade).toBe(true)
      
      console.log('✅ RACE CONDITION TEST PASSED: Deduplication cache cleaned')
    })
  })

  describe('⚡ Concurrent State Updates', () => {
    
    test('CRITICAL: Rapid state updates should not cause inconsistencies', async () => {
      const TestComponent = () => {
        const [count, setCount] = React.useState(0)
        const [updates, setUpdates] = React.useState(0)

        React.useEffect(() => {
          // Simulate rapid state updates
          const interval = setInterval(() => {
            setCount(prev => prev + 1)
            setUpdates(prev => prev + 1)
          }, 10)

          return () => clearInterval(interval)
        }, [])

        return (
          <div>
            <span data-testid="count">{count}</span>
            <span data-testid="updates">{updates}</span>
          </div>
        )
      }

      const { unmount } = render(<TestComponent />)

      // Let it run for a bit
      jest.advanceTimersByTime(500)

      const countElement = screen.getByTestId('count')
      const updatesElement = screen.getByTestId('updates')

      // Values should be consistent
      expect(countElement.textContent).toBe(updatesElement.textContent)

      unmount()
      console.log('✅ RACE CONDITION TEST PASSED: State updates consistent')
    })

    test('CRITICAL: Concurrent API calls should not interfere', async () => {
      let completedCalls = 0
      const results = []

      // Mock different response times
      mockFetch.mockImplementation((url) => {
        const delay = Math.random() * 200
        return new Promise(resolve => {
          setTimeout(() => {
            completedCalls++
            const result = { url, delay, order: completedCalls }
            results.push(result)
            resolve({
              ok: true,
              json: () => Promise.resolve(result)
            })
          }, delay)
        })
      })

      // Make multiple concurrent API calls
      const apiPromises = Array(10).fill(null).map((_, i) => 
        fetch(`/api/endpoint-${i}`)
      )

      jest.advanceTimersByTime(300)
      await Promise.all(apiPromises)

      // All calls should complete
      expect(completedCalls).toBe(10)
      expect(results).toHaveLength(10)

      console.log('✅ RACE CONDITION TEST PASSED: Concurrent API calls handled')
    })
  })

  describe('🔄 Resource Management Race Conditions', () => {
    
    test('CRITICAL: Rapid component mount/unmount should not leak resources', async () => {
      const { MemoryLeakPrevention } = await import('../utils/performanceBugFixes')
      
      const TestComponent = ({ id }: { id: number }) => {
        React.useEffect(() => {
          const interval = MemoryLeakPrevention.setInterval(() => {
            // Simulate work
          }, 100)

          const timeout = MemoryLeakPrevention.setTimeout(() => {
            // Simulate delayed work
          }, 1000)

          return () => {
            clearInterval(interval)
            clearTimeout(timeout)
          }
        }, [])

        return <div>Component {id}</div>
      }

      const initialCounts = MemoryLeakPrevention.getResourceCounts()

      // Rapidly mount and unmount components
      for (let i = 0; i < 20; i++) {
        const { unmount } = render(<TestComponent id={i} />)
        jest.advanceTimersByTime(50)
        unmount()
      }

      const finalCounts = MemoryLeakPrevention.getResourceCounts()

      // Resource counts should not increase significantly
      expect(finalCounts.intervals).toBeLessThanOrEqual(initialCounts.intervals + 5)
      expect(finalCounts.timeouts).toBeLessThanOrEqual(initialCounts.timeouts + 5)

      console.log('✅ RACE CONDITION TEST PASSED: Resource cleanup working')
    })

    test('CRITICAL: Observer creation/destruction race conditions', async () => {
      let observerCount = 0
      const observers = []

      // Mock ResizeObserver
      global.ResizeObserver = jest.fn().mockImplementation(() => {
        observerCount++
        const observer = {
          observe: jest.fn(),
          disconnect: jest.fn(() => {
            observerCount--
          }),
          unobserve: jest.fn()
        }
        observers.push(observer)
        return observer
      })

      const TestComponent = () => {
        const ref = React.useRef<HTMLDivElement>(null)

        React.useEffect(() => {
          if (ref.current) {
            const observer = new ResizeObserver(() => {})
            observer.observe(ref.current)

            return () => {
              observer.disconnect()
            }
          }
        }, [])

        return <div ref={ref}>Test</div>
      }

      // Rapidly mount/unmount components
      for (let i = 0; i < 15; i++) {
        const { unmount } = render(<TestComponent />)
        jest.advanceTimersByTime(10)
        unmount()
      }

      // Observer count should be back to 0
      expect(observerCount).toBe(0)

      console.log('✅ RACE CONDITION TEST PASSED: Observer cleanup working')
    })
  })
})

/**
 * RACE CONDITION TEST UTILITIES
 */
export const RaceConditionTestUtils = {
  
  // Simulate high concurrency
  simulateHighConcurrency: async (operation: () => Promise<any>, iterations: number = 50) => {
    const promises = Array(iterations).fill(null).map(() => operation())
    return Promise.all(promises)
  },

  // Test WebSocket connection stability
  testWebSocketStability: async (connectionAttempts: number = 10): Promise<boolean> => {
    let successfulConnections = 0
    
    for (let i = 0; i < connectionAttempts; i++) {
      try {
        webSocketService.connect()
        await new Promise(resolve => setTimeout(resolve, 100))
        successfulConnections++
        webSocketService.disconnect()
      } catch (error) {
        console.error('WebSocket connection failed:', error)
      }
    }
    
    return successfulConnections === connectionAttempts
  },

  // Test API deduplication under load
  testApiDeduplicationLoad: async (requestCount: number = 100): Promise<boolean> => {
    let actualApiCalls = 0
    
    const mockApiCall = () => {
      actualApiCalls++
      return Promise.resolve({ data: 'test' })
    }

    const promises = Array(requestCount).fill(null).map(() => 
      deduplicateRequest('load-test', mockApiCall)
    )

    await Promise.all(promises)
    
    // Should only make one actual API call despite many requests
    return actualApiCalls === 1
  },

  // Measure race condition recovery time
  measureRecoveryTime: async (faultyOperation: () => Promise<any>): Promise<number> => {
    const start = performance.now()
    
    try {
      await faultyOperation()
    } catch (error) {
      // Expected to fail
    }
    
    return performance.now() - start
  }
}
