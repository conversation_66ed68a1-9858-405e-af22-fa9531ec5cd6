# Generated by Django 4.2.7 on 2025-06-04 05:15

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('customer_service', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AIAssistant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('assistant_type', models.CharField(choices=[('chatbot', 'Customer Chatbot'), ('agent_helper', 'Agent Assistant'), ('sentiment_analyzer', 'Sentiment Analyzer'), ('auto_responder', 'Auto Responder'), ('knowledge_suggester', 'Knowledge Suggester')], max_length=50)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('ai_provider', models.CharField(default='openai', help_text='AI service provider', max_length=100)),
                ('model_name', models.CharField(default='gpt-3.5-turbo', max_length=100)),
                ('api_key_encrypted', models.TextField(blank=True, help_text='Encrypted API key')),
                ('confidence_threshold', models.FloatField(default=0.8, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('max_response_length', models.IntegerField(default=500)),
                ('response_tone', models.CharField(choices=[('professional', 'Professional'), ('friendly', 'Friendly'), ('casual', 'Casual')], default='professional', max_length=50)),
                ('training_data', models.TextField(blank=True, help_text='Custom training prompts and examples')),
                ('knowledge_base_integration', models.BooleanField(default=True)),
                ('total_interactions', models.IntegerField(default=0)),
                ('successful_resolutions', models.IntegerField(default=0)),
                ('average_satisfaction', models.FloatField(default=0.0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='CustomerTier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('name_ar', models.CharField(blank=True, max_length=100)),
                ('tier_level', models.CharField(choices=[('platinum', 'Platinum'), ('gold', 'Gold'), ('silver', 'Silver'), ('bronze', 'Bronze'), ('standard', 'Standard')], max_length=20)),
                ('description', models.TextField(blank=True)),
                ('response_time_hours', models.IntegerField(default=24)),
                ('resolution_time_hours', models.IntegerField(default=72)),
                ('escalation_time_hours', models.IntegerField(default=48)),
                ('default_priority', models.CharField(default='medium', max_length=20)),
                ('auto_escalate', models.BooleanField(default=False)),
                ('dedicated_agent', models.BooleanField(default=False)),
                ('benefits', models.TextField(blank=True, help_text='JSON formatted benefits list')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='IntegrationConnector',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('integration_type', models.CharField(choices=[('email', 'Email System'), ('crm', 'CRM System'), ('slack', 'Slack'), ('teams', 'Microsoft Teams'), ('jira', 'Jira'), ('salesforce', 'Salesforce'), ('hubspot', 'HubSpot'), ('webhook', 'Custom Webhook'), ('api', 'REST API'), ('sms', 'SMS Gateway')], max_length=50)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('endpoint_url', models.URLField(blank=True)),
                ('api_key_encrypted', models.TextField(blank=True)),
                ('username', models.CharField(blank=True, max_length=200)),
                ('password_encrypted', models.TextField(blank=True)),
                ('configuration', models.TextField(blank=True, help_text='JSON formatted configuration')),
                ('sync_frequency_minutes', models.IntegerField(default=60)),
                ('last_sync_at', models.DateTimeField(blank=True, null=True)),
                ('last_sync_status', models.CharField(blank=True, max_length=50)),
                ('total_syncs', models.IntegerField(default=0)),
                ('failed_syncs', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='TicketRoutingRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('priority', models.IntegerField(default=1, help_text='Higher number = higher priority')),
                ('condition_type', models.CharField(choices=[('keyword', 'Keyword Match'), ('category', 'Category'), ('priority', 'Priority Level'), ('customer_tier', 'Customer Tier'), ('time_of_day', 'Time of Day'), ('agent_workload', 'Agent Workload'), ('language', 'Language'), ('channel', 'Source Channel')], max_length=50)),
                ('condition_value', models.TextField(help_text='JSON formatted condition parameters')),
                ('action_type', models.CharField(choices=[('assign_agent', 'Assign to Agent'), ('assign_team', 'Assign to Team'), ('set_priority', 'Set Priority'), ('add_tag', 'Add Tag'), ('send_notification', 'Send Notification'), ('escalate', 'Escalate')], max_length=50)),
                ('action_value', models.TextField(help_text='JSON formatted action parameters')),
                ('apply_during_hours', models.CharField(blank=True, help_text='e.g., 09:00-17:00', max_length=100)),
                ('apply_on_weekdays', models.BooleanField(default=True)),
                ('apply_on_weekends', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-priority', 'name'],
            },
        ),
        migrations.CreateModel(
            name='WorkflowAutomation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('trigger_event', models.CharField(choices=[('ticket_created', 'Ticket Created'), ('ticket_updated', 'Ticket Updated'), ('ticket_resolved', 'Ticket Resolved'), ('ticket_closed', 'Ticket Closed'), ('comment_added', 'Comment Added'), ('agent_assigned', 'Agent Assigned'), ('sla_breach', 'SLA Breach'), ('customer_feedback', 'Customer Feedback'), ('escalation_triggered', 'Escalation Triggered')], max_length=50)),
                ('trigger_conditions', models.TextField(blank=True, help_text='JSON formatted conditions')),
                ('workflow_steps', models.TextField(help_text='JSON formatted workflow steps')),
                ('delay_minutes', models.IntegerField(default=0, help_text='Delay before execution')),
                ('max_retries', models.IntegerField(default=3)),
                ('retry_delay_minutes', models.IntegerField(default=5)),
                ('total_executions', models.IntegerField(default=0)),
                ('successful_executions', models.IntegerField(default=0)),
                ('failed_executions', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='WorkflowExecution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('execution_data', models.TextField(blank=True, help_text='JSON formatted execution context')),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True)),
                ('steps_completed', models.IntegerField(default=0)),
                ('total_steps', models.IntegerField(default=0)),
                ('execution_log', models.TextField(blank=True)),
                ('related_ticket', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customer_service.supportticket')),
                ('workflow', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='executions', to='customer_service.workflowautomation')),
            ],
        ),
        migrations.CreateModel(
            name='EscalationRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('trigger_type', models.CharField(choices=[('time_based', 'Time Based'), ('sla_breach', 'SLA Breach'), ('customer_response', 'Customer Response'), ('priority_change', 'Priority Change'), ('agent_unavailable', 'Agent Unavailable'), ('customer_satisfaction', 'Low Satisfaction')], max_length=50)),
                ('trigger_after_hours', models.IntegerField(blank=True, help_text='Hours before escalation', null=True)),
                ('trigger_conditions', models.TextField(blank=True, help_text='JSON formatted conditions')),
                ('escalate_to_manager', models.BooleanField(default=False)),
                ('send_email_notification', models.BooleanField(default=True)),
                ('send_sms_notification', models.BooleanField(default=False)),
                ('increase_priority', models.BooleanField(default=True)),
                ('notification_emails', models.TextField(blank=True, help_text='Comma-separated email addresses')),
                ('notification_template', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('escalate_to_agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='customer_service.supportagent')),
            ],
        ),
        migrations.CreateModel(
            name='CustomerProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('preferred_contact_method', models.CharField(choices=[('email', 'Email'), ('phone', 'Phone'), ('chat', 'Live Chat')], default='email', max_length=50)),
                ('preferred_language', models.CharField(choices=[('en', 'English'), ('ar', 'Arabic')], default='en', max_length=10)),
                ('timezone', models.CharField(default='UTC', max_length=50)),
                ('total_tickets', models.IntegerField(default=0)),
                ('resolved_tickets', models.IntegerField(default=0)),
                ('average_satisfaction', models.FloatField(default=0.0)),
                ('last_contact_date', models.DateTimeField(blank=True, null=True)),
                ('receive_notifications', models.BooleanField(default=True)),
                ('receive_surveys', models.BooleanField(default=True)),
                ('allow_ai_assistance', models.BooleanField(default=True)),
                ('internal_notes', models.TextField(blank=True, help_text='Internal notes for agents')),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags', max_length=500)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer_tier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='customer_service.customertier')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='customer_profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='AIInteraction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(blank=True, max_length=100)),
                ('user_input', models.TextField()),
                ('ai_response', models.TextField()),
                ('confidence_score', models.FloatField(blank=True, null=True)),
                ('response_time_ms', models.IntegerField(blank=True, null=True)),
                ('context_data', models.TextField(blank=True, help_text='JSON formatted context')),
                ('was_helpful', models.BooleanField(blank=True, null=True)),
                ('user_feedback', models.TextField(blank=True)),
                ('escalated_to_human', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('assistant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interactions', to='customer_service.aiassistant')),
                ('related_ticket', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='customer_service.supportticket')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
