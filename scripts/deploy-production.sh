#!/bin/bash
# CRITICAL FIX: Production Deployment Script
# Zero-downtime deployment with health checks and rollback capability

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="$PROJECT_ROOT/backups"
LOG_FILE="$PROJECT_ROOT/logs/deployment.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Create necessary directories
mkdir -p "$BACKUP_DIR" "$(dirname "$LOG_FILE")"

# Function to check if service is healthy
check_service_health() {
    local service_name=$1
    local health_url=$2
    local max_attempts=30
    local attempt=1

    log "Checking health of $service_name..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$health_url" > /dev/null 2>&1; then
            success "$service_name is healthy"
            return 0
        fi
        
        log "Attempt $attempt/$max_attempts: $service_name not ready yet..."
        sleep 10
        ((attempt++))
    done
    
    error "$service_name failed health check after $max_attempts attempts"
    return 1
}

# Function to create backup
create_backup() {
    log "Creating backup..."
    
    local backup_timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_path="$BACKUP_DIR/backup_$backup_timestamp"
    
    mkdir -p "$backup_path"
    
    # Backup database
    log "Backing up database..."
    docker-compose -f docker-compose.production.yml exec -T postgres pg_dump \
        -U "$DB_USER" -d "$DB_NAME" > "$backup_path/database.sql"
    
    # Backup current docker-compose state
    docker-compose -f docker-compose.production.yml config > "$backup_path/docker-compose.yml"
    
    # Backup environment file
    if [ -f ".env.production" ]; then
        cp ".env.production" "$backup_path/"
    fi
    
    echo "$backup_path" > "$BACKUP_DIR/latest_backup.txt"
    success "Backup created at $backup_path"
}

# Function to rollback deployment
rollback_deployment() {
    error "Deployment failed, initiating rollback..."
    
    if [ ! -f "$BACKUP_DIR/latest_backup.txt" ]; then
        error "No backup found for rollback"
        exit 1
    fi
    
    local backup_path=$(cat "$BACKUP_DIR/latest_backup.txt")
    
    if [ ! -d "$backup_path" ]; then
        error "Backup directory not found: $backup_path"
        exit 1
    fi
    
    log "Rolling back to backup: $backup_path"
    
    # Stop current services
    docker-compose -f docker-compose.production.yml down --remove-orphans
    
    # Restore database
    if [ -f "$backup_path/database.sql" ]; then
        log "Restoring database..."
        docker-compose -f docker-compose.production.yml up -d postgres
        sleep 30
        docker-compose -f docker-compose.production.yml exec -T postgres psql \
            -U "$DB_USER" -d "$DB_NAME" < "$backup_path/database.sql"
    fi
    
    # Restore configuration
    if [ -f "$backup_path/docker-compose.yml" ]; then
        cp "$backup_path/docker-compose.yml" "docker-compose.production.yml.backup"
    fi
    
    # Start services with previous configuration
    docker-compose -f docker-compose.production.yml up -d
    
    # Wait for services to be ready
    sleep 60
    
    # Verify rollback
    if check_service_health "Backend" "http://localhost:8000/api/health/" && \
       check_service_health "Frontend" "http://localhost:3000/"; then
        success "Rollback completed successfully"
    else
        error "Rollback failed - manual intervention required"
        exit 1
    fi
}

# Function to run pre-deployment checks
pre_deployment_checks() {
    log "Running pre-deployment checks..."
    
    # Check if required environment variables are set
    local required_vars=("DB_PASSWORD" "SECRET_KEY")
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            error "Required environment variable $var is not set"
            exit 1
        fi
    done
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        error "Docker is not running"
        exit 1
    fi
    
    # Check if docker-compose file exists
    if [ ! -f "docker-compose.production.yml" ]; then
        error "docker-compose.production.yml not found"
        exit 1
    fi
    
    # Check available disk space (require at least 2GB)
    local available_space=$(df . | awk 'NR==2 {print $4}')
    if [ "$available_space" -lt 2097152 ]; then  # 2GB in KB
        error "Insufficient disk space for deployment"
        exit 1
    fi
    
    success "Pre-deployment checks passed"
}

# Function to deploy application
deploy_application() {
    log "Starting application deployment..."
    
    # Pull latest images
    log "Pulling latest Docker images..."
    docker-compose -f docker-compose.production.yml pull
    
    # Start database and cache services first
    log "Starting database and cache services..."
    docker-compose -f docker-compose.production.yml up -d postgres redis
    
    # Wait for database to be ready
    if ! check_service_health "PostgreSQL" "http://localhost:5432"; then
        error "Database failed to start"
        return 1
    fi
    
    # Run database migrations
    log "Running database migrations..."
    docker-compose -f docker-compose.production.yml run --rm backend python manage.py migrate
    
    # Start application services
    log "Starting application services..."
    docker-compose -f docker-compose.production.yml up -d backend frontend
    
    # Wait for services to be ready
    if ! check_service_health "Backend" "http://localhost:8000/api/health/"; then
        error "Backend failed to start"
        return 1
    fi
    
    if ! check_service_health "Frontend" "http://localhost:3000/"; then
        error "Frontend failed to start"
        return 1
    fi
    
    # Start reverse proxy
    log "Starting reverse proxy..."
    docker-compose -f docker-compose.production.yml up -d nginx
    
    success "Application deployment completed"
}

# Function to run post-deployment tests
post_deployment_tests() {
    log "Running post-deployment tests..."
    
    # Test API endpoints
    local endpoints=(
        "http://localhost:8000/api/health/"
        "http://localhost:8000/api/dashboard-stats/"
        "http://localhost:8000/api/auth/login/"
    )
    
    for endpoint in "${endpoints[@]}"; do
        if curl -f -s "$endpoint" > /dev/null; then
            success "Endpoint $endpoint is responding"
        else
            error "Endpoint $endpoint is not responding"
            return 1
        fi
    done
    
    # Test database connectivity
    if docker-compose -f docker-compose.production.yml exec -T backend python manage.py shell -c "from django.db import connection; connection.cursor().execute('SELECT 1')"; then
        success "Database connectivity test passed"
    else
        error "Database connectivity test failed"
        return 1
    fi
    
    # Test cache connectivity
    if docker-compose -f docker-compose.production.yml exec -T backend python manage.py shell -c "from django.core.cache import cache; cache.set('test', 'value'); assert cache.get('test') == 'value'"; then
        success "Cache connectivity test passed"
    else
        error "Cache connectivity test failed"
        return 1
    fi
    
    success "Post-deployment tests passed"
}

# Function to cleanup old resources
cleanup_old_resources() {
    log "Cleaning up old resources..."
    
    # Remove unused Docker images
    docker image prune -f
    
    # Remove old backups (keep last 5)
    if [ -d "$BACKUP_DIR" ]; then
        ls -t "$BACKUP_DIR"/backup_* | tail -n +6 | xargs -r rm -rf
    fi
    
    success "Cleanup completed"
}

# Main deployment function
main() {
    log "🚀 Starting production deployment..."
    
    # Set trap to handle errors
    trap 'error "Deployment failed at line $LINENO"; rollback_deployment' ERR
    
    # Load environment variables
    if [ -f ".env.production" ]; then
        source ".env.production"
    fi
    
    # Run deployment steps
    pre_deployment_checks
    create_backup
    deploy_application
    post_deployment_tests
    cleanup_old_resources
    
    success "🎉 Production deployment completed successfully!"
    log "Deployment log saved to: $LOG_FILE"
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
