# Generated by Django 4.2.7 on 2025-06-04 10:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('customer_service', '0002_aiassistant_customertier_integrationconnector_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer_id', models.CharField(editable=False, max_length=20, unique=True)),
                ('first_name', models.CharField(max_length=100)),
                ('last_name', models.CharField(blank=True, max_length=100)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('customer_type', models.CharField(choices=[('individual', 'Individual'), ('business', 'Business'), ('enterprise', 'Enterprise')], default='individual', max_length=20)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('suspended', 'Suspended'), ('prospect', 'Prospect')], default='prospect', max_length=20)),
                ('company_name', models.CharField(blank=True, max_length=200)),
                ('company_size', models.CharField(blank=True, max_length=50)),
                ('industry', models.CharField(blank=True, max_length=100)),
                ('address_line1', models.CharField(blank=True, max_length=255)),
                ('address_line2', models.CharField(blank=True, max_length=255)),
                ('city', models.CharField(blank=True, max_length=100)),
                ('state', models.CharField(blank=True, max_length=100)),
                ('postal_code', models.CharField(blank=True, max_length=20)),
                ('country', models.CharField(blank=True, max_length=100)),
                ('preferred_language', models.CharField(choices=[('en', 'English'), ('ar', 'Arabic')], default='en', max_length=10)),
                ('preferred_contact_method', models.CharField(choices=[('email', 'Email'), ('phone', 'Phone'), ('sms', 'SMS'), ('chat', 'Chat')], default='email', max_length=20)),
                ('source', models.CharField(blank=True, help_text='How customer found us', max_length=50)),
                ('internal_notes', models.TextField(blank=True)),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags', max_length=500)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_contact_date', models.DateTimeField(blank=True, null=True)),
                ('total_tickets', models.IntegerField(default=0)),
                ('total_orders', models.IntegerField(default=0)),
                ('total_spent', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('satisfaction_score', models.FloatField(default=0.0, help_text='Average satisfaction score')),
                ('user', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='customer_record', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'customer_service_customer',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CustomerNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.TextField()),
                ('is_important', models.BooleanField(default=False)),
                ('is_private', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customer_notes', to='customer_service.customer')),
            ],
            options={
                'db_table': 'customer_service_customer_note',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CustomerInteraction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('interaction_type', models.CharField(choices=[('chat', 'Chat'), ('email', 'Email'), ('phone', 'Phone'), ('ticket', 'Support Ticket'), ('meeting', 'Meeting'), ('other', 'Other')], max_length=20)),
                ('subject', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('duration_minutes', models.IntegerField(blank=True, null=True)),
                ('outcome', models.CharField(blank=True, max_length=200)),
                ('follow_up_required', models.BooleanField(default=False)),
                ('follow_up_date', models.DateTimeField(blank=True, null=True)),
                ('customer_satisfaction', models.IntegerField(blank=True, choices=[(1, '1'), (2, '2'), (3, '3'), (4, '4'), (5, '5')], help_text='1-5 satisfaction rating', null=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interactions', to='customer_service.customer')),
                ('handled_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'customer_service_customer_interaction',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CustomerDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('contract', 'Contract'), ('invoice', 'Invoice'), ('receipt', 'Receipt'), ('id_document', 'ID Document'), ('other', 'Other')], max_length=20)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('file', models.FileField(upload_to='customer_documents/')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='customer_service.customer')),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'customer_service_customer_document',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['email'], name='customer_se_email_15f09c_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['customer_id'], name='customer_se_custome_05e4ae_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['status'], name='customer_se_status_dcc562_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['created_at'], name='customer_se_created_62929e_idx'),
        ),
    ]
