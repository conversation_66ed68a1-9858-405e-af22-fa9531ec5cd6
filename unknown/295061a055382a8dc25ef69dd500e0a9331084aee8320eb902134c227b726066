"""
KPI Algorithm Service
Advanced algorithmic features for KPI management including:
- Automated KPI calculation
- Predictive analytics
- Anomaly detection
- Smart recommendations
- Trend analysis
"""

import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from typing import Dict, List, Any, Optional, Tuple
from django.utils import timezone
from django.db.models import Q, Avg, Count, Sum
from .models import KPI, KPIValue, KPIAlert, Employee, Department, Project, MLPrediction, MLModel


class KPIAlgorithmService:
    """Service class for KPI algorithmic operations"""
    
    def __init__(self):
        self.confidence_threshold = 0.7
        self.anomaly_threshold = 2.0  # Standard deviations
        
    def calculate_automated_kpi(self, kpi: KPI) -> Optional[Decimal]:
        """
        Calculate KPI value using automated algorithms based on configuration
        """
        if not kpi.is_automated or not kpi.automation_config:
            return None
            
        config = kpi.automation_config
        calculation_method = config.get('method', 'formula')
        
        try:
            if calculation_method == 'formula':
                return self._calculate_formula_based(kpi, config)
            elif calculation_method == 'aggregation':
                return self._calculate_aggregation_based(kpi, config)
            elif calculation_method == 'ml_prediction':
                return self._calculate_ml_prediction(kpi, config)
            else:
                return None
        except Exception as e:
            print(f"Error calculating automated KPI {kpi.name}: {str(e)}")
            return None
    
    def _calculate_formula_based(self, kpi: KPI, config: Dict) -> Optional[Decimal]:
        """Calculate KPI using mathematical formula"""
        formula = config.get('formula', '')
        variables = config.get('variables', {})
        
        # Simple formula evaluation (in production, use a safe evaluator)
        try:
            # Replace variables in formula with actual values
            for var_name, var_config in variables.items():
                value = self._get_variable_value(var_config)
                formula = formula.replace(f'{{{var_name}}}', str(value))
            
            # Evaluate formula (simplified - in production use ast.literal_eval or similar)
            result = eval(formula)  # WARNING: Use safe evaluation in production
            return Decimal(str(round(result, 4)))
        except:
            return None
    
    def _calculate_aggregation_based(self, kpi: KPI, config: Dict) -> Optional[Decimal]:
        """Calculate KPI using data aggregation"""
        source_model = config.get('source_model')
        aggregation_type = config.get('aggregation', 'sum')
        field_name = config.get('field')
        filters = config.get('filters', {})
        
        if source_model == 'Employee':
            queryset = Employee.objects.all()
        elif source_model == 'Project':
            queryset = Project.objects.all()
        elif source_model == 'Department':
            queryset = Department.objects.all()
        else:
            return None
        
        # Apply filters
        for filter_key, filter_value in filters.items():
            queryset = queryset.filter(**{filter_key: filter_value})
        
        # Apply aggregation
        if aggregation_type == 'count':
            result = queryset.count()
        elif aggregation_type == 'sum':
            result = queryset.aggregate(total=Sum(field_name))['total'] or 0
        elif aggregation_type == 'avg':
            result = queryset.aggregate(avg=Avg(field_name))['avg'] or 0
        else:
            return None
        
        return Decimal(str(round(result, 4)))
    
    def _calculate_ml_prediction(self, kpi: KPI, config: Dict) -> Optional[Decimal]:
        """Calculate KPI using ML prediction"""
        if not kpi.ml_model:
            return None
        
        # Get input features
        input_data = self._prepare_ml_input_data(kpi, config)
        
        # Make prediction (simplified - in production use actual ML model)
        prediction_result = self._mock_ml_prediction(kpi, input_data)
        
        if prediction_result and prediction_result.get('confidence', 0) > self.confidence_threshold:
            return Decimal(str(round(prediction_result['predicted_value'], 4)))
        
        return None
    
    def _get_variable_value(self, var_config: Dict) -> float:
        """Get value for a formula variable"""
        source = var_config.get('source', 'constant')
        
        if source == 'constant':
            return float(var_config.get('value', 0))
        elif source == 'kpi':
            kpi_id = var_config.get('kpi_id')
            try:
                kpi = KPI.objects.get(id=kpi_id)
                latest_value = kpi.values.first()
                return float(latest_value.value) if latest_value else 0
            except:
                return 0
        elif source == 'database':
            # Query database for value
            model_name = var_config.get('model')
            field_name = var_config.get('field')
            aggregation = var_config.get('aggregation', 'count')
            
            if model_name == 'Employee':
                if aggregation == 'count':
                    return float(Employee.objects.count())
            # Add more model queries as needed
            
        return 0
    
    def detect_anomalies(self, kpi: KPI, new_value: Decimal) -> Dict[str, Any]:
        """
        Detect anomalies in KPI values using statistical methods
        """
        if not kpi.anomaly_detection:
            return {'is_anomaly': False}
        
        # Get historical values
        historical_values = list(
            kpi.values.order_by('-period_start')[:30].values_list('value', flat=True)
        )
        
        if len(historical_values) < 5:
            return {'is_anomaly': False, 'reason': 'Insufficient historical data'}
        
        # Convert to numpy array for calculations
        values = np.array([float(v) for v in historical_values])
        new_val = float(new_value)
        
        # Calculate statistics
        mean = np.mean(values)
        std = np.std(values)
        z_score = abs((new_val - mean) / std) if std > 0 else 0
        
        # Detect anomaly
        is_anomaly = z_score > self.anomaly_threshold
        
        # Additional checks
        anomaly_type = None
        if is_anomaly:
            if new_val > mean + (self.anomaly_threshold * std):
                anomaly_type = 'spike'
            elif new_val < mean - (self.anomaly_threshold * std):
                anomaly_type = 'drop'
        
        return {
            'is_anomaly': is_anomaly,
            'anomaly_type': anomaly_type,
            'z_score': round(z_score, 2),
            'mean': round(mean, 2),
            'std': round(std, 2),
            'threshold': self.anomaly_threshold,
            'confidence': min(z_score / self.anomaly_threshold, 1.0) if is_anomaly else 0
        }
    
    def generate_recommendations(self, kpi: KPI) -> List[Dict[str, Any]]:
        """
        Generate AI-powered recommendations for KPI improvement
        """
        if not kpi.auto_recommendations:
            return []
        
        recommendations = []
        
        # Analyze current performance
        current_value = self._get_current_kpi_value(kpi)
        target_value = float(kpi.target_value) if kpi.target_value else None
        
        if current_value and target_value:
            achievement = (current_value / target_value) * 100
            
            if achievement < 80:
                recommendations.extend(self._get_underperformance_recommendations(kpi, achievement))
            elif achievement > 120:
                recommendations.extend(self._get_overperformance_recommendations(kpi, achievement))
            else:
                recommendations.extend(self._get_maintenance_recommendations(kpi, achievement))
        
        # Analyze trends
        trend_analysis = self.analyze_trend(kpi)
        if trend_analysis['trend'] == 'declining':
            recommendations.extend(self._get_declining_trend_recommendations(kpi))
        
        return recommendations[:5]  # Limit to top 5 recommendations
    
    def analyze_trend(self, kpi: KPI, periods: int = 12) -> Dict[str, Any]:
        """
        Analyze KPI trend using linear regression
        """
        values = list(
            kpi.values.order_by('-period_start')[:periods].values_list('value', 'period_start')
        )
        
        if len(values) < 3:
            return {'trend': 'insufficient_data', 'slope': 0, 'r_squared': 0}
        
        # Prepare data for trend analysis
        y = np.array([float(v[0]) for v in reversed(values)])
        x = np.arange(len(y))
        
        # Calculate linear regression
        slope, intercept = np.polyfit(x, y, 1)
        correlation = np.corrcoef(x, y)[0, 1]
        r_squared = correlation ** 2
        
        # Determine trend direction
        if abs(slope) < 0.1:
            trend = 'stable'
        elif slope > 0:
            trend = 'improving' if kpi.trend_direction == 'UP' else 'declining'
        else:
            trend = 'declining' if kpi.trend_direction == 'UP' else 'improving'
        
        return {
            'trend': trend,
            'slope': round(slope, 4),
            'r_squared': round(r_squared, 4),
            'confidence': round(r_squared * 100, 1),
            'periods_analyzed': len(values)
        }
    
    def predict_future_values(self, kpi: KPI, periods_ahead: int = 6) -> List[Dict[str, Any]]:
        """
        Predict future KPI values using time series analysis
        """
        historical_values = list(
            kpi.values.order_by('-period_start')[:24].values_list('value', 'period_start')
        )
        
        if len(historical_values) < 6:
            return []
        
        # Simple linear trend prediction (in production, use ARIMA or other time series models)
        trend_analysis = self.analyze_trend(kpi, len(historical_values))
        slope = trend_analysis['slope']
        
        latest_value = float(historical_values[0][0])
        predictions = []
        
        for i in range(1, periods_ahead + 1):
            predicted_value = latest_value + (slope * i)
            
            # Add some uncertainty
            confidence = max(0.5, trend_analysis['r_squared'])
            
            predictions.append({
                'period': i,
                'predicted_value': round(predicted_value, 2),
                'confidence': round(confidence * 100, 1),
                'date': timezone.now() + timedelta(days=30 * i)  # Assuming monthly periods
            })
        
        return predictions
    
    def _get_current_kpi_value(self, kpi: KPI) -> Optional[float]:
        """Get current KPI value"""
        latest_value = kpi.values.first()
        return float(latest_value.value) if latest_value else None
    
    def _prepare_ml_input_data(self, kpi: KPI, config: Dict) -> Dict[str, Any]:
        """Prepare input data for ML prediction"""
        # This would prepare actual features for ML model
        return {
            'kpi_id': str(kpi.id),
            'historical_values': [float(v) for v in kpi.values.values_list('value', flat=True)[:12]],
            'target_value': float(kpi.target_value) if kpi.target_value else None,
            'frequency': kpi.frequency,
            'measurement_type': kpi.measurement_type
        }
    
    def _mock_ml_prediction(self, kpi: KPI, input_data: Dict) -> Dict[str, Any]:
        """Mock ML prediction (replace with actual ML model in production)"""
        historical = input_data.get('historical_values', [])
        if not historical:
            return None
        
        # Simple prediction based on trend
        recent_avg = sum(historical[:3]) / len(historical[:3]) if len(historical) >= 3 else historical[0]
        predicted_value = recent_avg * (1 + np.random.normal(0, 0.1))  # Add some variance
        
        return {
            'predicted_value': predicted_value,
            'confidence': 0.75 + np.random.random() * 0.2,  # Random confidence between 0.75-0.95
            'factors': ['historical_trend', 'seasonal_pattern', 'external_factors'],
            'model_version': '1.0'
        }
    
    def _get_underperformance_recommendations(self, kpi: KPI, achievement: float) -> List[Dict[str, Any]]:
        """Get recommendations for underperforming KPIs"""
        return [
            {
                'type': 'action',
                'priority': 'high',
                'title': 'Increase Resource Allocation',
                'description': f'KPI is at {achievement:.1f}% of target. Consider allocating more resources.',
                'estimated_impact': 'medium'
            },
            {
                'type': 'analysis',
                'priority': 'medium',
                'title': 'Root Cause Analysis',
                'description': 'Conduct detailed analysis to identify bottlenecks and obstacles.',
                'estimated_impact': 'high'
            }
        ]
    
    def _get_overperformance_recommendations(self, kpi: KPI, achievement: float) -> List[Dict[str, Any]]:
        """Get recommendations for overperforming KPIs"""
        return [
            {
                'type': 'optimization',
                'priority': 'medium',
                'title': 'Raise Target Goals',
                'description': f'KPI is at {achievement:.1f}% of target. Consider setting higher targets.',
                'estimated_impact': 'medium'
            }
        ]
    
    def _get_maintenance_recommendations(self, kpi: KPI, achievement: float) -> List[Dict[str, Any]]:
        """Get recommendations for KPIs on target"""
        return [
            {
                'type': 'maintenance',
                'priority': 'low',
                'title': 'Maintain Current Performance',
                'description': 'KPI is performing well. Continue current strategies.',
                'estimated_impact': 'low'
            }
        ]
    
    def _get_declining_trend_recommendations(self, kpi: KPI) -> List[Dict[str, Any]]:
        """Get recommendations for declining trend KPIs"""
        return [
            {
                'type': 'urgent',
                'priority': 'high',
                'title': 'Address Declining Trend',
                'description': 'KPI shows declining trend. Immediate intervention required.',
                'estimated_impact': 'high'
            }
        ]


# Global instance
kpi_algorithm_service = KPIAlgorithmService()
