"""
Simple Customer Service
Extends existing models to track customers properly
"""

from django.contrib.auth.models import User
from django.utils import timezone
from .models import SupportTicket
import re
import logging

logger = logging.getLogger(__name__)

class SimpleCustomerService:
    """Simple service for managing customers using existing User model"""
    
    @staticmethod
    def create_or_get_customer_user(email, first_name=None, last_name=None, **kwargs):
        """
        Create a new user/customer or get existing one by email
        """
        try:
            # Try to get existing user
            user = User.objects.get(email=email)
            logger.info(f"Found existing customer user: {user.username}")
            return user, False
            
        except User.DoesNotExist:
            # Create new user/customer
            username = SimpleCustomerService.generate_username_from_email(email)
            
            user_data = {
                'username': username,
                'email': email,
                'first_name': first_name or SimpleCustomerService.extract_name_from_email(email),
                'last_name': last_name or '',
                'is_active': True,
            }
            
            user = User.objects.create(**user_data)
            logger.info(f"Created new customer user: {user.username}")
            
            return user, True
            
        except Exception as e:
            logger.error(f"Error creating customer user: {e}")
            raise
    
    @staticmethod
    def generate_username_from_email(email):
        """Generate a unique username from email"""
        try:
            base_username = email.split('@')[0]
            # Remove special characters
            base_username = re.sub(r'[^a-zA-Z0-9]', '', base_username)
            
            # Ensure it's not too long
            base_username = base_username[:20]
            
            # Check if username exists
            username = base_username
            counter = 1
            while User.objects.filter(username=username).exists():
                username = f"{base_username}{counter}"
                counter += 1
                
            return username
        except:
            # Fallback to timestamp-based username
            return f"user_{int(timezone.now().timestamp())}"
    
    @staticmethod
    def extract_name_from_email(email):
        """Extract a reasonable first name from email"""
        try:
            username = email.split('@')[0]
            # Remove numbers and special characters
            name = re.sub(r'[^a-zA-Z]', ' ', username)
            # Capitalize first letter of each word
            name = ' '.join(word.capitalize() for word in name.split() if word)
            return name[:30] if name else 'Customer'
        except:
            return 'Customer'
    
    @staticmethod
    def create_customer_from_chat(message_data):
        """Create customer from chat interaction"""
        email = message_data.get('customer_email')
        name = message_data.get('customer_name', 'Anonymous')
        
        if not email:
            # Generate a temporary email for anonymous users
            session_id = message_data.get('session_id', 'unknown')
            email = f"anonymous_{session_id}@temp.local"
            name = name or 'Anonymous User'
        
        # Split name into first and last
        name_parts = name.split(' ', 1) if name else ['Customer']
        first_name = name_parts[0]
        last_name = name_parts[1] if len(name_parts) > 1 else ''
        
        user, created = SimpleCustomerService.create_or_get_customer_user(
            email=email,
            first_name=first_name,
            last_name=last_name
        )
        
        return user, created
    
    @staticmethod
    def create_support_ticket_from_chat(customer_user, message_data):
        """Create a support ticket from chat interaction"""
        try:
            ticket_data = {
                'title': message_data.get('title', 'Chat Support Request'),
                'description': message_data.get('description', 'Support request from chat'),
                'customer': customer_user,
                'customer_email': customer_user.email,
                'customer_phone': message_data.get('customer_phone', ''),
                'source': 'chat',
                'priority': message_data.get('priority', 'medium'),
                'status': 'open'
            }
            
            ticket = SupportTicket.objects.create(**ticket_data)
            logger.info(f"Created support ticket: {ticket.ticket_id} for customer: {customer_user.username}")
            
            return ticket
            
        except Exception as e:
            logger.error(f"Error creating support ticket: {e}")
            return None
    
    @staticmethod
    def get_customer_stats(customer_user):
        """Get customer statistics"""
        try:
            tickets = SupportTicket.objects.filter(customer=customer_user)
            
            stats = {
                'customer_id': f"CUST-{customer_user.id:06d}",
                'total_tickets': tickets.count(),
                'open_tickets': tickets.filter(status='open').count(),
                'resolved_tickets': tickets.filter(status='resolved').count(),
                'recent_tickets': tickets.order_by('-created_at')[:5],
                'customer_since': customer_user.date_joined,
                'last_ticket': tickets.order_by('-created_at').first(),
                'email': customer_user.email,
                'name': customer_user.get_full_name() or customer_user.username,
            }
            
            # Calculate average satisfaction if available
            satisfaction_ratings = [
                ticket.customer_satisfaction_rating 
                for ticket in tickets 
                if ticket.customer_satisfaction_rating
            ]
            
            if satisfaction_ratings:
                stats['avg_satisfaction'] = sum(satisfaction_ratings) / len(satisfaction_ratings)
            else:
                stats['avg_satisfaction'] = 0
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting customer stats: {e}")
            return {}
    
    @staticmethod
    def search_customers(query):
        """Search customers by various fields"""
        try:
            users = User.objects.filter(
                models.Q(username__icontains=query) |
                models.Q(first_name__icontains=query) |
                models.Q(last_name__icontains=query) |
                models.Q(email__icontains=query)
            ).order_by('-date_joined')
            
            return users
            
        except Exception as e:
            logger.error(f"Error searching customers: {e}")
            return User.objects.none()
    
    @staticmethod
    def get_customer_display_id(user):
        """Get a display-friendly customer ID"""
        return f"CUST-{user.id:06d}"
    
    @staticmethod
    def log_chat_interaction(customer_user, message, response, metadata=None):
        """Log chat interaction as a support ticket comment or note"""
        try:
            # Find the most recent open ticket for this customer
            recent_ticket = SupportTicket.objects.filter(
                customer=customer_user,
                status__in=['open', 'in_progress']
            ).order_by('-created_at').first()
            
            if not recent_ticket:
                # Create a new ticket for chat interactions
                recent_ticket = SimpleCustomerService.create_support_ticket_from_chat(
                    customer_user,
                    {
                        'title': 'Chat Support Session',
                        'description': f'Chat interaction started\nCustomer: {message}\nAI: {response}',
                        'priority': 'medium'
                    }
                )
            
            if recent_ticket:
                # Add comment to existing ticket
                from .models import TicketComment
                
                TicketComment.objects.create(
                    ticket=recent_ticket,
                    author=customer_user,
                    comment_type='customer',
                    content=f"Chat Message: {message}\nAI Response: {response}",
                    is_public=True
                )
                
                logger.info(f"Logged chat interaction for customer {customer_user.username} on ticket {recent_ticket.ticket_id}")
            
        except Exception as e:
            logger.error(f"Error logging chat interaction: {e}")

# Import Django's Q for search functionality
from django.db import models
