"""
CRITICAL FIX: Multi-Factor Authentication Implementation
Enterprise-grade MFA with TOTP, backup codes, and security monitoring
"""

import pyotp
import qrcode
import io
import base64
import secrets
import hashlib
from datetime import datetime, timedelta
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
import logging

logger = logging.getLogger(__name__)
User = get_user_model()

class MFAManager:
    """Multi-Factor Authentication Manager"""
    
    @staticmethod
    def generate_secret_key():
        """Generate a new TOTP secret key"""
        return pyotp.random_base32()
    
    @staticmethod
    def generate_qr_code(user, secret_key):
        """Generate QR code for TOTP setup"""
        
        # Create TOTP URI
        totp_uri = pyotp.totp.TOTP(secret_key).provisioning_uri(
            name=user.email,
            issuer_name="EMS - Employee Management System"
        )
        
        # Generate QR code
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        # Create QR code image
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        buffer.seek(0)
        
        qr_code_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        return {
            "qr_code": f"data:image/png;base64,{qr_code_base64}",
            "secret_key": secret_key,
            "manual_entry_key": secret_key
        }
    
    @staticmethod
    def verify_totp_code(secret_key, code, window=1):
        """Verify TOTP code with time window tolerance"""
        
        totp = pyotp.TOTP(secret_key)
        
        # Check current time and nearby time windows
        for i in range(-window, window + 1):
            if totp.verify(code, valid_window=i):
                return True
        
        return False
    
    @staticmethod
    def generate_backup_codes(count=10):
        """Generate backup codes for MFA recovery"""
        
        backup_codes = []
        for _ in range(count):
            # Generate 8-character alphanumeric code
            code = secrets.token_hex(4).upper()
            backup_codes.append(code)
        
        return backup_codes
    
    @staticmethod
    def hash_backup_code(code):
        """Hash backup code for secure storage"""
        
        salt = settings.SECRET_KEY.encode()
        return hashlib.pbkdf2_hmac('sha256', code.encode(), salt, 100000).hex()
    
    @staticmethod
    def verify_backup_code(user, code):
        """Verify and consume backup code"""
        
        try:
            from .models import UserMFABackupCode
            
            hashed_code = MFAManager.hash_backup_code(code)
            
            backup_code = UserMFABackupCode.objects.filter(
                user=user,
                code_hash=hashed_code,
                used=False
            ).first()
            
            if backup_code:
                # Mark as used
                backup_code.used = True
                backup_code.used_at = timezone.now()
                backup_code.save()
                
                logger.info(f"Backup code used for user {user.id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error verifying backup code: {e}")
            return False
    
    @staticmethod
    def is_mfa_required(user):
        """Check if MFA is required for user"""
        
        try:
            from .models import UserMFASettings
            
            mfa_settings = UserMFASettings.objects.filter(user=user).first()
            return mfa_settings and mfa_settings.is_enabled
            
        except Exception:
            return False
    
    @staticmethod
    def get_mfa_settings(user):
        """Get user's MFA settings"""
        
        try:
            from .models import UserMFASettings
            
            mfa_settings, created = UserMFASettings.objects.get_or_create(user=user)
            return mfa_settings
            
        except Exception as e:
            logger.error(f"Error getting MFA settings: {e}")
            return None

class MFASecurityMonitor:
    """Monitor MFA security events"""
    
    @staticmethod
    def log_mfa_event(user, event_type, success=True, ip_address=None, user_agent=None):
        """Log MFA security event"""
        
        try:
            from .models import MFASecurityLog
            
            MFASecurityLog.objects.create(
                user=user,
                event_type=event_type,
                success=success,
                ip_address=ip_address,
                user_agent=user_agent,
                timestamp=timezone.now()
            )
            
            # Check for suspicious activity
            MFASecurityMonitor.check_suspicious_activity(user, event_type, success)
            
        except Exception as e:
            logger.error(f"Error logging MFA event: {e}")
    
    @staticmethod
    def check_suspicious_activity(user, event_type, success):
        """Check for suspicious MFA activity"""
        
        try:
            from .models import MFASecurityLog
            
            # Check for multiple failed attempts
            if not success and event_type in ['totp_verification', 'backup_code_verification']:
                recent_failures = MFASecurityLog.objects.filter(
                    user=user,
                    event_type=event_type,
                    success=False,
                    timestamp__gte=timezone.now() - timedelta(minutes=15)
                ).count()
                
                if recent_failures >= 5:
                    MFASecurityMonitor.trigger_security_alert(user, 'multiple_mfa_failures')
            
            # Check for unusual access patterns
            if success and event_type == 'totp_verification':
                recent_locations = MFASecurityLog.objects.filter(
                    user=user,
                    event_type=event_type,
                    success=True,
                    timestamp__gte=timezone.now() - timedelta(hours=24)
                ).values_list('ip_address', flat=True).distinct()
                
                if len(recent_locations) > 5:
                    MFASecurityMonitor.trigger_security_alert(user, 'unusual_access_pattern')
                    
        except Exception as e:
            logger.error(f"Error checking suspicious activity: {e}")
    
    @staticmethod
    def trigger_security_alert(user, alert_type):
        """Trigger security alert"""
        
        logger.warning(f"MFA Security Alert: {alert_type} for user {user.id}")
        
        # Here you would implement alerting logic:
        # - Send email notification
        # - Create admin notification
        # - Temporarily lock account if needed
        # - Send to SIEM system
        
        cache.set(f"mfa_security_alert_{user.id}_{alert_type}", True, 3600)

# API Views for MFA

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def setup_mfa(request):
    """Setup MFA for user"""
    
    try:
        user = request.user
        
        # Check if MFA is already enabled
        if MFAManager.is_mfa_required(user):
            return Response({
                'error': 'MFA is already enabled for this user'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Generate secret key
        secret_key = MFAManager.generate_secret_key()
        
        # Generate QR code
        qr_data = MFAManager.generate_qr_code(user, secret_key)
        
        # Generate backup codes
        backup_codes = MFAManager.generate_backup_codes()
        
        # Store secret key temporarily (user needs to verify before enabling)
        cache.set(f"mfa_setup_{user.id}", {
            'secret_key': secret_key,
            'backup_codes': backup_codes
        }, 600)  # 10 minutes
        
        MFASecurityMonitor.log_mfa_event(
            user, 'mfa_setup_initiated', True,
            request.META.get('REMOTE_ADDR'),
            request.META.get('HTTP_USER_AGENT')
        )
        
        return Response({
            'qr_code': qr_data['qr_code'],
            'manual_entry_key': qr_data['manual_entry_key'],
            'backup_codes': backup_codes,
            'message': 'Scan the QR code with your authenticator app and verify with a code to complete setup'
        })
        
    except Exception as e:
        logger.error(f"MFA setup error: {e}")
        return Response({
            'error': 'Failed to setup MFA'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def verify_mfa_setup(request):
    """Verify MFA setup with TOTP code"""
    
    try:
        user = request.user
        code = request.data.get('code')
        
        if not code:
            return Response({
                'error': 'TOTP code is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get setup data from cache
        setup_data = cache.get(f"mfa_setup_{user.id}")
        if not setup_data:
            return Response({
                'error': 'MFA setup session expired. Please start setup again.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        secret_key = setup_data['secret_key']
        backup_codes = setup_data['backup_codes']
        
        # Verify TOTP code
        if not MFAManager.verify_totp_code(secret_key, code):
            MFASecurityMonitor.log_mfa_event(
                user, 'mfa_setup_verification', False,
                request.META.get('REMOTE_ADDR'),
                request.META.get('HTTP_USER_AGENT')
            )
            
            return Response({
                'error': 'Invalid TOTP code'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Enable MFA for user
        from .models import UserMFASettings, UserMFABackupCode
        
        mfa_settings = MFAManager.get_mfa_settings(user)
        mfa_settings.secret_key = secret_key
        mfa_settings.is_enabled = True
        mfa_settings.enabled_at = timezone.now()
        mfa_settings.save()
        
        # Save backup codes
        for code in backup_codes:
            UserMFABackupCode.objects.create(
                user=user,
                code_hash=MFAManager.hash_backup_code(code)
            )
        
        # Clear setup cache
        cache.delete(f"mfa_setup_{user.id}")
        
        MFASecurityMonitor.log_mfa_event(
            user, 'mfa_enabled', True,
            request.META.get('REMOTE_ADDR'),
            request.META.get('HTTP_USER_AGENT')
        )
        
        return Response({
            'message': 'MFA has been successfully enabled',
            'backup_codes': backup_codes
        })
        
    except Exception as e:
        logger.error(f"MFA verification error: {e}")
        return Response({
            'error': 'Failed to verify MFA setup'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def verify_mfa_code(request):
    """Verify MFA code for authentication"""
    
    try:
        user = request.user
        code = request.data.get('code')
        backup_code = request.data.get('backup_code')
        
        if not code and not backup_code:
            return Response({
                'error': 'TOTP code or backup code is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Check if MFA is enabled
        if not MFAManager.is_mfa_required(user):
            return Response({
                'error': 'MFA is not enabled for this user'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        mfa_settings = MFAManager.get_mfa_settings(user)
        verified = False
        
        if backup_code:
            # Verify backup code
            verified = MFAManager.verify_backup_code(user, backup_code)
            event_type = 'backup_code_verification'
        else:
            # Verify TOTP code
            verified = MFAManager.verify_totp_code(mfa_settings.secret_key, code)
            event_type = 'totp_verification'
        
        MFASecurityMonitor.log_mfa_event(
            user, event_type, verified,
            request.META.get('REMOTE_ADDR'),
            request.META.get('HTTP_USER_AGENT')
        )
        
        if verified:
            # Set MFA verification flag in session/cache
            cache.set(f"mfa_verified_{user.id}", True, 3600)  # 1 hour
            
            return Response({
                'message': 'MFA verification successful',
                'verified': True
            })
        else:
            return Response({
                'error': 'Invalid MFA code',
                'verified': False
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        logger.error(f"MFA code verification error: {e}")
        return Response({
            'error': 'Failed to verify MFA code'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def disable_mfa(request):
    """Disable MFA for user"""
    
    try:
        user = request.user
        password = request.data.get('password')
        
        # Verify password before disabling MFA
        if not user.check_password(password):
            return Response({
                'error': 'Invalid password'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Disable MFA
        mfa_settings = MFAManager.get_mfa_settings(user)
        mfa_settings.is_enabled = False
        mfa_settings.disabled_at = timezone.now()
        mfa_settings.save()
        
        # Delete backup codes
        from .models import UserMFABackupCode
        UserMFABackupCode.objects.filter(user=user).delete()
        
        MFASecurityMonitor.log_mfa_event(
            user, 'mfa_disabled', True,
            request.META.get('REMOTE_ADDR'),
            request.META.get('HTTP_USER_AGENT')
        )
        
        return Response({
            'message': 'MFA has been disabled'
        })
        
    except Exception as e:
        logger.error(f"MFA disable error: {e}")
        return Response({
            'error': 'Failed to disable MFA'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def mfa_status(request):
    """Get MFA status for user"""
    
    try:
        user = request.user
        mfa_enabled = MFAManager.is_mfa_required(user)
        
        response_data = {
            'mfa_enabled': mfa_enabled,
            'user_id': user.id
        }
        
        if mfa_enabled:
            mfa_settings = MFAManager.get_mfa_settings(user)
            response_data.update({
                'enabled_at': mfa_settings.enabled_at.isoformat() if mfa_settings.enabled_at else None,
                'backup_codes_remaining': user.mfa_backup_codes.filter(used=False).count()
            })
        
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"MFA status error: {e}")
        return Response({
            'error': 'Failed to get MFA status'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
