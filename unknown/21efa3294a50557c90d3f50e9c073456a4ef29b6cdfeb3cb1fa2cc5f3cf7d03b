"""
KPI URL Configuration
Routes for KPI management APIs
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    KPICategoryViewSet, KPIViewSet, KPIValueViewSet,
    KPITargetViewSet, KPIAlertViewSet, kpi_dashboard
)

# Create router for KPI viewsets
router = DefaultRouter()
router.register(r'categories', KPICategoryViewSet, basename='kpi-categories')
router.register(r'kpis', KPIViewSet, basename='kpis')
router.register(r'values', KPIValueViewSet, basename='kpi-values')
router.register(r'targets', KPITargetViewSet, basename='kpi-targets')
router.register(r'alerts', KPIAlertViewSet, basename='kpi-alerts')

urlpatterns = [
    # Dashboard endpoints
    path('dashboard/', kpi_dashboard, name='kpi-dashboard'),

    # Include router URLs
    path('', include(router.urls)),
]
