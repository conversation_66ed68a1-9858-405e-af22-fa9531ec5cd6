# CRITICAL FIX: Prometheus Configuration for EMS Monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # System metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # EMS Backend Application
  - job_name: 'ems-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/api/metrics/'
    scrape_interval: 30s
    scrape_timeout: 10s

  # EMS Frontend Application
  - job_name: 'ems-frontend'
    static_configs:
      - targets: ['frontend:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # PostgreSQL Database
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Redis Cache
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # Blackbox probes for endpoint monitoring
  - job_name: 'blackbox-http'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - http://backend:8000/api/health/
        - http://frontend:3000/
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # API endpoint monitoring
  - job_name: 'ems-api-endpoints'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - http://backend:8000/api/auth/login/
        - http://backend:8000/api/employees/
        - http://backend:8000/api/departments/
        - http://backend:8000/api/dashboard-stats/
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # Database connectivity monitoring
  - job_name: 'database-connectivity'
    metrics_path: /probe
    params:
      module: [tcp_connect]
    static_configs:
      - targets:
        - postgres:5432
        - redis:6379
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115
