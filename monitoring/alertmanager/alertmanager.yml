# CRITICAL FIX: Advanced AlertManager Configuration
# Enterprise-grade alerting with intelligent routing and escalation

global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your_smtp_password'
  slack_api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'

# Templates for alert formatting
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Route tree for alert routing
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'
  
  routes:
    # Critical alerts - immediate notification
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      repeat_interval: 5m
      routes:
        # Database down - immediate escalation
        - match:
            alertname: PostgreSQLDown
          receiver: 'database-team'
          continue: true
        
        # Application down - immediate escalation  
        - match:
            alertname: EMSBackendDown
          receiver: 'dev-team'
          continue: true
        
        # Security incidents - immediate escalation
        - match:
            alertname: HighFailedLoginAttempts
          receiver: 'security-team'
          continue: true

    # High severity alerts - quick notification
    - match:
        severity: high
      receiver: 'high-priority-alerts'
      group_wait: 30s
      repeat_interval: 15m
      
    # Medium severity alerts - standard notification
    - match:
        severity: medium
      receiver: 'medium-priority-alerts'
      group_wait: 5m
      repeat_interval: 1h
      
    # Low severity alerts - batched notification
    - match:
        severity: low
      receiver: 'low-priority-alerts'
      group_wait: 10m
      repeat_interval: 4h

    # Business hours vs after hours routing
    - match:
        severity: critical
      receiver: 'after-hours-critical'
      active_time_intervals:
        - 'after-hours'
      
    # Maintenance window - suppress non-critical alerts
    - match_re:
        severity: 'medium|low'
      receiver: 'null'
      active_time_intervals:
        - 'maintenance-window'

# Time intervals for routing
time_intervals:
  - name: 'business-hours'
    time_intervals:
      - times:
          - start_time: '09:00'
            end_time: '17:00'
        weekdays: ['monday:friday']
        
  - name: 'after-hours'
    time_intervals:
      - times:
          - start_time: '17:01'
            end_time: '08:59'
        weekdays: ['monday:friday']
      - times:
          - start_time: '00:00'
            end_time: '23:59'
        weekdays: ['saturday', 'sunday']
        
  - name: 'maintenance-window'
    time_intervals:
      - times:
          - start_time: '02:00'
            end_time: '04:00'
        weekdays: ['sunday']

# Alert receivers
receivers:
  # Default receiver
  - name: 'default-receiver'
    email_configs:
      - to: '<EMAIL>'
        subject: 'EMS Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Instance: {{ .Labels.instance }}
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}

  # Critical alerts - multiple channels
  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 CRITICAL: {{ .GroupLabels.alertname }}'
        body: |
          CRITICAL ALERT DETECTED
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Service: {{ .Labels.service }}
          Instance: {{ .Labels.instance }}
          Started: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          
          Runbook: {{ .Annotations.runbook_url }}
          Dashboard: {{ .Annotations.dashboard_url }}
          {{ end }}
          
          This is a critical alert requiring immediate attention.
          
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/CRITICAL/WEBHOOK'
        channel: '#critical-alerts'
        title: '🚨 Critical Alert: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Instance:* {{ .Labels.instance }}
          *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
        send_resolved: true
        
    webhook_configs:
      - url: 'https://your-pagerduty-integration.com/webhook'
        send_resolved: true

  # Database team alerts
  - name: 'database-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '🗄️ Database Alert: {{ .GroupLabels.alertname }}'
        body: |
          DATABASE ALERT
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Database: {{ .Labels.database }}
          Instance: {{ .Labels.instance }}
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
          
    slack_configs:
      - channel: '#database-alerts'
        title: '🗄️ Database Alert: {{ .GroupLabels.alertname }}'

  # Development team alerts
  - name: 'dev-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '💻 Application Alert: {{ .GroupLabels.alertname }}'
        
    slack_configs:
      - channel: '#dev-alerts'
        title: '💻 Application Alert: {{ .GroupLabels.alertname }}'

  # Security team alerts
  - name: 'security-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '🔒 Security Alert: {{ .GroupLabels.alertname }}'
        body: |
          SECURITY ALERT DETECTED
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Source IP: {{ .Labels.source_ip }}
          User: {{ .Labels.user }}
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
          
          Immediate investigation required.
          
    slack_configs:
      - channel: '#security-alerts'
        title: '🔒 Security Alert: {{ .GroupLabels.alertname }}'
        
    webhook_configs:
      - url: 'https://your-siem-system.com/webhook'

  # High priority alerts
  - name: 'high-priority-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🟠 High Priority: {{ .GroupLabels.alertname }}'
        
    slack_configs:
      - channel: '#high-priority-alerts'
        title: '🟠 High Priority: {{ .GroupLabels.alertname }}'

  # Medium priority alerts
  - name: 'medium-priority-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🟡 Medium Priority: {{ .GroupLabels.alertname }}'
        
    slack_configs:
      - channel: '#medium-priority-alerts'
        title: '🟡 Medium Priority: {{ .GroupLabels.alertname }}'

  # Low priority alerts
  - name: 'low-priority-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🟢 Low Priority: {{ .GroupLabels.alertname }}'
        
    slack_configs:
      - channel: '#low-priority-alerts'
        title: '🟢 Low Priority: {{ .GroupLabels.alertname }}'

  # After hours critical alerts
  - name: 'after-hours-critical'
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 AFTER HOURS CRITICAL: {{ .GroupLabels.alertname }}'
        
    webhook_configs:
      - url: 'https://your-pagerduty-integration.com/webhook'
        send_resolved: true
        
    # SMS notifications for after hours
    webhook_configs:
      - url: 'https://your-sms-service.com/webhook'
        http_config:
          basic_auth:
            username: 'sms_user'
            password: 'sms_password'

  # Null receiver for suppressed alerts
  - name: 'null'

# Inhibition rules to reduce noise
inhibit_rules:
  # Inhibit warning alerts if critical alert is firing
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance']

  # Inhibit individual service alerts if entire instance is down
  - source_match:
      alertname: 'InstanceDown'
    target_match_re:
      alertname: '.*'
    equal: ['instance']

  # Inhibit database connection alerts if database is down
  - source_match:
      alertname: 'PostgreSQLDown'
    target_match_re:
      alertname: 'PostgreSQL.*'
    equal: ['instance']

  # Inhibit application alerts if backend is down
  - source_match:
      alertname: 'EMSBackendDown'
    target_match_re:
      alertname: 'High.*|.*Error.*'
    equal: ['instance']
