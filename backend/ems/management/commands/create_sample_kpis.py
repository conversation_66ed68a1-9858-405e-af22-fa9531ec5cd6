"""
Django management command to create sample KPI data
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
import random
from decimal import Decimal

from ems.models import (
    KPICategory, KPI, KPIValue, KPITarget, KPIAlert,
    Employee, Department, Role
)


class Command(BaseCommand):
    help = 'Create sample KPI data for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing KPI data before creating new data',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing KPI data...')
            KPIAlert.objects.all().delete()
            KPIValue.objects.all().delete()
            KPITarget.objects.all().delete()
            KPI.objects.all().delete()
            KPICategory.objects.all().delete()

        self.stdout.write('Creating sample KPI data...')

        # Create KPI Categories
        categories_data = [
            {
                'name': 'FINANCIAL',
                'name_ar': 'المالية',
                'description': 'Financial performance indicators',
                'description_ar': 'مؤشرات الأداء المالي',
                'icon': 'DollarSign',
                'color': 'from-green-500 to-green-600',
                'sort_order': 1
            },
            {
                'name': 'HR',
                'name_ar': 'الموارد البشرية',
                'description': 'Human resources metrics',
                'description_ar': 'مقاييس الموارد البشرية',
                'icon': 'Users',
                'color': 'from-blue-500 to-blue-600',
                'sort_order': 2
            },
            {
                'name': 'OPERATIONS',
                'name_ar': 'العمليات',
                'description': 'Operational efficiency metrics',
                'description_ar': 'مقاييس كفاءة العمليات',
                'icon': 'Settings',
                'color': 'from-purple-500 to-purple-600',
                'sort_order': 3
            },
            {
                'name': 'SALES',
                'name_ar': 'المبيعات والتسويق',
                'description': 'Sales and marketing performance',
                'description_ar': 'أداء المبيعات والتسويق',
                'icon': 'TrendingUp',
                'color': 'from-orange-500 to-orange-600',
                'sort_order': 4
            },
            {
                'name': 'CUSTOMER',
                'name_ar': 'خدمة العملاء',
                'description': 'Customer satisfaction metrics',
                'description_ar': 'مقاييس رضا العملاء',
                'icon': 'Heart',
                'color': 'from-pink-500 to-pink-600',
                'sort_order': 5
            }
        ]

        categories = {}
        for cat_data in categories_data:
            category, created = KPICategory.objects.get_or_create(
                name=cat_data['name'],
                defaults=cat_data
            )
            categories[cat_data['name']] = category
            if created:
                self.stdout.write(f'Created category: {category.name}')

        # Get first employee as creator
        try:
            creator = Employee.objects.first()
            if not creator:
                self.stdout.write(self.style.ERROR('No employees found. Please create employees first.'))
                return
        except Exception:
            self.stdout.write(self.style.ERROR('Error getting employee. Please ensure employees exist.'))
            return

        # Create sample KPIs
        kpis_data = [
            # Financial KPIs
            {
                'name': 'Monthly Revenue',
                'name_ar': 'الإيرادات الشهرية',
                'description': 'Total monthly revenue generated',
                'description_ar': 'إجمالي الإيرادات الشهرية المحققة',
                'category': 'FINANCIAL',
                'measurement_type': 'CURRENCY',
                'unit': 'USD',
                'unit_ar': 'دولار',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': Decimal('100000.00'),
                'warning_threshold': Decimal('80000.00'),
                'critical_threshold': Decimal('60000.00')
            },
            {
                'name': 'Profit Margin',
                'name_ar': 'هامش الربح',
                'description': 'Net profit margin percentage',
                'description_ar': 'نسبة هامش الربح الصافي',
                'category': 'FINANCIAL',
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'unit_ar': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': Decimal('20.00'),
                'warning_threshold': Decimal('15.00'),
                'critical_threshold': Decimal('10.00')
            },
            # HR KPIs
            {
                'name': 'Employee Satisfaction',
                'name_ar': 'رضا الموظفين',
                'description': 'Employee satisfaction score',
                'description_ar': 'درجة رضا الموظفين',
                'category': 'HR',
                'measurement_type': 'SCORE',
                'unit': '/10',
                'unit_ar': '/10',
                'frequency': 'QUARTERLY',
                'trend_direction': 'UP',
                'target_value': Decimal('8.50'),
                'warning_threshold': Decimal('7.00'),
                'critical_threshold': Decimal('6.00')
            },
            {
                'name': 'Employee Turnover Rate',
                'name_ar': 'معدل دوران الموظفين',
                'description': 'Monthly employee turnover rate',
                'description_ar': 'معدل دوران الموظفين الشهري',
                'category': 'HR',
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'unit_ar': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'DOWN',
                'target_value': Decimal('5.00'),
                'warning_threshold': Decimal('8.00'),
                'critical_threshold': Decimal('12.00')
            },
            # Operations KPIs
            {
                'name': 'Project Completion Rate',
                'name_ar': 'معدل إنجاز المشاريع',
                'description': 'Percentage of projects completed on time',
                'description_ar': 'نسبة المشاريع المنجزة في الوقت المحدد',
                'category': 'OPERATIONS',
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'unit_ar': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': Decimal('90.00'),
                'warning_threshold': Decimal('80.00'),
                'critical_threshold': Decimal('70.00')
            },
            # Sales KPIs
            {
                'name': 'Sales Conversion Rate',
                'name_ar': 'معدل تحويل المبيعات',
                'description': 'Lead to sale conversion rate',
                'description_ar': 'معدل تحويل العملاء المحتملين إلى مبيعات',
                'category': 'SALES',
                'measurement_type': 'PERCENTAGE',
                'unit': '%',
                'unit_ar': '%',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': Decimal('25.00'),
                'warning_threshold': Decimal('20.00'),
                'critical_threshold': Decimal('15.00')
            },
            # Customer KPIs
            {
                'name': 'Customer Satisfaction Score',
                'name_ar': 'درجة رضا العملاء',
                'description': 'Average customer satisfaction rating',
                'description_ar': 'متوسط تقييم رضا العملاء',
                'category': 'CUSTOMER',
                'measurement_type': 'SCORE',
                'unit': '/5',
                'unit_ar': '/5',
                'frequency': 'MONTHLY',
                'trend_direction': 'UP',
                'target_value': Decimal('4.50'),
                'warning_threshold': Decimal('4.00'),
                'critical_threshold': Decimal('3.50')
            }
        ]

        kpis = {}
        for kpi_data in kpis_data:
            category_name = kpi_data.pop('category')
            kpi_data['category'] = categories[category_name]
            kpi_data['created_by'] = creator
            kpi_data['status'] = 'ACTIVE'
            
            kpi, created = KPI.objects.get_or_create(
                name=kpi_data['name'],
                defaults=kpi_data
            )
            kpis[kpi.name] = kpi
            if created:
                self.stdout.write(f'Created KPI: {kpi.name}')

        # Create sample KPI values (last 6 months)
        self.stdout.write('Creating sample KPI values...')
        
        for kpi in kpis.values():
            for i in range(6):  # Last 6 months
                period_start = timezone.now() - timedelta(days=30 * (i + 1))
                period_end = period_start + timedelta(days=30)
                
                # Generate realistic values based on KPI type and target
                if kpi.target_value:
                    base_value = float(kpi.target_value)
                    # Add some variance around the target
                    variance = random.uniform(-0.3, 0.3)  # ±30% variance
                    value = base_value * (1 + variance)
                    
                    # Ensure positive values
                    value = max(value, 0)
                    
                    # Add trend (improving over time)
                    trend_factor = (6 - i) * 0.05  # 5% improvement per month
                    if kpi.trend_direction == 'UP':
                        value *= (1 + trend_factor)
                    elif kpi.trend_direction == 'DOWN':
                        value *= (1 - trend_factor)
                    
                    KPIValue.objects.create(
                        kpi=kpi,
                        value=Decimal(str(round(value, 2))),
                        period_start=period_start,
                        period_end=period_end,
                        recorded_by=creator,
                        data_quality_score=Decimal('95.00'),
                        confidence_level=Decimal('90.00')
                    )

        # Create sample targets
        self.stdout.write('Creating sample KPI targets...')
        
        for kpi in kpis.values():
            if kpi.target_value:
                # Current year target
                start_date = timezone.now().date().replace(month=1, day=1)
                end_date = start_date.replace(year=start_date.year + 1) - timedelta(days=1)
                
                KPITarget.objects.create(
                    kpi=kpi,
                    target_value=kpi.target_value,
                    target_type='ABSOLUTE',
                    start_date=start_date,
                    end_date=end_date,
                    description=f'Annual target for {kpi.name}',
                    created_by=creator
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created sample KPI data:\n'
                f'- {len(categories)} categories\n'
                f'- {len(kpis)} KPIs\n'
                f'- {KPIValue.objects.count()} KPI values\n'
                f'- {KPITarget.objects.count()} KPI targets'
            )
        )
