#!/usr/bin/env python3
"""
CRITICAL FIX: Final Production Validation Script
Comprehensive validation of all critical fixes before production deployment
"""

import os
import sys
import subprocess
import time
from pathlib import Path

# Add Django to path
sys.path.append(str(Path(__file__).parent.parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

import django
django.setup()

from django.db import connection
from django.core.cache import cache
from django.conf import settings

class ProductionValidator:
    def __init__(self):
        self.passed_tests = 0
        self.failed_tests = 0
        self.warnings = []
        
    def run_all_validations(self):
        """Run comprehensive production validation"""
        print("🚀 Starting Final Production Validation...")
        print("=" * 70)
        
        # Critical Fix Validations
        self.validate_database_migration()
        self.validate_api_endpoints()
        self.validate_database_indexes()
        self.validate_rate_limiting()
        self.validate_authentication_security()
        
        # Performance Validations
        self.validate_query_performance()
        self.validate_cache_performance()
        
        # Security Validations
        self.validate_security_settings()
        self.validate_cors_configuration()
        
        # System Validations
        self.validate_health_checks()
        self.validate_error_handling()
        
        self.print_final_summary()
        return self.failed_tests == 0
    
    def validate_database_migration(self):
        """Validate PostgreSQL migration success"""
        print("🗄️ Validating Database Migration...")
        
        try:
            # Check database vendor
            if connection.vendor != 'postgresql':
                self.fail_test("Database is not PostgreSQL")
                return
            
            # Check database connectivity
            with connection.cursor() as cursor:
                cursor.execute("SELECT version()")
                version = cursor.fetchone()[0]
                
                if "PostgreSQL" in version:
                    self.pass_test(f"PostgreSQL connected: {version.split()[1]}")
                else:
                    self.fail_test("Database version check failed")
            
            # Check for SQLite remnants
            sqlite_file = Path('db.sqlite3')
            if sqlite_file.exists():
                self.warnings.append("SQLite file still exists - consider removing after validation")
            
        except Exception as e:
            self.fail_test(f"Database validation failed: {e}")
    
    def validate_api_endpoints(self):
        """Validate API endpoint consolidation"""
        print("🌐 Validating API Endpoints...")
        
        try:
            from django.urls import reverse
            from django.test import Client
            
            client = Client()
            
            # Test main endpoints
            endpoints = [
                'health-check',
                'dashboard-stats',
                'token_obtain_pair'
            ]
            
            working_endpoints = 0
            for endpoint in endpoints:
                try:
                    url = reverse(endpoint)
                    response = client.get(url)
                    if response.status_code in [200, 401, 405]:  # 401/405 are acceptable for auth endpoints
                        working_endpoints += 1
                except Exception as e:
                    self.warnings.append(f"Endpoint {endpoint} issue: {e}")
            
            if working_endpoints >= len(endpoints) - 1:  # Allow one endpoint to fail
                self.pass_test(f"API endpoints accessible: {working_endpoints}/{len(endpoints)}")
            else:
                self.fail_test(f"Too many API endpoints failing: {working_endpoints}/{len(endpoints)}")
                
        except Exception as e:
            self.fail_test(f"API endpoint validation failed: {e}")
    
    def validate_database_indexes(self):
        """Validate database indexes are created"""
        print("📊 Validating Database Indexes...")
        
        try:
            with connection.cursor() as cursor:
                # Check for custom indexes
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM pg_indexes 
                    WHERE schemaname = 'public' 
                    AND indexname LIKE 'idx_%'
                """)
                
                index_count = cursor.fetchone()[0]
                
                if index_count >= 10:  # Expect at least 10 custom indexes
                    self.pass_test(f"Database indexes created: {index_count} custom indexes")
                else:
                    self.fail_test(f"Insufficient database indexes: {index_count} found")
                
        except Exception as e:
            self.fail_test(f"Database index validation failed: {e}")
    
    def validate_rate_limiting(self):
        """Validate rate limiting is working"""
        print("🛡️ Validating Rate Limiting...")
        
        try:
            # Check if rate limiting is enabled
            rate_limit_enabled = getattr(settings, 'RATELIMIT_ENABLE', False)
            
            if not rate_limit_enabled:
                self.fail_test("Rate limiting is disabled")
                return
            
            # Test cache connectivity (required for rate limiting)
            cache.set('test_key', 'test_value', 10)
            if cache.get('test_key') == 'test_value':
                cache.delete('test_key')
                self.pass_test("Rate limiting cache working")
            else:
                self.fail_test("Rate limiting cache not working")
                
        except Exception as e:
            self.fail_test(f"Rate limiting validation failed: {e}")
    
    def validate_authentication_security(self):
        """Validate authentication security improvements"""
        print("🔐 Validating Authentication Security...")
        
        try:
            # Check JWT settings
            jwt_settings = getattr(settings, 'SIMPLE_JWT', {})
            
            # Check for httpOnly cookie authentication
            auth_classes = getattr(settings, 'REST_FRAMEWORK', {}).get('DEFAULT_AUTHENTICATION_CLASSES', [])
            
            has_custom_auth = any('CookieJWT' in auth_class for auth_class in auth_classes)
            
            if has_custom_auth:
                self.pass_test("Custom JWT authentication configured")
            else:
                self.warnings.append("Custom JWT authentication not found")
            
            # Check token expiry settings
            access_token_lifetime = jwt_settings.get('ACCESS_TOKEN_LIFETIME')
            if access_token_lifetime:
                self.pass_test(f"JWT token lifetime configured: {access_token_lifetime}")
            else:
                self.warnings.append("JWT token lifetime not configured")
                
        except Exception as e:
            self.fail_test(f"Authentication validation failed: {e}")
    
    def validate_query_performance(self):
        """Validate query performance improvements"""
        print("⚡ Validating Query Performance...")
        
        try:
            from django.test.utils import override_settings
            from ems.models import Employee
            
            # Test query optimization
            with override_settings(DEBUG=True):
                from django.db import reset_queries
                reset_queries()
                
                # This should use select_related and not cause N+1 queries
                employees = list(Employee.objects.select_related('user', 'department')[:10])
                
                query_count = len(connection.queries)
                
                if query_count <= 3:  # Should be 1-3 queries max
                    self.pass_test(f"Query optimization working: {query_count} queries for 10 employees")
                else:
                    self.fail_test(f"Query optimization needed: {query_count} queries for 10 employees")
                    
        except Exception as e:
            self.fail_test(f"Query performance validation failed: {e}")
    
    def validate_cache_performance(self):
        """Validate cache performance"""
        print("🚀 Validating Cache Performance...")
        
        try:
            start_time = time.time()
            
            # Test cache operations
            cache.set('perf_test', 'test_data', 60)
            result = cache.get('perf_test')
            cache.delete('perf_test')
            
            cache_time = (time.time() - start_time) * 1000
            
            if result == 'test_data' and cache_time < 50:  # Should be under 50ms
                self.pass_test(f"Cache performance good: {cache_time:.2f}ms")
            else:
                self.fail_test(f"Cache performance poor: {cache_time:.2f}ms")
                
        except Exception as e:
            self.fail_test(f"Cache performance validation failed: {e}")
    
    def validate_security_settings(self):
        """Validate security settings"""
        print("🔒 Validating Security Settings...")
        
        security_checks = [
            ('DEBUG', False, "Debug mode should be disabled in production"),
            ('SECURE_SSL_REDIRECT', True, "SSL redirect should be enabled"),
            ('SECURE_HSTS_SECONDS', lambda x: x > 0, "HSTS should be configured"),
            ('SESSION_COOKIE_SECURE', True, "Secure session cookies should be enabled"),
            ('CSRF_COOKIE_SECURE', True, "Secure CSRF cookies should be enabled")
        ]
        
        passed_checks = 0
        for setting_name, expected, description in security_checks:
            value = getattr(settings, setting_name, None)
            
            if callable(expected):
                check_passed = expected(value) if value is not None else False
            else:
                check_passed = value == expected
            
            if check_passed:
                passed_checks += 1
            else:
                self.warnings.append(f"{description} (Current: {value})")
        
        if passed_checks >= len(security_checks) - 2:  # Allow 2 warnings
            self.pass_test(f"Security settings mostly configured: {passed_checks}/{len(security_checks)}")
        else:
            self.fail_test(f"Security settings need attention: {passed_checks}/{len(security_checks)}")
    
    def validate_cors_configuration(self):
        """Validate CORS configuration"""
        print("🌐 Validating CORS Configuration...")
        
        try:
            cors_origins = getattr(settings, 'CORS_ALLOWED_ORIGINS', [])
            
            if len(cors_origins) > 20:
                self.warnings.append(f"Too many CORS origins configured: {len(cors_origins)}")
            elif len(cors_origins) > 0:
                self.pass_test(f"CORS configured with {len(cors_origins)} origins")
            else:
                self.fail_test("No CORS origins configured")
                
        except Exception as e:
            self.fail_test(f"CORS validation failed: {e}")
    
    def validate_health_checks(self):
        """Validate health check endpoints"""
        print("🏥 Validating Health Checks...")
        
        try:
            from django.test import Client
            
            client = Client()
            response = client.get('/api/health/')
            
            if response.status_code == 200:
                self.pass_test("Health check endpoint working")
            else:
                self.fail_test(f"Health check endpoint failed: {response.status_code}")
                
        except Exception as e:
            self.fail_test(f"Health check validation failed: {e}")
    
    def validate_error_handling(self):
        """Validate error handling improvements"""
        print("🚨 Validating Error Handling...")
        
        try:
            from django.test import Client
            
            client = Client()
            
            # Test 404 handling
            response = client.get('/api/nonexistent-endpoint/')
            if response.status_code == 404:
                self.pass_test("404 error handling working")
            else:
                self.warnings.append(f"Unexpected response for 404: {response.status_code}")
            
            # Test method not allowed
            response = client.post('/api/health/')
            if response.status_code in [405, 200]:  # 405 or 200 are both acceptable
                self.pass_test("Method not allowed handling working")
            else:
                self.warnings.append(f"Unexpected response for method not allowed: {response.status_code}")
                
        except Exception as e:
            self.fail_test(f"Error handling validation failed: {e}")
    
    def pass_test(self, message):
        """Record a passed test"""
        self.passed_tests += 1
        print(f"  ✅ {message}")
    
    def fail_test(self, message):
        """Record a failed test"""
        self.failed_tests += 1
        print(f"  ❌ {message}")
    
    def print_final_summary(self):
        """Print final validation summary"""
        print("\n" + "=" * 70)
        print("📋 FINAL VALIDATION SUMMARY")
        print("=" * 70)
        
        total_tests = self.passed_tests + self.failed_tests
        success_rate = (self.passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n🎯 Test Results:")
        print(f"  • Total tests: {total_tests}")
        print(f"  • Passed: {self.passed_tests}")
        print(f"  • Failed: {self.failed_tests}")
        print(f"  • Success rate: {success_rate:.1f}%")
        
        if self.warnings:
            print(f"\n⚠️ Warnings ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"  • {warning}")
        
        if self.failed_tests == 0:
            print(f"\n🎉 ALL VALIDATIONS PASSED!")
            print(f"✅ System is ready for production deployment")
        else:
            print(f"\n❌ {self.failed_tests} VALIDATION(S) FAILED")
            print(f"🚫 Fix failed validations before production deployment")

if __name__ == '__main__':
    validator = ProductionValidator()
    success = validator.run_all_validations()
    sys.exit(0 if success else 1)
