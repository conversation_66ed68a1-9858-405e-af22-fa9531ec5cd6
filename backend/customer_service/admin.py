from django.contrib import admin
from .models import (
    SupportTicketCategory, SupportAgent, ServiceLevelAgreement,
    SupportTicket, TicketComment, KnowledgeBaseCategory,
    KnowledgeBaseArticle, CustomerFeedback, LiveChatSession, ChatMessage
)
from .models_advanced import (
    TicketRoutingRule, EscalationRule, AIAssistant, AIInteraction,
    WorkflowAutomation, WorkflowExecution, IntegrationConnector,
    CustomerTier, CustomerProfile
)

@admin.register(SupportTicketCategory)
class SupportTicketCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'name_ar', 'color', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'name_ar', 'description']

@admin.register(SupportAgent)
class SupportAgentAdmin(admin.ModelAdmin):
    list_display = ['user', 'agent_id', 'is_available', 'performance_rating', 'total_tickets_resolved']
    list_filter = ['is_available', 'created_at']
    search_fields = ['user__username', 'user__first_name', 'user__last_name', 'agent_id']

@admin.register(ServiceLevelAgreement)
class ServiceLevelAgreementAdmin(admin.ModelAdmin):
    list_display = ['name', 'priority', 'response_time_hours', 'resolution_time_hours', 'is_active']
    list_filter = ['priority', 'is_active']
    search_fields = ['name', 'name_ar']

@admin.register(SupportTicket)
class SupportTicketAdmin(admin.ModelAdmin):
    list_display = ['ticket_id', 'title', 'customer', 'priority', 'status', 'assigned_agent', 'created_at']
    list_filter = ['status', 'priority', 'source', 'category', 'created_at']
    search_fields = ['ticket_id', 'title', 'customer__username', 'customer_email']
    readonly_fields = ['ticket_id', 'created_at', 'updated_at']

@admin.register(TicketComment)
class TicketCommentAdmin(admin.ModelAdmin):
    list_display = ['ticket', 'author', 'comment_type', 'is_public', 'created_at']
    list_filter = ['comment_type', 'is_public', 'created_at']
    search_fields = ['ticket__ticket_id', 'author__username', 'content']

@admin.register(KnowledgeBaseCategory)
class KnowledgeBaseCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'name_ar', 'parent', 'sort_order', 'is_active']
    list_filter = ['is_active', 'parent']
    search_fields = ['name', 'name_ar', 'description']

@admin.register(KnowledgeBaseArticle)
class KnowledgeBaseArticleAdmin(admin.ModelAdmin):
    list_display = ['title', 'category', 'article_type', 'author', 'is_published', 'view_count', 'created_at']
    list_filter = ['article_type', 'is_published', 'is_featured', 'category', 'created_at']
    search_fields = ['title', 'title_ar', 'content', 'tags']
    readonly_fields = ['slug', 'view_count', 'helpful_votes', 'not_helpful_votes']

@admin.register(CustomerFeedback)
class CustomerFeedbackAdmin(admin.ModelAdmin):
    list_display = ['customer', 'feedback_type', 'overall_rating', 'related_ticket', 'created_at']
    list_filter = ['feedback_type', 'overall_rating', 'created_at']
    search_fields = ['customer__username', 'comments', 'suggestions']

@admin.register(LiveChatSession)
class LiveChatSessionAdmin(admin.ModelAdmin):
    list_display = ['session_id', 'customer', 'agent', 'status', 'started_at', 'ended_at']
    list_filter = ['status', 'started_at']
    search_fields = ['session_id', 'customer__username', 'subject']

@admin.register(ChatMessage)
class ChatMessageAdmin(admin.ModelAdmin):
    list_display = ['session', 'sender', 'message_type', 'is_read', 'created_at']
    list_filter = ['message_type', 'is_read', 'created_at']
    search_fields = ['session__session_id', 'sender__username', 'content']

# Advanced Models Admin
@admin.register(TicketRoutingRule)
class TicketRoutingRuleAdmin(admin.ModelAdmin):
    list_display = ['name', 'condition_type', 'action_type', 'priority', 'is_active', 'created_at']
    list_filter = ['condition_type', 'action_type', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(EscalationRule)
class EscalationRuleAdmin(admin.ModelAdmin):
    list_display = ['name', 'trigger_type', 'trigger_after_hours', 'escalate_to_agent', 'is_active']
    list_filter = ['trigger_type', 'is_active', 'escalate_to_manager']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(AIAssistant)
class AIAssistantAdmin(admin.ModelAdmin):
    list_display = ['name', 'assistant_type', 'ai_provider', 'is_active', 'total_interactions', 'success_rate']
    list_filter = ['assistant_type', 'ai_provider', 'is_active']
    search_fields = ['name', 'description']
    readonly_fields = ['total_interactions', 'successful_resolutions', 'average_satisfaction', 'created_at', 'updated_at']

@admin.register(AIInteraction)
class AIInteractionAdmin(admin.ModelAdmin):
    list_display = ['assistant', 'user', 'confidence_score', 'was_helpful', 'escalated_to_human', 'created_at']
    list_filter = ['assistant', 'was_helpful', 'escalated_to_human', 'created_at']
    search_fields = ['user_input', 'ai_response']
    readonly_fields = ['created_at']

@admin.register(WorkflowAutomation)
class WorkflowAutomationAdmin(admin.ModelAdmin):
    list_display = ['name', 'trigger_event', 'is_active', 'total_executions', 'success_rate']
    list_filter = ['trigger_event', 'is_active']
    search_fields = ['name', 'description']
    readonly_fields = ['total_executions', 'successful_executions', 'failed_executions', 'created_at', 'updated_at']

@admin.register(WorkflowExecution)
class WorkflowExecutionAdmin(admin.ModelAdmin):
    list_display = ['workflow', 'status', 'steps_completed', 'total_steps', 'started_at', 'completed_at']
    list_filter = ['status', 'workflow', 'started_at']
    readonly_fields = ['started_at', 'completed_at']

@admin.register(IntegrationConnector)
class IntegrationConnectorAdmin(admin.ModelAdmin):
    list_display = ['name', 'integration_type', 'is_active', 'last_sync_at', 'sync_success_rate']
    list_filter = ['integration_type', 'is_active', 'last_sync_status']
    search_fields = ['name', 'description']
    readonly_fields = ['last_sync_at', 'total_syncs', 'failed_syncs', 'created_at', 'updated_at']

@admin.register(CustomerTier)
class CustomerTierAdmin(admin.ModelAdmin):
    list_display = ['name', 'tier_level', 'response_time_hours', 'resolution_time_hours', 'auto_escalate']
    list_filter = ['tier_level', 'auto_escalate', 'dedicated_agent']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(CustomerProfile)
class CustomerProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'customer_tier', 'preferred_contact_method', 'total_tickets', 'average_satisfaction']
    list_filter = ['customer_tier', 'preferred_contact_method', 'preferred_language', 'receive_notifications']
    search_fields = ['user__username', 'user__email', 'internal_notes']
    readonly_fields = ['total_tickets', 'resolved_tickets', 'average_satisfaction', 'last_contact_date', 'created_at', 'updated_at']

# Import enhanced customer admin (this will override the default User admin)
try:
    from .admin_customer import *
except ImportError:
    pass
