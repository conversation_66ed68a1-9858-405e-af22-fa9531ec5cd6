# CRITICAL FIX: Production Docker Configuration
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ems_postgres
    environment:
      POSTGRES_DB: ${DB_NAME:-ems_production}
      POSTGRES_USER: ${DB_USER:-ems_user}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-ems_user} -d ${DB_NAME:-ems_production}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ems_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ems_redis
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ems_network

  # Django Backend
  backend:
    image: ${BACKEND_IMAGE:-ghcr.io/your-org/ems-backend:latest}
    container_name: ems_backend
    environment:
      - DEBUG=false
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=${DB_NAME:-ems_production}
      - DB_USER=${DB_USER:-ems_user}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-localhost,127.0.0.1}
      - RATELIMIT_ENABLE=true
      - SENTRY_DSN=${SENTRY_DSN}
    volumes:
      - static_files:/app/staticfiles
      - media_files:/app/media
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    networks:
      - ems_network

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.production
    container_name: ems_frontend
    environment:
      - VITE_API_BASE_URL=http://localhost:8000/api
      - VITE_WS_URL=ws://localhost:8000/ws
    volumes:
      - ./frontend:/app
      - frontend_build:/app/dist
    ports:
      - "3000:3000"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - ems_network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: ems_nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - static_files:/var/www/static
      - media_files:/var/www/media
      - frontend_build:/var/www/frontend
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - ems_network

volumes:
  postgres_data:
  redis_data:
  static_files:
  media_files:
  frontend_build:

networks:
  ems_network:
    driver: bridge
