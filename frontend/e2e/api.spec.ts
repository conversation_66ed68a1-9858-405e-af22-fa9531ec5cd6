import { test, expect } from '@playwright/test';

test.describe('API Integration Tests', () => {
  let authToken: string;

  test.beforeAll(async ({ request }) => {
    // Get authentication token
    const loginResponse = await request.post('http://127.0.0.1:8000/api/auth/login/', {
      data: {
        username: 'admin',
        password: 'admin123'
      }
    });

    expect(loginResponse.ok()).toBeTruthy();
    const loginData = await loginResponse.json();
    authToken = loginData.access;
    expect(authToken).toBeTruthy();
  });

  test('should authenticate and get user profile', async ({ request }) => {
    const response = await request.get('http://127.0.0.1:8000/api/auth/user/', {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    expect(response.ok()).toBeTruthy();
    const userData = await response.json();
    expect(userData).toHaveProperty('username');
    expect(userData.username).toBe('admin');
  });

  test('should get dashboard stats', async ({ request }) => {
    const response = await request.get('http://127.0.0.1:8000/api/dashboard-stats/', {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    expect(response.ok()).toBeTruthy();
    const stats = await response.json();
    
    // Check for expected dashboard statistics
    expect(stats).toHaveProperty('total_employees');
    expect(stats).toHaveProperty('total_departments');
    expect(typeof stats.total_employees).toBe('number');
    expect(typeof stats.total_departments).toBe('number');
  });

  test('should get departments list', async ({ request }) => {
    const response = await request.get('http://127.0.0.1:8000/api/departments/', {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    expect(response.ok()).toBeTruthy();
    const departments = await response.json();
    
    // Should return an array (even if empty)
    expect(Array.isArray(departments.results || departments)).toBeTruthy();
  });

  test('should get employees list', async ({ request }) => {
    const response = await request.get('http://127.0.0.1:8000/api/employees/', {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    expect(response.ok()).toBeTruthy();
    const employees = await response.json();
    
    // Should return an array (even if empty)
    expect(Array.isArray(employees.results || employees)).toBeTruthy();
  });

  test('should handle unauthorized requests', async ({ request }) => {
    const response = await request.get('http://127.0.0.1:8000/api/dashboard-stats/');

    expect(response.status()).toBe(401);
    const errorData = await response.json();
    expect(errorData).toHaveProperty('detail');
  });

  test('should handle invalid endpoints', async ({ request }) => {
    const response = await request.get('http://127.0.0.1:8000/api/nonexistent-endpoint/', {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    expect(response.status()).toBe(404);
  });

  test('should validate CORS headers', async ({ request }) => {
    const response = await request.options('http://127.0.0.1:8000/api/dashboard-stats/', {
      headers: {
        'Origin': 'http://localhost:5173',
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'authorization'
      }
    });

    // Should allow CORS for the frontend origin
    expect(response.headers()['access-control-allow-origin']).toBeTruthy();
  });

  test('should refresh authentication token', async ({ request }) => {
    // First get a refresh token
    const loginResponse = await request.post('http://127.0.0.1:8000/api/auth/login/', {
      data: {
        username: 'admin',
        password: 'admin123'
      }
    });

    const loginData = await loginResponse.json();
    const refreshToken = loginData.refresh;

    // Use refresh token to get new access token
    const refreshResponse = await request.post('http://127.0.0.1:8000/api/auth/refresh/', {
      data: {
        refresh: refreshToken
      }
    });

    expect(refreshResponse.ok()).toBeTruthy();
    const refreshData = await refreshResponse.json();
    expect(refreshData).toHaveProperty('access');
    expect(refreshData.access).toBeTruthy();
  });

  test('should handle API rate limiting gracefully', async ({ request }) => {
    // Make multiple rapid requests to test rate limiting
    const promises = Array.from({ length: 10 }, () =>
      request.get('http://127.0.0.1:8000/api/dashboard-stats/', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })
    );

    const responses = await Promise.all(promises);
    
    // All requests should either succeed or be rate limited (429)
    responses.forEach(response => {
      expect([200, 429]).toContain(response.status());
    });
  });

  test('should validate API response structure', async ({ request }) => {
    const response = await request.get('http://127.0.0.1:8000/api/departments/', {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    
    // Check for pagination structure if using DRF pagination
    if (data.results) {
      expect(data).toHaveProperty('count');
      expect(data).toHaveProperty('results');
      expect(Array.isArray(data.results)).toBeTruthy();
    } else {
      // Direct array response
      expect(Array.isArray(data)).toBeTruthy();
    }
  });
});
