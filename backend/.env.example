# Django Environment Configuration
# Copy this file to .env and update the values for your environment

# SECURITY WARNING: Generate a new secret key for production!
# You can generate one using: python -c 'from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())'
SECRET_KEY=your-super-secret-key-here

# Debug mode - NEVER set to True in production
DEBUG=False

# Allowed hosts - comma-separated list of domains
ALLOWED_HOSTS=localhost,127.0.0.1,yourdomain.com

# Database Configuration
DATABASE_URL=sqlite:///db.sqlite3
# For PostgreSQL: DATABASE_URL=postgresql://user:password@localhost:5432/dbname
# For MySQL: DATABASE_URL=mysql://user:password@localhost:3306/dbname

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Email Configuration (for password reset, notifications, etc.)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379/0

# File Storage Configuration
MEDIA_ROOT=/path/to/media/files
STATIC_ROOT=/path/to/static/files

# Security Settings
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_HSTS_PRELOAD=False
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
X_FRAME_OPTIONS=DENY

# Logging Level
LOG_LEVEL=INFO

# API Rate Limiting
RATE_LIMIT_ENABLED=True
RATE_LIMIT_PER_MINUTE=60

# JWT Token Settings
JWT_ACCESS_TOKEN_LIFETIME_MINUTES=60
JWT_REFRESH_TOKEN_LIFETIME_DAYS=7
