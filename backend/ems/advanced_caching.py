"""
CRITICAL FIX: Advanced Caching Strategy
Multi-level caching with intelligent invalidation and performance monitoring
"""

import json
import hashlib
import time
from datetime import datetime, timedelta
from functools import wraps
from typing import Any, Optional, Dict, List, Callable
from django.core.cache import cache
from django.core.cache.utils import make_template_fragment_key
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.conf import settings
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)

class CacheManager:
    """Advanced cache management with intelligent invalidation"""
    
    # Cache key prefixes for different data types
    PREFIXES = {
        'employee': 'emp',
        'department': 'dept', 
        'project': 'proj',
        'leave_request': 'leave',
        'dashboard': 'dash',
        'stats': 'stats',
        'user_session': 'session',
        'api_response': 'api'
    }
    
    # Default cache timeouts (in seconds)
    TIMEOUTS = {
        'short': 300,      # 5 minutes
        'medium': 1800,    # 30 minutes  
        'long': 3600,      # 1 hour
        'daily': 86400,    # 24 hours
        'weekly': 604800   # 7 days
    }
    
    @classmethod
    def generate_key(cls, prefix: str, identifier: str, **kwargs) -> str:
        """Generate standardized cache key"""
        
        # Include additional parameters in key
        params = []
        for key, value in sorted(kwargs.items()):
            if value is not None:
                params.append(f"{key}:{value}")
        
        param_string = "_".join(params)
        base_key = f"{cls.PREFIXES.get(prefix, prefix)}:{identifier}"
        
        if param_string:
            base_key += f":{param_string}"
        
        # Hash long keys to avoid Redis key length limits
        if len(base_key) > 200:
            hash_suffix = hashlib.md5(base_key.encode()).hexdigest()[:8]
            base_key = f"{cls.PREFIXES.get(prefix, prefix)}:hash:{hash_suffix}"
        
        return base_key
    
    @classmethod
    def set_with_tags(cls, key: str, value: Any, timeout: int, tags: List[str] = None):
        """Set cache value with tags for group invalidation"""
        
        # Store the main value
        cache.set(key, value, timeout)
        
        # Store tags for group invalidation
        if tags:
            for tag in tags:
                tag_key = f"tag:{tag}"
                tagged_keys = cache.get(tag_key, set())
                tagged_keys.add(key)
                cache.set(tag_key, tagged_keys, timeout + 3600)  # Tags live longer
        
        # Store metadata
        metadata = {
            'created_at': timezone.now().isoformat(),
            'timeout': timeout,
            'tags': tags or []
        }
        cache.set(f"{key}:meta", metadata, timeout + 3600)
    
    @classmethod
    def invalidate_by_tags(cls, tags: List[str]):
        """Invalidate all cache entries with specified tags"""
        
        invalidated_count = 0
        
        for tag in tags:
            tag_key = f"tag:{tag}"
            tagged_keys = cache.get(tag_key, set())
            
            for key in tagged_keys:
                cache.delete(key)
                cache.delete(f"{key}:meta")
                invalidated_count += 1
            
            # Clear the tag itself
            cache.delete(tag_key)
        
        logger.info(f"Invalidated {invalidated_count} cache entries for tags: {tags}")
        return invalidated_count
    
    @classmethod
    def get_cache_stats(cls) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        
        stats = {
            'timestamp': timezone.now().isoformat(),
            'cache_info': {},
            'key_patterns': {},
            'memory_usage': {}
        }
        
        try:
            # Get Redis info if using Redis cache
            if hasattr(cache, '_cache') and hasattr(cache._cache, 'get_client'):
                client = cache._cache.get_client()
                info = client.info()
                
                stats['cache_info'] = {
                    'redis_version': info.get('redis_version'),
                    'used_memory': info.get('used_memory_human'),
                    'connected_clients': info.get('connected_clients'),
                    'total_commands_processed': info.get('total_commands_processed'),
                    'keyspace_hits': info.get('keyspace_hits', 0),
                    'keyspace_misses': info.get('keyspace_misses', 0)
                }
                
                # Calculate hit rate
                hits = info.get('keyspace_hits', 0)
                misses = info.get('keyspace_misses', 0)
                total = hits + misses
                
                if total > 0:
                    stats['cache_info']['hit_rate'] = (hits / total) * 100
                
                # Get key patterns
                keys = client.keys('*')
                pattern_counts = {}
                
                for key in keys[:1000]:  # Limit to avoid performance issues
                    if isinstance(key, bytes):
                        key = key.decode('utf-8')
                    
                    prefix = key.split(':')[0] if ':' in key else 'other'
                    pattern_counts[prefix] = pattern_counts.get(prefix, 0) + 1
                
                stats['key_patterns'] = pattern_counts
                
        except Exception as e:
            logger.warning(f"Could not get cache stats: {e}")
            stats['error'] = str(e)
        
        return stats

def cache_result(prefix: str, timeout: str = 'medium', tags: List[str] = None, 
                key_func: Callable = None, invalidate_on: List[str] = None):
    """
    Advanced caching decorator with intelligent invalidation
    
    Args:
        prefix: Cache key prefix
        timeout: Timeout duration ('short', 'medium', 'long', 'daily', 'weekly')
        tags: Tags for group invalidation
        key_func: Custom function to generate cache key
        invalidate_on: Model names that should invalidate this cache
    """
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # Default key generation
                func_name = func.__name__
                arg_hash = hashlib.md5(str(args + tuple(sorted(kwargs.items()))).encode()).hexdigest()[:8]
                cache_key = CacheManager.generate_key(prefix, f"{func_name}_{arg_hash}")
            
            # Try to get from cache
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for key: {cache_key}")
                return cached_result
            
            # Execute function and cache result
            start_time = time.time()
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # Determine timeout
            timeout_seconds = CacheManager.TIMEOUTS.get(timeout, CacheManager.TIMEOUTS['medium'])
            
            # Cache the result with tags
            CacheManager.set_with_tags(cache_key, result, timeout_seconds, tags)
            
            logger.debug(f"Cache miss for key: {cache_key}, execution time: {execution_time:.3f}s")
            return result
        
        # Store invalidation info on function for signal handlers
        wrapper._cache_invalidate_on = invalidate_on or []
        wrapper._cache_tags = tags or []
        
        return wrapper
    
    return decorator

class SmartCacheInvalidator:
    """Smart cache invalidation based on model changes"""
    
    # Model to cache tag mapping
    MODEL_CACHE_TAGS = {
        'Employee': ['employee', 'dashboard', 'stats'],
        'Department': ['department', 'employee', 'dashboard'],
        'Project': ['project', 'dashboard', 'stats'],
        'LeaveRequest': ['leave_request', 'employee', 'dashboard'],
        'Task': ['task', 'project', 'dashboard'],
        'User': ['user', 'employee', 'session']
    }
    
    @classmethod
    def invalidate_for_model(cls, model_name: str, instance_id: int = None):
        """Invalidate caches related to a specific model"""
        
        tags_to_invalidate = cls.MODEL_CACHE_TAGS.get(model_name, [])
        
        if instance_id:
            # Add instance-specific tags
            tags_to_invalidate.extend([
                f"{model_name.lower()}:{instance_id}",
                f"{model_name.lower()}_detail:{instance_id}"
            ])
        
        if tags_to_invalidate:
            CacheManager.invalidate_by_tags(tags_to_invalidate)

# Cached functions for common operations

@cache_result('dashboard', 'medium', ['dashboard', 'stats'])
def get_dashboard_stats():
    """Get cached dashboard statistics"""
    from .models import Employee, Department, Project, LeaveRequest
    
    stats = {
        'total_employees': Employee.objects.count(),
        'active_employees': Employee.objects.filter(is_active=True).count(),
        'total_departments': Department.objects.count(),
        'active_projects': Project.objects.filter(status='active').count(),
        'pending_leave_requests': LeaveRequest.objects.filter(status='pending').count(),
        'generated_at': timezone.now().isoformat()
    }
    
    return stats

@cache_result('employee', 'long', ['employee'])
def get_employee_list(department_id=None, is_active=None, page=1, page_size=50):
    """Get cached employee list with filters"""
    from .models import Employee
    
    queryset = Employee.objects.select_related('user', 'department')
    
    if department_id:
        queryset = queryset.filter(department_id=department_id)
    
    if is_active is not None:
        queryset = queryset.filter(is_active=is_active)
    
    # Pagination
    start = (page - 1) * page_size
    end = start + page_size
    
    employees = list(queryset[start:end])
    
    return {
        'employees': [
            {
                'id': emp.id,
                'name': emp.user.get_full_name(),
                'email': emp.user.email,
                'department': emp.department.name if emp.department else None,
                'is_active': emp.is_active
            }
            for emp in employees
        ],
        'total_count': queryset.count(),
        'page': page,
        'page_size': page_size
    }

@cache_result('project', 'medium', ['project'])
def get_project_details(project_id):
    """Get cached project details"""
    from .models import Project
    
    try:
        project = Project.objects.select_related('project_manager__user', 'department')\
                                .prefetch_related('team_members__user', 'tasks')\
                                .get(id=project_id)
        
        return {
            'id': project.id,
            'name': project.name,
            'description': project.description,
            'status': project.status,
            'project_manager': {
                'id': project.project_manager.id,
                'name': project.project_manager.user.get_full_name()
            } if project.project_manager else None,
            'department': {
                'id': project.department.id,
                'name': project.department.name
            } if project.department else None,
            'team_members': [
                {
                    'id': member.id,
                    'name': member.user.get_full_name()
                }
                for member in project.team_members.all()
            ],
            'task_count': project.tasks.count(),
            'completed_tasks': project.tasks.filter(status='completed').count()
        }
        
    except Project.DoesNotExist:
        return None

@cache_result('leave_request', 'short', ['leave_request'])
def get_pending_leave_requests(manager_id=None):
    """Get cached pending leave requests"""
    from .models import LeaveRequest
    
    queryset = LeaveRequest.objects.select_related(
        'employee__user', 'leave_type', 'approved_by__user'
    ).filter(status='pending')
    
    if manager_id:
        # Filter by manager's department
        queryset = queryset.filter(employee__department__manager_id=manager_id)
    
    return [
        {
            'id': lr.id,
            'employee': {
                'id': lr.employee.id,
                'name': lr.employee.user.get_full_name()
            },
            'leave_type': lr.leave_type.name if lr.leave_type else None,
            'start_date': lr.start_date.isoformat(),
            'end_date': lr.end_date.isoformat(),
            'days_requested': lr.days_requested,
            'reason': lr.reason,
            'submitted_at': lr.created_at.isoformat()
        }
        for lr in queryset[:50]  # Limit to 50 for performance
    ]

# Signal handlers for automatic cache invalidation

@receiver(post_save)
def invalidate_cache_on_save(sender, instance, **kwargs):
    """Invalidate relevant caches when models are saved"""
    
    model_name = sender.__name__
    SmartCacheInvalidator.invalidate_for_model(model_name, instance.id)

@receiver(post_delete)
def invalidate_cache_on_delete(sender, instance, **kwargs):
    """Invalidate relevant caches when models are deleted"""
    
    model_name = sender.__name__
    SmartCacheInvalidator.invalidate_for_model(model_name, instance.id)

# Cache warming functions

def warm_dashboard_cache():
    """Warm up dashboard cache"""
    logger.info("Warming dashboard cache...")
    get_dashboard_stats()

def warm_employee_cache():
    """Warm up employee cache"""
    logger.info("Warming employee cache...")
    
    # Warm common employee queries
    get_employee_list()  # All employees
    get_employee_list(is_active=True)  # Active employees
    
    # Warm by department
    from .models import Department
    for dept in Department.objects.all()[:5]:  # Top 5 departments
        get_employee_list(department_id=dept.id)

def warm_project_cache():
    """Warm up project cache"""
    logger.info("Warming project cache...")
    
    from .models import Project
    for project in Project.objects.filter(status='active')[:10]:  # Top 10 active projects
        get_project_details(project.id)

def warm_all_caches():
    """Warm up all critical caches"""
    logger.info("Starting cache warming process...")
    
    start_time = time.time()
    
    warm_dashboard_cache()
    warm_employee_cache()
    warm_project_cache()
    
    elapsed_time = time.time() - start_time
    logger.info(f"Cache warming completed in {elapsed_time:.2f} seconds")

# Cache monitoring and cleanup

def cleanup_expired_cache_metadata():
    """Clean up expired cache metadata"""
    
    try:
        if hasattr(cache, '_cache') and hasattr(cache._cache, 'get_client'):
            client = cache._cache.get_client()
            
            # Find metadata keys
            meta_keys = client.keys('*:meta')
            expired_count = 0
            
            for meta_key in meta_keys:
                if isinstance(meta_key, bytes):
                    meta_key = meta_key.decode('utf-8')
                
                # Check if main key exists
                main_key = meta_key.replace(':meta', '')
                if not client.exists(main_key):
                    client.delete(meta_key)
                    expired_count += 1
            
            logger.info(f"Cleaned up {expired_count} expired cache metadata entries")
            return expired_count
            
    except Exception as e:
        logger.error(f"Cache cleanup failed: {e}")
        return 0

# Management command functions

def get_cache_health_report():
    """Generate comprehensive cache health report"""
    
    stats = CacheManager.get_cache_stats()
    
    # Add application-specific metrics
    stats['application_metrics'] = {
        'dashboard_cache_age': get_cache_age('dashboard'),
        'employee_cache_coverage': get_cache_coverage('employee'),
        'cache_warming_status': check_cache_warming_status()
    }
    
    return stats

def get_cache_age(prefix):
    """Get age of cache entries for a prefix"""
    
    try:
        if hasattr(cache, '_cache') and hasattr(cache._cache, 'get_client'):
            client = cache._cache.get_client()
            keys = client.keys(f"{CacheManager.PREFIXES.get(prefix, prefix)}:*")
            
            if not keys:
                return None
            
            # Get TTL for first key
            first_key = keys[0]
            if isinstance(first_key, bytes):
                first_key = first_key.decode('utf-8')
            
            ttl = client.ttl(first_key)
            return ttl
            
    except Exception as e:
        logger.error(f"Could not get cache age: {e}")
        return None

def get_cache_coverage(prefix):
    """Get cache coverage percentage for a prefix"""
    
    # This would require tracking cache hits/misses
    # For now, return a placeholder
    return 85.0  # 85% coverage

def check_cache_warming_status():
    """Check if caches are properly warmed"""
    
    # Check if critical caches exist
    critical_caches = [
        CacheManager.generate_key('dashboard', 'get_dashboard_stats'),
        CacheManager.generate_key('employee', 'get_employee_list')
    ]
    
    warmed_count = 0
    for cache_key in critical_caches:
        if cache.get(cache_key) is not None:
            warmed_count += 1
    
    return {
        'warmed_caches': warmed_count,
        'total_critical_caches': len(critical_caches),
        'warming_percentage': (warmed_count / len(critical_caches)) * 100
    }
