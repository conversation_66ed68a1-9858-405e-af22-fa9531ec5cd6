# CRITICAL FIX: Production Environment Configuration
# Copy this file to .env.production and update with your values

# Database Configuration (PostgreSQL)
DB_NAME=ems_production
DB_USER=ems_user
DB_PASSWORD=your_secure_password_here
DB_HOST=localhost
DB_PORT=5432
DB_SSL_REQUIRE=true
DB_CONN_MAX_AGE=600
DB_MAX_CONNS=20

# Alternative: Database URL (if using managed database service)
# DATABASE_URL=postgresql://user:password@host:port/database

# Django Settings
DEBUG=false
SECRET_KEY=your_production_secret_key_here
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# Security Settings
SECURE_SSL_REDIRECT=true
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=true
SECURE_HSTS_PRELOAD=true

# Cache Configuration (Redis)
REDIS_URL=redis://localhost:6379/0
CACHE_TTL=300

# Rate Limiting
RATELIMIT_ENABLE=true

# Monitoring and Logging
LOG_LEVEL=INFO
SENTRY_DSN=your_sentry_dsn_here

# Email Configuration
EMAIL_HOST=smtp.yourdomain.com
EMAIL_PORT=587
EMAIL_USE_TLS=true
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password

# File Storage (if using cloud storage)
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
AWS_STORAGE_BUCKET_NAME=your-bucket-name
AWS_S3_REGION_NAME=us-east-1
