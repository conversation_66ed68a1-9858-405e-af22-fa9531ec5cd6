/**
 * UX FIX: Comprehensive Loading States Hook
 * Manages multiple loading states with better user feedback
 */

import { useState, useCallback, useRef, useEffect } from 'react'
import { useUXFeedback } from '@/utils/uxFeedbackManager'

export interface LoadingStateOptions {
  language?: 'ar' | 'en'
  showFeedback?: boolean
  expectedDuration?: number
  autoTimeout?: number
  onTimeout?: () => void
}

export interface LoadingState {
  isLoading: boolean
  error: string | null
  startTime: number | null
  duration: number
  progress?: number
}

export interface LoadingStates {
  [key: string]: LoadingState
}

export function useLoadingStates(defaultLanguage: 'ar' | 'en' = 'en') {
  const [loadingStates, setLoadingStates] = useState<LoadingStates>({})
  const timeoutsRef = useRef<Map<string, NodeJS.Timeout>>(new Map())
  const intervalsRef = useRef<Map<string, NodeJS.Timeout>>(new Map())
  const uxFeedback = useUXFeedback(defaultLanguage)

  // UX FIX: Start loading with enhanced feedback
  const startLoading = useCallback((
    key: string, 
    message?: string, 
    options: LoadingStateOptions = {}
  ) => {
    const {
      language = defaultLanguage,
      showFeedback = true,
      expectedDuration,
      autoTimeout,
      onTimeout
    } = options

    const startTime = Date.now()

    // Clear any existing timeout for this key
    const existingTimeout = timeoutsRef.current.get(key)
    if (existingTimeout) {
      clearTimeout(existingTimeout)
    }

    const existingInterval = intervalsRef.current.get(key)
    if (existingInterval) {
      clearInterval(existingInterval)
    }

    // Update loading state
    setLoadingStates(prev => ({
      ...prev,
      [key]: {
        isLoading: true,
        error: null,
        startTime,
        duration: 0,
        progress: 0
      }
    }))

    // Show feedback if enabled
    let feedbackId: string | undefined
    if (showFeedback && message) {
      feedbackId = uxFeedback.loading(message, { 
        expectedDuration,
        showProgress: !!expectedDuration 
      })
    }

    // Set up progress tracking if expected duration is provided
    if (expectedDuration) {
      const interval = setInterval(() => {
        const elapsed = Date.now() - startTime
        const progress = Math.min((elapsed / expectedDuration) * 100, 95)
        
        setLoadingStates(prev => ({
          ...prev,
          [key]: {
            ...prev[key],
            duration: elapsed,
            progress
          }
        }))

        if (progress >= 95) {
          clearInterval(interval)
          intervalsRef.current.delete(key)
        }
      }, 100)

      intervalsRef.current.set(key, interval)
    }

    // Set up auto timeout if specified
    if (autoTimeout) {
      const timeout = setTimeout(() => {
        stopLoading(key, 'timeout', 
          language === 'ar' ? 'انتهت مهلة العملية' : 'Operation timed out'
        )
        onTimeout?.()
      }, autoTimeout)

      timeoutsRef.current.set(key, timeout)
    }

    return feedbackId
  }, [defaultLanguage, uxFeedback])

  // UX FIX: Stop loading with result feedback
  const stopLoading = useCallback((
    key: string, 
    result?: 'success' | 'error' | 'timeout',
    message?: string
  ) => {
    // Clear timeouts and intervals
    const timeout = timeoutsRef.current.get(key)
    if (timeout) {
      clearTimeout(timeout)
      timeoutsRef.current.delete(key)
    }

    const interval = intervalsRef.current.get(key)
    if (interval) {
      clearInterval(interval)
      intervalsRef.current.delete(key)
    }

    // Update loading state
    setLoadingStates(prev => {
      const currentState = prev[key]
      if (!currentState) return prev

      const duration = currentState.startTime ? Date.now() - currentState.startTime : 0

      return {
        ...prev,
        [key]: {
          ...currentState,
          isLoading: false,
          duration,
          progress: 100,
          error: result === 'error' || result === 'timeout' ? message || 'Error occurred' : null
        }
      }
    })

    // Show result feedback
    if (result && message) {
      if (result === 'success') {
        uxFeedback.success(message)
      } else if (result === 'error') {
        uxFeedback.error(message)
      } else if (result === 'timeout') {
        uxFeedback.warning(message)
      }
    }
  }, [uxFeedback])

  // UX FIX: Set loading error
  const setLoadingError = useCallback((key: string, error: string) => {
    stopLoading(key, 'error', error)
  }, [stopLoading])

  // UX FIX: Update loading progress manually
  const updateProgress = useCallback((key: string, progress: number) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        progress: Math.min(Math.max(progress, 0), 100)
      }
    }))
  }, [])

  // UX FIX: Check if any loading state is active
  const isAnyLoading = useCallback(() => {
    return Object.values(loadingStates).some(state => state.isLoading)
  }, [loadingStates])

  // UX FIX: Get loading state for a specific key
  const getLoadingState = useCallback((key: string): LoadingState | null => {
    return loadingStates[key] || null
  }, [loadingStates])

  // UX FIX: Get all active loading states
  const getActiveLoadingStates = useCallback(() => {
    return Object.entries(loadingStates)
      .filter(([, state]) => state.isLoading)
      .reduce((acc, [key, state]) => ({ ...acc, [key]: state }), {})
  }, [loadingStates])

  // UX FIX: Clear all loading states
  const clearAllLoading = useCallback(() => {
    // Clear all timeouts and intervals
    timeoutsRef.current.forEach(timeout => clearTimeout(timeout))
    intervalsRef.current.forEach(interval => clearInterval(interval))
    timeoutsRef.current.clear()
    intervalsRef.current.clear()

    setLoadingStates({})
    uxFeedback.clearAll()
  }, [uxFeedback])

  // UX FIX: Batch loading operations
  const batchLoading = useCallback((operations: Array<{
    key: string
    message?: string
    options?: LoadingStateOptions
  }>) => {
    const feedbackIds: string[] = []
    
    operations.forEach(({ key, message, options }) => {
      const feedbackId = startLoading(key, message, options)
      if (feedbackId) {
        feedbackIds.push(feedbackId)
      }
    })

    return feedbackIds
  }, [startLoading])

  // UX FIX: Sequential loading with progress
  const sequentialLoading = useCallback(async (
    operations: Array<{
      key: string
      operation: () => Promise<any>
      message?: string
      successMessage?: string
      errorMessage?: string
    }>
  ) => {
    const results: any[] = []
    const totalOperations = operations.length

    for (let i = 0; i < operations.length; i++) {
      const { key, operation, message, successMessage, errorMessage } = operations[i]
      const progress = (i / totalOperations) * 100

      try {
        startLoading(key, message, { 
          expectedDuration: 2000,
          showFeedback: true 
        })
        
        updateProgress(key, progress)
        
        const result = await operation()
        results.push(result)
        
        stopLoading(key, 'success', successMessage)
      } catch (error) {
        stopLoading(key, 'error', errorMessage || 'Operation failed')
        throw error
      }
    }

    return results
  }, [startLoading, stopLoading, updateProgress])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      timeoutsRef.current.forEach(timeout => clearTimeout(timeout))
      intervalsRef.current.forEach(interval => clearInterval(interval))
    }
  }, [])

  return {
    // State
    loadingStates,
    
    // Actions
    startLoading,
    stopLoading,
    setLoadingError,
    updateProgress,
    clearAllLoading,
    
    // Batch operations
    batchLoading,
    sequentialLoading,
    
    // Queries
    isAnyLoading,
    getLoadingState,
    getActiveLoadingStates,
    
    // Utilities
    isLoading: (key: string) => loadingStates[key]?.isLoading || false,
    hasError: (key: string) => !!loadingStates[key]?.error,
    getError: (key: string) => loadingStates[key]?.error || null,
    getProgress: (key: string) => loadingStates[key]?.progress || 0,
    getDuration: (key: string) => loadingStates[key]?.duration || 0
  }
}

// UX FIX: Specialized hooks for common use cases
export function useFormLoadingStates(language: 'ar' | 'en' = 'en') {
  const loadingStates = useLoadingStates(language)

  return {
    ...loadingStates,
    
    // Form-specific methods
    startSubmitting: (message?: string) => 
      loadingStates.startLoading('submit', message || (language === 'ar' ? 'جاري الحفظ...' : 'Saving...')),
    
    stopSubmitting: (success: boolean, message?: string) =>
      loadingStates.stopLoading('submit', success ? 'success' : 'error', message),
    
    startValidating: () =>
      loadingStates.startLoading('validate', language === 'ar' ? 'جاري التحقق...' : 'Validating...', { showFeedback: false }),
    
    stopValidating: () =>
      loadingStates.stopLoading('validate'),
    
    isSubmitting: () => loadingStates.isLoading('submit'),
    isValidating: () => loadingStates.isLoading('validate')
  }
}

export function useDataLoadingStates(language: 'ar' | 'en' = 'en') {
  const loadingStates = useLoadingStates(language)

  return {
    ...loadingStates,
    
    // Data-specific methods
    startFetching: (dataType: string) =>
      loadingStates.startLoading('fetch', 
        language === 'ar' ? `جاري تحميل ${dataType}...` : `Loading ${dataType}...`),
    
    stopFetching: (success: boolean, message?: string) =>
      loadingStates.stopLoading('fetch', success ? 'success' : 'error', message),
    
    startRefreshing: () =>
      loadingStates.startLoading('refresh', language === 'ar' ? 'جاري التحديث...' : 'Refreshing...'),
    
    stopRefreshing: (success: boolean) =>
      loadingStates.stopLoading('refresh', success ? 'success' : 'error'),
    
    isFetching: () => loadingStates.isLoading('fetch'),
    isRefreshing: () => loadingStates.isLoading('refresh')
  }
}

export default useLoadingStates
