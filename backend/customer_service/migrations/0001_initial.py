# Generated by Django 4.2.7 on 2025-06-03 17:14

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ServiceLevelAgreement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('name_ar', models.CharField(max_length=100)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], max_length=20)),
                ('response_time_hours', models.IntegerField()),
                ('resolution_time_hours', models.IntegerField()),
                ('escalation_time_hours', models.IntegerField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='SupportAgent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('agent_id', models.CharField(max_length=20, unique=True)),
                ('specializations', models.JSONField(default=list)),
                ('max_concurrent_tickets', models.IntegerField(default=10)),
                ('is_available', models.BooleanField(default=True)),
                ('performance_rating', models.FloatField(default=5.0, validators=[django.core.validators.MinValueValidator(1.0), django.core.validators.MaxValueValidator(5.0)])),
                ('total_tickets_resolved', models.IntegerField(default=0)),
                ('average_resolution_time', models.DurationField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='SupportTicket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ticket_id', models.CharField(max_length=20, unique=True)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('customer_email', models.EmailField(max_length=254)),
                ('customer_phone', models.CharField(blank=True, max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium', max_length=20)),
                ('source', models.CharField(choices=[('email', 'Email'), ('phone', 'Phone'), ('chat', 'Live Chat'), ('web', 'Web Form'), ('mobile', 'Mobile App'), ('social', 'Social Media')], default='web', max_length=20)),
                ('status', models.CharField(choices=[('open', 'Open'), ('in_progress', 'In Progress'), ('pending_customer', 'Pending Customer'), ('resolved', 'Resolved'), ('closed', 'Closed'), ('cancelled', 'Cancelled')], default='open', max_length=20)),
                ('tags', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_response_at', models.DateTimeField(blank=True, null=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('closed_at', models.DateTimeField(blank=True, null=True)),
                ('sla_response_due', models.DateTimeField(blank=True, null=True)),
                ('sla_resolution_due', models.DateTimeField(blank=True, null=True)),
                ('sla_escalation_due', models.DateTimeField(blank=True, null=True)),
                ('is_sla_breached', models.BooleanField(default=False)),
                ('customer_satisfaction_rating', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('resolution_time', models.DurationField(blank=True, null=True)),
                ('assigned_agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='customer_service.supportagent')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SupportTicketCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('name_ar', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('color', models.CharField(default='#3B82F6', max_length=7)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Support Ticket Categories',
            },
        ),
        migrations.CreateModel(
            name='TicketComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('comment_type', models.CharField(choices=[('customer', 'Customer Reply'), ('agent', 'Agent Reply'), ('internal', 'Internal Note'), ('system', 'System Update')], max_length=20)),
                ('content', models.TextField()),
                ('is_public', models.BooleanField(default=True)),
                ('attachments', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('ticket', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='customer_service.supportticket')),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
        migrations.AddField(
            model_name='supportticket',
            name='category',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='customer_service.supportticketcategory'),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='support_tickets', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='sla',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='customer_service.servicelevelagreement'),
        ),
        migrations.CreateModel(
            name='LiveChatSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(max_length=50, unique=True)),
                ('status', models.CharField(choices=[('waiting', 'Waiting for Agent'), ('active', 'Active Chat'), ('ended', 'Chat Ended'), ('transferred', 'Transferred')], default='waiting', max_length=20)),
                ('subject', models.CharField(blank=True, max_length=200)),
                ('customer_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('referrer_url', models.URLField(blank=True)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('agent_joined_at', models.DateTimeField(blank=True, null=True)),
                ('ended_at', models.DateTimeField(blank=True, null=True)),
                ('wait_time', models.DurationField(blank=True, null=True)),
                ('chat_duration', models.DurationField(blank=True, null=True)),
                ('customer_satisfaction', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='customer_service.supportagent')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chat_sessions', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='KnowledgeBaseCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('name_ar', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('icon', models.CharField(default='HelpCircle', max_length=50)),
                ('color', models.CharField(default='#3B82F6', max_length=7)),
                ('sort_order', models.IntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customer_service.knowledgebasecategory')),
            ],
            options={
                'verbose_name_plural': 'Knowledge Base Categories',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='KnowledgeBaseArticle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('content_ar', models.TextField()),
                ('summary', models.TextField(blank=True)),
                ('summary_ar', models.TextField(blank=True)),
                ('article_type', models.CharField(choices=[('article', 'Article'), ('faq', 'FAQ'), ('tutorial', 'Tutorial'), ('troubleshooting', 'Troubleshooting'), ('policy', 'Policy')], default='article', max_length=20)),
                ('tags', models.JSONField(default=list)),
                ('is_published', models.BooleanField(default=False)),
                ('is_featured', models.BooleanField(default=False)),
                ('slug', models.SlugField(max_length=250, unique=True)),
                ('meta_description', models.CharField(blank=True, max_length=160)),
                ('view_count', models.IntegerField(default=0)),
                ('helpful_votes', models.IntegerField(default=0)),
                ('not_helpful_votes', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('published_at', models.DateTimeField(blank=True, null=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer_service.knowledgebasecategory')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CustomerFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('feedback_type', models.CharField(choices=[('ticket_rating', 'Ticket Rating'), ('service_survey', 'Service Survey'), ('product_feedback', 'Product Feedback'), ('general_feedback', 'General Feedback')], max_length=20)),
                ('overall_rating', models.IntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('response_time_rating', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('solution_quality_rating', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('agent_professionalism_rating', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('comments', models.TextField(blank=True)),
                ('suggestions', models.TextField(blank=True)),
                ('is_anonymous', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('related_ticket', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customer_service.supportticket')),
            ],
        ),
        migrations.CreateModel(
            name='ChatMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_type', models.CharField(choices=[('customer', 'Customer Message'), ('agent', 'Agent Message'), ('system', 'System Message'), ('file', 'File Attachment')], max_length=20)),
                ('content', models.TextField()),
                ('attachments', models.JSONField(default=list)),
                ('is_read', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='customer_service.livechatsession')),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
    ]
