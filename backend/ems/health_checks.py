"""
CRITICAL FIX: Comprehensive Health Check System
Production-ready health monitoring for all critical components
"""

import time
import psutil
from django.http import JsonResponse
from django.db import connection
from django.core.cache import cache
from django.conf import settings
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import logging

logger = logging.getLogger(__name__)

@csrf_exempt
@require_http_methods(["GET"])
def health_check(request):
    """Comprehensive health check endpoint"""
    
    start_time = time.time()
    health_status = {
        "status": "healthy",
        "timestamp": time.time(),
        "checks": {},
        "system": {},
        "performance": {}
    }
    
    try:
        # Database health check
        db_health = check_database_health()
        health_status["checks"]["database"] = db_health
        
        # Cache health check
        cache_health = check_cache_health()
        health_status["checks"]["cache"] = cache_health
        
        # System resource check
        system_health = check_system_resources()
        health_status["system"] = system_health
        
        # Performance metrics
        performance_metrics = get_performance_metrics()
        health_status["performance"] = performance_metrics
        
        # Rate limiting health
        rate_limit_health = check_rate_limiting_health()
        health_status["checks"]["rate_limiting"] = rate_limit_health
        
        # Determine overall status
        failed_checks = [name for name, check in health_status["checks"].items() 
                        if not check.get("healthy", False)]
        
        if failed_checks:
            health_status["status"] = "unhealthy"
            health_status["failed_checks"] = failed_checks
        
        # Add response time
        health_status["response_time_ms"] = (time.time() - start_time) * 1000
        
        # HTTP status code based on health
        status_code = 200 if health_status["status"] == "healthy" else 503
        
        return JsonResponse(health_status, status=status_code)
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JsonResponse({
            "status": "error",
            "error": str(e),
            "timestamp": time.time()
        }, status=500)

def check_database_health():
    """Check database connectivity and performance"""
    
    try:
        start_time = time.time()
        
        # Test basic connectivity
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
        
        query_time = (time.time() - start_time) * 1000
        
        # Get connection info
        db_info = {
            "healthy": True,
            "query_time_ms": query_time,
            "vendor": connection.vendor,
            "queries_executed": len(connection.queries) if settings.DEBUG else "N/A"
        }
        
        # Check for slow queries
        if query_time > 100:
            db_info["warning"] = "Slow database response"
        
        return db_info
        
    except Exception as e:
        return {
            "healthy": False,
            "error": str(e)
        }

def check_cache_health():
    """Check cache connectivity and performance"""
    
    try:
        start_time = time.time()
        
        # Test cache operations
        test_key = "health_check_test"
        test_value = "test_value"
        
        cache.set(test_key, test_value, 10)
        retrieved_value = cache.get(test_key)
        cache.delete(test_key)
        
        cache_time = (time.time() - start_time) * 1000
        
        if retrieved_value == test_value:
            return {
                "healthy": True,
                "cache_time_ms": cache_time,
                "backend": settings.CACHES['default']['BACKEND']
            }
        else:
            return {
                "healthy": False,
                "error": "Cache value mismatch"
            }
            
    except Exception as e:
        return {
            "healthy": False,
            "error": str(e)
        }

def check_system_resources():
    """Check system resource usage"""
    
    try:
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Memory usage
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        # Disk usage
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        
        return {
            "cpu_percent": cpu_percent,
            "memory_percent": memory_percent,
            "disk_percent": disk_percent,
            "memory_available_gb": memory.available / (1024**3),
            "disk_free_gb": disk.free / (1024**3)
        }
        
    except Exception as e:
        return {
            "error": str(e)
        }

def get_performance_metrics():
    """Get application performance metrics"""
    
    try:
        # Database connection pool info (if available)
        db_connections = getattr(connection, 'queries_logged', 0)
        
        # Cache hit rate (if available)
        cache_stats = {}
        try:
            # Try to get Redis stats
            from django_redis import get_redis_connection
            redis_conn = get_redis_connection("default")
            info = redis_conn.info()
            cache_stats = {
                "connected_clients": info.get("connected_clients", 0),
                "used_memory_human": info.get("used_memory_human", "N/A"),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0)
            }
            
            # Calculate hit rate
            hits = cache_stats["keyspace_hits"]
            misses = cache_stats["keyspace_misses"]
            if hits + misses > 0:
                cache_stats["hit_rate"] = hits / (hits + misses)
                
        except Exception:
            cache_stats = {"status": "unavailable"}
        
        return {
            "database_queries": db_connections,
            "cache_stats": cache_stats
        }
        
    except Exception as e:
        return {
            "error": str(e)
        }

def check_rate_limiting_health():
    """Check rate limiting system health"""
    
    try:
        from .rate_limiting import rate_limit_health_check
        return rate_limit_health_check()
        
    except Exception as e:
        return {
            "healthy": False,
            "error": str(e)
        }

@csrf_exempt
@require_http_methods(["GET"])
def detailed_health_check(request):
    """Detailed health check with more comprehensive information"""
    
    health_data = {
        "timestamp": time.time(),
        "application": {
            "name": "EMS Backend",
            "version": "1.0.0",
            "environment": "production" if not settings.DEBUG else "development"
        },
        "database": get_detailed_database_info(),
        "cache": get_detailed_cache_info(),
        "system": get_detailed_system_info(),
        "security": get_security_status()
    }
    
    return JsonResponse(health_data)

def get_detailed_database_info():
    """Get detailed database information"""
    
    try:
        with connection.cursor() as cursor:
            # PostgreSQL specific queries
            if connection.vendor == 'postgresql':
                cursor.execute("""
                    SELECT 
                        count(*) as active_connections,
                        (SELECT setting FROM pg_settings WHERE name = 'max_connections') as max_connections
                    FROM pg_stat_activity 
                    WHERE state = 'active'
                """)
                
                result = cursor.fetchone()
                return {
                    "vendor": "postgresql",
                    "active_connections": result[0],
                    "max_connections": result[1],
                    "connection_usage": f"{(result[0]/int(result[1]))*100:.1f}%"
                }
            else:
                return {
                    "vendor": connection.vendor,
                    "status": "connected"
                }
                
    except Exception as e:
        return {
            "error": str(e)
        }

def get_detailed_cache_info():
    """Get detailed cache information"""
    
    try:
        from django_redis import get_redis_connection
        redis_conn = get_redis_connection("default")
        info = redis_conn.info()
        
        return {
            "redis_version": info.get("redis_version"),
            "uptime_in_seconds": info.get("uptime_in_seconds"),
            "connected_clients": info.get("connected_clients"),
            "used_memory": info.get("used_memory"),
            "used_memory_human": info.get("used_memory_human"),
            "total_commands_processed": info.get("total_commands_processed"),
            "keyspace_hits": info.get("keyspace_hits"),
            "keyspace_misses": info.get("keyspace_misses")
        }
        
    except Exception as e:
        return {
            "error": str(e)
        }

def get_detailed_system_info():
    """Get detailed system information"""
    
    try:
        return {
            "cpu_count": psutil.cpu_count(),
            "cpu_percent_per_core": psutil.cpu_percent(percpu=True),
            "memory_total_gb": psutil.virtual_memory().total / (1024**3),
            "memory_available_gb": psutil.virtual_memory().available / (1024**3),
            "disk_total_gb": psutil.disk_usage('/').total / (1024**3),
            "disk_free_gb": psutil.disk_usage('/').free / (1024**3),
            "boot_time": psutil.boot_time(),
            "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
        }
        
    except Exception as e:
        return {
            "error": str(e)
        }

def get_security_status():
    """Get security configuration status"""
    
    return {
        "debug_mode": settings.DEBUG,
        "rate_limiting_enabled": getattr(settings, 'RATELIMIT_ENABLE', False),
        "cors_allowed_origins_count": len(getattr(settings, 'CORS_ALLOWED_ORIGINS', [])),
        "secure_ssl_redirect": getattr(settings, 'SECURE_SSL_REDIRECT', False),
        "session_cookie_secure": getattr(settings, 'SESSION_COOKIE_SECURE', False),
        "csrf_cookie_secure": getattr(settings, 'CSRF_COOKIE_SECURE', False)
    }
