"""
EMS PDF Views
Legacy PDF generation endpoints - now redirects to new PDF generation app
"""

from django.http import JsonResponse, HttpResponseRedirect
from django.urls import reverse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
import logging

logger = logging.getLogger(__name__)


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def generate_hr_pdf(request):
    """
    Legacy HR PDF generation endpoint
    Redirects to new PDF generation app
    """
    try:
        # Import the new PDF service
        from pdf_generation.views import generate_hr_pdf as new_generate_hr_pdf
        return new_generate_hr_pdf(request)
    except ImportError:
        return JsonResponse({
            'error': 'PDF generation service not available',
            'message': 'Please ensure pdf_generation app is properly installed'
        }, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def test_arabic_pdf(request):
    """
    Test Arabic PDF generation
    """
    try:
        from pdf_generation.services import pdf_service
        
        # Test data for Arabic PDF
        test_data = {
            'title': 'تقرير تجريبي',
            'content': 'هذا تقرير تجريبي لاختبار دعم اللغة العربية في ملفات PDF',
            'employees': [
                {'name': 'أحمد محمد', 'position': 'مدير', 'department': 'الموارد البشرية'},
                {'name': 'فاطمة علي', 'position': 'محاسبة', 'department': 'المالية'},
            ]
        }
        
        # Generate test PDF
        history = pdf_service.generate_pdf(
            template_type='hr_report_ar',
            language='ar',
            data=test_data,
            user=request.user,
            filename='test_arabic_report.pdf'
        )
        
        return pdf_service.get_pdf_response(history)
        
    except Exception as e:
        logger.error(f"Arabic PDF test failed: {str(e)}")
        return JsonResponse({
            'error': 'Arabic PDF test failed',
            'details': str(e)
        }, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def pdf_status(request):
    """
    PDF generation service status
    """
    try:
        from pdf_generation.views import pdf_status as new_pdf_status
        return new_pdf_status(request)
    except ImportError:
        return JsonResponse({
            'status': 'error',
            'error': 'PDF generation service not available'
        }, status=500)
