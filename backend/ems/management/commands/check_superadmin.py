from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from ems.models import Role, UserProfile, Employee


class Command(BaseCommand):
    help = 'Check superadmin user setup and role assignment'

    def handle(self, *args, **options):
        try:
            # Check if superadmin user exists
            user = User.objects.get(username='superadmin')
            self.stdout.write(
                self.style.SUCCESS(f'✅ Superadmin user found: {user.username}')
            )
            
            # Check user profile and role
            try:
                profile = UserProfile.objects.get(user=user)
                self.stdout.write(
                    self.style.SUCCESS(f'✅ User profile found')
                )
                self.stdout.write(f'   Role: {profile.role.name}')
                self.stdout.write(f'   Role Display: {profile.role.get_name_display()}')
                
                # Check if role is SUPERADMIN
                if profile.role.name == 'SUPERADMIN':
                    self.stdout.write(
                        self.style.SUCCESS('✅ User has SUPERADMIN role')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'⚠️  User has {profile.role.name} role, not SUPERADMIN')
                    )
                    
                    # Update to SUPERADMIN role
                    superadmin_role = Role.objects.get(name='SUPERADMIN')
                    profile.role = superadmin_role
                    profile.save()
                    self.stdout.write(
                        self.style.SUCCESS('✅ Updated user role to SUPERADMIN')
                    )
                    
            except UserProfile.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR('❌ User profile not found')
                )
                
                # Create profile with SUPERADMIN role
                superadmin_role = Role.objects.get(name='SUPERADMIN')
                profile = UserProfile.objects.create(
                    user=user,
                    role=superadmin_role,
                    preferred_language='ar',
                    timezone='Asia/Riyadh'
                )
                self.stdout.write(
                    self.style.SUCCESS('✅ Created user profile with SUPERADMIN role')
                )
            
            # Check employee record
            try:
                employee = Employee.objects.get(user=user)
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Employee record found: {employee.employee_id}')
                )
            except Employee.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING('⚠️  Employee record not found')
                )
            
            # Check all available roles
            self.stdout.write('\n📋 Available Roles:')
            for role in Role.objects.all():
                self.stdout.write(f'   - {role.name}: {role.get_name_display()}')
            
            # Check user permissions
            self.stdout.write(f'\n🔐 User Permissions:')
            self.stdout.write(f'   - Is Staff: {user.is_staff}')
            self.stdout.write(f'   - Is Superuser: {user.is_superuser}')
            self.stdout.write(f'   - Is Active: {user.is_active}')
            
            # Display login credentials
            self.stdout.write(f'\n🔑 Login Credentials:')
            self.stdout.write(f'   Username: {user.username}')
            self.stdout.write(f'   Email: {user.email}')
            self.stdout.write(f'   Password: superadmin123 (default)')
            
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('❌ Superadmin user not found')
            )
        except Role.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('❌ SUPERADMIN role not found')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error: {str(e)}')
            )
